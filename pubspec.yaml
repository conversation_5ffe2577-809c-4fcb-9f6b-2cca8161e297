name: parenthing_dashboard
description: "A new Flutter project."
publish_to: "none"

version: 1.0.4+6

environment:
  sdk: ">=3.3.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  cupertino_icons: ^1.0.6
  get: ^4.6.6
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.10+1
  http: ^1.2.1
  gap: ^3.0.1
  package_info_plus: ^8.0.0
  number_paginator: ^0.4.1
  intl: ^0.19.0
  flutter_staggered_animations: ^1.1.1
  shimmer: ^3.0.0
  provider: ^6.1.2
  bottom_navy_bar: ^6.0.0
  badges: ^3.1.2
  get_it:
  fl_chart: ^0.68.0
  flutter_bounceable: ^1.1.0
  file_picker: ^8.0.5
  pin_code_fields: ^8.0.1
  image_picker: ^1.1.1
  dropdown_button2: ^2.3.9
  dotted_border: ^2.1.0
  image: ^4.1.7
  get_storage:
  loading_indicator: ^3.1.1
  table_calendar: ^3.0.9
  url_launcher: any
  crypto: ^3.0.3
  photo_view: ^0.15.0
  calendar_date_picker2: ^1.1.5
  clipboard: ^0.1.3 
  location: ^5.0.0
  google_maps_flutter: ^2.3.0
  geolocator: ^9.0.2
  geocoding: ^3.0.0
  uuid: ^3.0.7
  permission_handler: ^10.3.0
  google_maps_flutter_web: ^0.5.8
  pie_chart: ^5.4.0
  flutter_local_notifications: ^17.2.2
  firebase_core: ^3.3.0
  firebase_messaging: ^15.0.4
  carousel_slider: ^5.0.0
  cached_network_image: ^3.3.1
  google_places_flutter: ^2.1.0
  linkfy_text: ^1.1.6
  font_awesome_flutter: 10.6.0
  firebase_analytics: ^11.0.4

  
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0


flutter:
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/svg/
    - assets/png/

  fonts:
    - family: Rubic
      fonts:
        - asset: fonts/Rubik-Bold.ttf
        - asset: fonts/Rubik-Medium.ttf
        - asset: fonts/Rubik-Regular.ttf
        - asset: fonts/Rubik-SemiBold.ttf

  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
