# Parenthing Dashboard

A responsive Flutter web application designed to help parents raise happy, successful children through tools, tracking, and communication.

## 🚀 Getting Started

This project is a Flutter web dashboard. Follow the steps below to get started with development or deployment.

### ✅ Prerequisites

- Flutter SDK >= 3.3.3 < 4.0.0
- Dart SDK (comes with Flutter)
- Web-compatible browser (e.g. Chrome)
- Node/npm (optional for CI/CD or web hosting)

### 🔧 Installation

1. **Clone the repository**  
   ```bash
   git clone https://github.com/your-username/parenthing_dashboard.git
   cd parenthing_dashboard


2. Install dependencies
    flutter pub get

3. Run the app in development mode
    flutter run -d chrome

## 📦 Building for Web
To build the web app and include versioning for cache-busting:

    flutter build web --release
    ## Generate version.txt based on pubspec.yaml version
    version=$(grep '^version:' pubspec.yaml | awk '{print $2}')
    echo "$version" > build/web/version.txt

This creates a version.txt file in the build/web/ directory, which helps control caching and update the service worker only when the app version changes.

## ✨ Features
    ⚡ Flutter web-optimized performance
    🎨 Responsive UI for desktop and mobile
    🔒 Firebase push notifications (via service workers)
    🗺️ Google Maps and location integration
    🧠 State management with GetX and Provider
    📦 Package versioning and cache control using version.txt