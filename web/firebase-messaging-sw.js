// Please see this file for the latest firebase-js-sdk version:
// https://github.com/firebase/flutterfire/blob/master/packages/firebase_core/firebase_core_web/lib/src/firebase_sdk_version.dart
importScripts("https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js");

firebase.initializeApp({
    apiKey: "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs",
    authDomain: "parenthing-app.firebaseapp.com",
    projectId: "parenthing-app",
    storageBucket: "parenthing-app.appspot.com",
    messagingSenderId: "272995004120",
    appId: "1:272995004120:web:d1f1c64c8ba5c5c61e31ad",
    measurementId: "G-4WP7XZZJZ3"
});

const messaging = firebase.messaging();

// Optional:
messaging.onBackgroundMessage((payload) => {
  console.log('Received background message ', payload);
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icons/icon-192x192.png' // Adjust the icon path
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});