import 'dart:convert';
import 'dart:developer';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/firebase/firebase_notifications.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/model/business_graph_model.dart';
import 'package:parenthing_dashboard/model/homepage_images_model.dart';
import 'package:parenthing_dashboard/model/homepage_mobile_banner_model.dart';
import 'package:parenthing_dashboard/model/kyc_details_model.dart';
import 'package:parenthing_dashboard/model/login/login_model.dart';
import 'package:parenthing_dashboard/model/notifications_model.dart';
import 'package:parenthing_dashboard/model/user_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';
import 'package:parenthing_dashboard/view/landing_page/admin_loading_page.dart';
import 'package:parenthing_dashboard/view/landing_page/landing_page.dart';

class UserController extends GetxController {
  var isDialogShown = false.obs;
  RxBool isChangeNumberClicked = false.obs;
  String userID = "0";
  RxBool isAddressCreated = false.obs;
  RxBool isAddressEdited = false.obs;
  final phoneController = TextEditingController().obs;
  final otpController = TextEditingController().obs;
  RxBool isImageUploaded = false.obs;
  RxBool isImageLoading = false.obs;
  var homePageImageModel = <HomePageImageModel>[].obs;
  RxBool isMobileImageLoading = false.obs;
  var homePageMobileBannerModel = <HomePageMobileBanner>[].obs;
  // RxBool isLoading = false.obs;
  RxBool isSendOTPLoading = false.obs;
  var loginModel = LoginModel().obs;
  var userModel = UserModel(location: []).obs;
  RxBool isKycDetailsLoading = true.obs;
  var kycDetailsModel = KycDetailsModel().obs;
  var homePageGraph = BusinessGraphModel().obs;
  RxBool isBusinessDataLoading = true.obs;
  RxInt unreadNotificationsCount = 0.obs;
  var notificationsList = BusinessNotificationListModel(notifications: []).obs;
  RxBool isNotificationsLoading = true.obs;

  late final BusinessController businessController;
  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    businessController = Get.find<BusinessController>();
    super.onInit();
  }

  Future sendOtpAPI({required String phone}) async {
    isSendOTPLoading.value = true;

    Map<String, dynamic> payload = {"mobile": phone};
    try {
      final response = await apiController.apiHelperFn(
        apiUrl: AppUrl.sendOTPUrl,
        payload: payload,
      );
      if (kDebugMode) {
        print("send Otp :: ${json.encode(response)}");
      }
      final jsonMap = json.decode(response);
      if (jsonMap["success"] == true) {
        isSendOTPLoading.value = false;
        CustomSnackBar.showSuccessSnackbar("OTP Sent",
            "Verification code has been sent to your mobile number");
        return true;
      } else {
//     showOkAlertDialog(context: Get.context!,
//   title: "Error",
//   message: "Something went wrong. Please try again.",
// );
        CustomSnackBar.showErrorSnackbar("Unregistered Mobile Number",
            jsonMap["msg"] ?? "Failed to send OTP. Please try again.");
        isSendOTPLoading.value = false;
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      isSendOTPLoading.value = false;
      return false;
    }
  }

  Future verifyOtpAPI({required String phone, required String otp}) async {
    isSendOTPLoading.value = true;
    Map<String, String> payload = {
      "mobile": phone,
      "otp": otp,
    };
    try {
      final response = await apiController.apiHelperFn(
        apiUrl: AppUrl.verifyOTPUrl,
        payload: payload,
      );

      final jsonMap = json.decode(response);
      if (response != null) {
        if (jsonMap['success'] == true) {
          storage.write("isLogin", "true");
          storage.write('roleID', jsonMap['data']['role_id'].toString());
          storage.write('AUTH_TOKEN', jsonMap['data']['token'].toString());
          storage.write('USER_ID', jsonMap['data']['id'].toString());
          storage.write('USER_NAME', jsonMap['data']['name'].toString());
          businessController.userName.value =
              jsonMap['data']['name'].toString();
          businessController.userProfilePic.value =
              jsonMap['data']['profile_picture_url'].toString();
          storage.write(
              "PROFILE_PIC", jsonMap['data']['profile_picture_url'].toString());
          loginModel.value = LoginModel.fromJson(jsonMap['data']);

          String? deviceToken = await FirebaseService.getDeviceToken();
          log("Device otp page Token: $deviceToken");
          if (deviceToken != null) {
            bool tokenSaved = await saveDeviceToken(deviceToken);
            log("Token saved: $tokenSaved");
          } else {
            log("Failed to get device token");
          }
          phoneController.value.clear();
          otpController.value.clear();
          if (jsonMap['data']['role_id'] == 2) {
            locator<NavigationServices>().navigateToReplacement(
              const AdminLoadingAnimationPage(),
            );
          } else {
            locator<NavigationServices>().navigateToReplacement(
              const LoadingAnimationPage(),
            );
          }
          isSendOTPLoading.value = false;
          return true;
        } else {
          storage.write("isLogin", "false");
          isSendOTPLoading.value = false;

          return false;
        }
      } else {
        storage.write("isLogin", "false");
        isSendOTPLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      storage.write("isLogin", "false");
      if (kDebugMode) {
        print('$e \n$stackTrace');
      }
      isSendOTPLoading.value = false;
      return false;
    }
  }

  Future<bool> saveDeviceToken(String firebaseToken) async {
    try {
      String businessID = await storage.read("USER_ID");

      Map<String, dynamic> payload = {
        "business_id": businessID,
        "user_firebase_token": firebaseToken,
        "device_type": "web"
      };
      if (kDebugMode) {
        print("Firebase Payload :::=> $payload");
      }

      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.saveDeviceTokenUrl, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        log("Firebase token saved successfully");
        return true;
      } else {
        log("Failed to save Firebase token");
        return false;
      }
    } catch (e) {
      log("Error saving device token: $e");
      return false;
    }
  }

  void getHomePageImages() async {
    isImageLoading.value = true;
    try {
      Map<String, dynamic> payload = {
        "banner_type": "dashboard",
      };
      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.homePageImageUrl, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap['data'] != null) {
          final data = jsonMap['data'];
          homePageImageModel.value = data
              .map<HomePageImageModel>((e) => HomePageImageModel.fromJson(e))
              .toList();
          // Future.delayed(const Duration(milliseconds: 300)).then((value) {
          //   isImageLoading.value = false;
          // });
        } else {
          log("Invalid data format");
          // Future.delayed(const Duration(milliseconds: 300)).then((value) {
          //   isImageLoading.value = false;
          // });
        }
      } else {
        log("API response not successful: ${value?['msg']}");
        // Future.delayed(const Duration(milliseconds: 300)).then((value) {
        //   isImageLoading.value = false;
        // });
      }
    } catch (error, stackTrace) {
      // Future.delayed(const Duration(milliseconds: 300)).then((value) {
      //   isImageLoading.value = false;
      // });
      if (kDebugMode) {
        print("getHomePageImages error: $error \n$stackTrace");
      }
    } finally {
      Future.delayed(const Duration(milliseconds: 300)).then((value) {
        isImageLoading.value = false;
      });
    }
  }

// to dispose the image model after use
  void clearAdminImageData() {
    isImageLoading.value = true;
    isMobileImageLoading.value = true;
    homePageImageModel.clear();
    homePageMobileBannerModel.clear();
    isImageLoading.value = false;
    isMobileImageLoading.value = false;
  }

  void getMobileHomePageImages() async {
    isMobileImageLoading.value = true;
    try {
      Map<String, dynamic> payload = {
        "banner_type": "mobile",
      };
      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.homePageImageUrl, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          homePageMobileBannerModel.value = data
              .map<HomePageMobileBanner>(
                  (e) => HomePageMobileBanner.fromJson(e))
              .toList();
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isMobileImageLoading.value = false;
          });
        } else {
          log("Invalid data format");
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isMobileImageLoading.value = false;
          });
        }
      } else {
        log("API response not successful: ${value?['msg']}");
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isMobileImageLoading.value = false;
        });
      }
    } catch (error, stackTrace) {
      isMobileImageLoading.value = false;
      if (kDebugMode) {
        print("getMobileHomePageImages error: $error \n$stackTrace");
      }
    }
  }

  void getUserKycDetails() async {
    userID = storage.read("USER_ID") ?? "0";
    isKycDetailsLoading.value = true;
    try {
      Map<String, dynamic> payload = {
        "business_id": int.parse(userID),
      };
      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.kycDetailsApi, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          kycDetailsModel.value = KycDetailsModel.fromJson(data);
          isKycDetailsLoading.value = false;
        } else {
          log("Invalid data format");
          isKycDetailsLoading.value = false;
        }
      } else {
        log("API response not successful: ${value?['msg']}");
        isKycDetailsLoading.value = false;
      }
    } catch (error, stackTrace) {
      isKycDetailsLoading.value = false;
      if (kDebugMode) {
        print("getUserKycDetails error: $error \n$stackTrace");
      }
    }
  }

  void getBusinessGraphData() async {
    userID = storage.read("USER_ID") ?? "0";
    isBusinessDataLoading.value = true;
    try {
      Map<String, dynamic> payload = {
        "business_id": int.parse(userID),
      };
      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.homePageGraphUrl, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          homePageGraph.value = BusinessGraphModel.fromJson(data);
          isBusinessDataLoading.value = false;
        } else {
          homePageGraph.value = BusinessGraphModel();
          log("Invalid data format");
          isBusinessDataLoading.value = false;
        }
      } else {
        homePageGraph.value = BusinessGraphModel();
        log("API response not successful: ${value?['msg']}");
        isBusinessDataLoading.value = false;
      }
    } catch (error, stackTrace) {
      homePageGraph.value = BusinessGraphModel();
      isBusinessDataLoading.value = false;
      if (kDebugMode) {
        print("homePage graph data error: $error \n$stackTrace");
      }
    }
  }

  //! create address fun
  Future<bool> createAddress(Map<String, dynamic> addressPayload) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Loader.showLoading();
      isAddressCreated.value = true;

      Map<String, dynamic> payload = {
        "business_id": userID,
        "location_details": addressPayload
      };

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.addLocationUrl,
        payload: payload,
      );
      var jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        Loader.hideLoading();
        isAddressCreated.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isAddressCreated.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isAddressCreated.value = false;
      if (kDebugMode) {
        print("fun createAddress $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! edit address fun
  Future<bool> editAddress(Map<String, dynamic> addressPayload) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Loader.showLoading();
      isAddressEdited.value = true;
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.editLocationUrl,
        payload: addressPayload,
      );
      var jsonMap = json.decode(response);
      if (kDebugMode) {
        print('edit location api ${json.encode(jsonMap)}');
      }
      if (jsonMap['success'] == true) {
        if (kDebugMode) {
          print('edit location called success');
        }
        Loader.hideLoading();
        isAddressEdited.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isAddressEdited.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isAddressEdited.value = false;
      if (kDebugMode) {
        print("fun createAddress $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> removeSavedDeviceToken() async {
    try {
      String businessID = await storage.read("USER_ID");

      Map<String, dynamic> payload = {
        "business_id": businessID,
      };
      if (kDebugMode) {
        print("removeSavedDeviceToken Payload :::=> $payload");
      }

      final value = await apiController.apiHelperFn(
          apiUrl: AppUrl.removeSavedDeviceTokenUrl, payload: payload);
      final jsonMap = json.decode(value);
      if (jsonMap["success"] == true) {
        log("Firebase token removed successfully");
        return true;
      } else {
        log("Failed to remove the Firebase token");
        return false;
      }
    } catch (e) {
      log("Error removing in device token: $e");
      return false;
    }
  }

  //! to get all event list fun
  Future<bool> getAllNotificationsList() async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "business_id": int.parse(userID),
      };
      log("get notification payload $payload");
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.businessNotificationsUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          final notificationResponse =
              BusinessNotificationListModel.fromJson(data);
          notificationsList.value = notificationResponse;

          unreadNotificationsCount.value = notificationsList.value.notifications
              .where((notification) => notification.isRead == 0)
              .length;
          isNotificationsLoading.value = false;
          return true;
        } else {
          notificationsList.value =
              BusinessNotificationListModel(notifications: []);
          unreadNotificationsCount.value = 0;
          isNotificationsLoading.value = false;
          return false;
        }
      } else {
        notificationsList.value =
            BusinessNotificationListModel(notifications: []);
        unreadNotificationsCount.value = 0;
        isNotificationsLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      log("Business notifications list api call error: $e");
      log("Stack trace: $stackTrace");
      notificationsList.value =
          BusinessNotificationListModel(notifications: []);
      unreadNotificationsCount.value = 0;
      isNotificationsLoading.value = false;
      if (kDebugMode) {
        print("Business notifications list api call $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> markNotificationAsRead({required int notificationId}) async {
    try {
      String businessID = await storage.read("USER_ID");
      Map<String, dynamic> payload = {
        "business_id": businessID,
        "notification_id": notificationId,
        // "is_close": 1,
        "is_read": 1
      };

      log("update notification payload $payload");

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.businessUpdateNotificationsUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        int index = notificationsList.value.notifications
            .indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          notificationsList.value.notifications[index].isRead = 1;
          notificationsList.refresh();
          // Update unread count
          unreadNotificationsCount.value = notificationsList.value.notifications
              .where((notification) => notification.isRead == 0)
              .length;
        }
        return true;
      }
      return false;
    } catch (e) {
      log('Error marking notification as read: $e');
      return false;
    }
  }
}
