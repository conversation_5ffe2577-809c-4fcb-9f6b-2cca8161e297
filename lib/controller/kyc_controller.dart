import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';

class KycController extends GetxController {
  TextEditingController businessName = TextEditingController();
  TextEditingController registeredAddress = TextEditingController();
  TextEditingController docNumber = TextEditingController();
  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  Future<bool> kycSubmitApi(Map<String, dynamic> payload) async {
    try {
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.kycSubmit,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
