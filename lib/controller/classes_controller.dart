import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:parenthing_dashboard/model/class/category_model.dart';
import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';


class ClassController extends GetxController {
  String fileName = "ClassController";
  RxBool isClassCreateLoading = true.obs;
  RxBool isClassEditLoading = false.obs;
  RxBool isClassListLoading = true.obs;
  RxBool isClassDetailsLoading = true.obs;
  RxBool isClassDeleteLoading = true.obs;
  var classList = <ClassModel>[].obs;
  var classDetailsModel = ClassModel(locationDetails: LocationDetails()).obs;
//  RxBool classesCategoryListLoading = true.obs;
  var classCategoryList = <ClassCategoryModel>[].obs;
  var classSubCategories = <Subcategory>[].obs;
  var isLoading = false.obs;
  RxString selectedClassCategoryName = "".obs;
  RxString selectedSubClassCategoryName = "".obs;
  final storage = GetStorage();
  String userID = "0";
  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  List<String> serviceableCity = ['Mumbai', 'Pune', 'Hyderabad', 'Bangalore'];
  List<String> serviceableAdd = [
    'Mumbai Branch',
    'Pune Branch',
    'Hyderabad Branch',
    'Bangalore Branch'
  ];

  void resetClassDetailsModel() {
    classDetailsModel.value = ClassModel(locationDetails: LocationDetails());
  }

  Future<bool> getAllClassList(String type) async {
    isClassListLoading.value = true;
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "business_id": int.parse(userID),
        "type": type
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.classListUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          List<ClassModel> newList =
              data.map<ClassModel>((e) => ClassModel.fromJson(e)).toList();
          classList.value = newList;
          isClassListLoading.value = false;
          return true;
        } else {
          classList.value = [];
          isClassListLoading.value = false;
          return false;
        }
      } else {
        classList.value = [];
        isClassListLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      classList.value = [];
      isClassListLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun getAllClassDetails $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> createClass(
      {required int publish,
      required String status,
      required ClassModel createClassModel}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Loader.showLoading();
      isClassCreateLoading.value = true;
      log("case 1");
      createClassModel.businessId = userID;
      createClassModel.classId = 0;
      createClassModel.publish = publish;
      createClassModel.status = status;

      var payload = createClassModel;
      if (kDebugMode) {
        print("Create class Payload:::=> ${json.encode(payload)}");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.createClassUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        log("case 3");
        log("class Created Success");
        Loader.hideLoading();
        isClassCreateLoading.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isClassCreateLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print("$fileName=>fun createClass $e \n  $stackTrace");
      }
      Loader.hideLoading();
      isClassCreateLoading.value = false;
      return false;
    }
  }

  //! to get single event details by id
  Future<bool> getClassDetailsData(int classByID) async {
    try {
      isClassDetailsLoading.value = true;
      Map<String, dynamic> payload = {
        "class_id": classByID,
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.classDetailsUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          classDetailsModel.value = ClassModel.fromJson(jsonMap['data']);

          return true;
        } else {
          classDetailsModel.value =
              ClassModel(locationDetails: LocationDetails());
          isClassDetailsLoading.value = false;
          return false;
        }
      } else {
        classDetailsModel.value =
            ClassModel(locationDetails: LocationDetails());
        isClassDetailsLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      classDetailsModel.value = ClassModel(locationDetails: LocationDetails());
      isClassDetailsLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun classDetails $e \n  $stackTrace");
      }
      return false;
    } finally {
      await Future.delayed(const Duration(milliseconds: 300));
      isClassDetailsLoading.value = false;
    }
  }

  // edit
  Future<bool> editClass(
      {required ClassModel editClassModel,
      required int publish,
      required String status,
      required int isApproved}) async {
    Loader.showLoading();

    isClassEditLoading.value = true;
    try {
      userID = storage.read("USER_ID") ?? "0";
      log("case 1");
      editClassModel.businessId = userID;
      editClassModel.publish = publish;
      editClassModel.status = status;
      editClassModel.isApproved = isApproved;
      var payload = editClassModel;
      log("Payload: ${json.encode(payload)}");
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.editClassUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        log("Class Edited Successfully");
        await Future.delayed(const Duration(milliseconds: 300)).then((value) {
          Loader.hideLoading();
          isClassEditLoading.value = false;
        });

        return true;
      } else {
        classDetailsModel.value =
            ClassModel(locationDetails: LocationDetails());
        Loader.hideLoading();

        isClassEditLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();

      isClassEditLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>Edit Class $e \n  $stackTrace");
      }
      return false;
    }
  }

  void updateSubCategories(String categoryName) {
    var selectedCategory = classCategoryList
        .firstWhere((category) => category.name == categoryName);
    classSubCategories.value = selectedCategory.subcategories;
    selectedSubClassCategoryName.value = '';
  }

  Future<bool> getClassCategoryListData() async {
    try {
      final value = await apiController.getApi(AppUrl.classCategoryApi);
      if (value['success'] == true) {
        log('✅ getClassCategoryListData Success Response: ${value.toString()}');
        final classCategoryModel = value['data'];
        classCategoryList.value = classCategoryModel
            .map<ClassCategoryModel>((e) => ClassCategoryModel.fromJson(e))
            .toList();
        return true;
      } else {
        log('❌ getClassCategoryListData Failed Response: ${value.toString()}');
        classCategoryList.value = [];
        return false;
      }
    } catch (error, stackTrace) {
      classCategoryList.value = [];
      log('getClassCategoryListData: $error \n$stackTrace');
      return false;
    }
  }

  Future<bool> getClassDeleteData(int classID) async {
    try {
      // Loader.showLoading();
      isClassDeleteLoading.value = true;
      Map<String, dynamic> payload = {"class_id": classID};
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.deleteClassUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        log("list fetched success");
        // Loader.hideLoading();
        isClassDeleteLoading.value = false;
        return true;
      } else {
        // Loader.hideLoading();
        isClassDeleteLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      // Loader.hideLoading();
      isClassDeleteLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun deleteClass $e \n  $stackTrace");
      }
      return false;
    }
  }
}
