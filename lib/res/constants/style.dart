import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:parenthing_dashboard/res/app_color.dart';

TextStyle rubikStyle = GoogleFonts.rubik();

//! heading text style
TextStyle headingTextBold = GoogleFonts.rubik(
  fontSize: 32.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle headingTextSemiBold = GoogleFonts.rubik(
  fontSize: 32.0,
  fontWeight: Get.width <= 820 ? FontWeight.w500 : FontWeight.w600,
  color: AppColors.txtprimary,
);

//! H2 text style
TextStyle heading2TextMedium = GoogleFonts.rubik(
  fontSize: 24.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle heading2TextRegular = GoogleFonts.rubik(
  fontSize: 24.0,
  fontWeight: FontWeight.w600,
  color: AppColors.txtprimary,
);

//! title text style
TextStyle titleTextBold = GoogleFonts.rubik(
  fontSize: 22.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle titleTextSemiBold = GoogleFonts.rubik(
  fontSize: 22.0,
  fontWeight: Get.width <= 820 ? FontWeight.w500 : FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle titleTextMedium = GoogleFonts.rubik(
  fontSize: 22.0,
  fontWeight: Get.width <= 820 ? FontWeight.w400 : FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle titleTextRegular = GoogleFonts.rubik(
  fontSize: 22.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! title 2 text style
TextStyle title2TextBold = GoogleFonts.rubik(
  fontSize: 20.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle title2TextSemiBold = GoogleFonts.rubik(
  fontSize: 20.0,
  fontWeight: FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle title2TextMedium = GoogleFonts.rubik(
  fontSize: 20.0,
  fontWeight: FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle title2TextRegular = GoogleFonts.rubik(
  fontSize: 20.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! title 3 text style
TextStyle title3TextBold = GoogleFonts.rubik(
  fontSize: 18.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle title3TextSemiBold = GoogleFonts.rubik(
  fontSize: 18.0,
  fontWeight: Get.width <= 820 ? FontWeight.w500 : FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle title3TxtSemiBoldprimary = GoogleFonts.rubik(
  fontSize: 18.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.kprimarycolor,
);

TextStyle title3TextMedium = GoogleFonts.rubik(
  fontSize: 18.0,
  fontWeight: FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle title3TextRegular = GoogleFonts.rubik(
  fontSize: 18.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! body1 text style
TextStyle bodyTextBold = GoogleFonts.rubik(
  fontSize: 16.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w700,
  color: AppColors.txtprimary,
);

TextStyle bodyTextSemiBold = GoogleFonts.rubik(
  fontSize: 16.0,
  fontWeight: FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle bodyTextMedium = GoogleFonts.rubik(
  fontSize: 16.0,
  fontWeight: FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle bodyTextRegular = GoogleFonts.rubik(
  fontSize: 16.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! body2 text style
TextStyle body2TextBold = GoogleFonts.rubik(
  fontSize: 14.0,
  fontWeight: Get.width <= 820 ? FontWeight.w600 : FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle body2TextSemiBold = GoogleFonts.rubik(
  fontSize: 14.0,
  fontWeight: FontWeight.w600,
  color: AppColors.txtprimary,
);

TextStyle body2TextMedium = GoogleFonts.rubik(
  fontSize: 14.0,
  fontWeight: FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle body2TextRegular = GoogleFonts.rubik(
  fontSize: 14.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! body3 text style
TextStyle body3TextMedium = GoogleFonts.rubik(
  fontSize: 12.0,
  fontWeight: FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle body3TextRegular = GoogleFonts.rubik(
  fontSize: 12.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

//! Caption text style
TextStyle captionTextMedium = GoogleFonts.rubik(
  fontSize: 10.0,
  fontWeight: Get.width <= 820 ? FontWeight.w400 : FontWeight.w500,
  color: AppColors.txtprimary,
);

TextStyle captionTextRegular = GoogleFonts.rubik(
  fontSize: 10.0,
  fontWeight: FontWeight.w400,
  color: AppColors.txtprimary,
);

TextStyle dropdownTextStyle = GoogleFonts.rubik(
  color: AppColors.detailgrey,
  fontWeight: FontWeight.w400,
  fontSize: 15.0,
);
