import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class SideBardButton extends StatelessWidget {
  const SideBardButton({
    super.key,
    required this.buttonText,
    required this.buttonIcon,
    required this.onClick,
    required this.isSelected,
    required this.selectedIcon,
  });

  final String buttonText;
  final String buttonIcon;
  final VoidCallback onClick;
  final String selectedIcon;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 5.0),
      child: Container(
        height: 48,
        width: Get.width,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.scaffoldColor : AppColors.kwhite,
          borderRadius: BorderRadius.circular(4),
        ),
        child: ListTile(
          selected: true,
          tileColor: AppColors.kwhite,
          onTap: onClick,
          title: Text(
            buttonText,
            style: bodyTextRegular.copyWith(
                color:
                    isSelected ? AppColors.kprimarycolor : AppColors.ktertiary),
          ),
          leading: SvgPicture.asset(isSelected ? selectedIcon : buttonIcon,
              height: 22.0),
        ),
      ),
    );
  }
}
