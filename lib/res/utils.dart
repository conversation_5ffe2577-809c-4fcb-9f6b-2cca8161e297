import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

class Utils {
 
static Future<void> launchURL(String urlString, {bool isPhone = false}) async {
  // Ensure URL has proper scheme for web URLs
  String processedUrl = urlString;
  if (!isPhone && !urlString.startsWith('http://') && !urlString.startsWith('https://')) {
    processedUrl = 'https://$urlString';
  }
  
  final Uri url = Uri.parse(processedUrl);
  final Uri launchPhone = Uri(
    scheme: 'tel',
    path: urlString,
  );
  final Uri targetUri = isPhone ? launchPhone : url;

  if (!await canLaunchUrl(targetUri)) {
  
    if (kIsWeb && !isPhone) {
      try {
        await launchUrl(
          targetUri,
          mode: LaunchMode.externalApplication,
          webOnlyWindowName: '_blank',
        );
        return;
      } catch (e) {
        throw Exception('Could not launch $targetUri: $e');
      }
    }
    throw Exception('Could not launch $targetUri');
  }

  // Use different launch modes based on platform
  if (kIsWeb) {
    // For web, force external browser
    await launchUrl(
      targetUri,
      mode: LaunchMode.externalApplication,
      webOnlyWindowName: '_blank',
    );
  } else {
    // For mobile platforms
    await launchUrl(
      targetUri, 
      mode: LaunchMode.externalApplication,
    );
  }
}
}
