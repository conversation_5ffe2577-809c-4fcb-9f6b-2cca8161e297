// To parse this JSON data, do
//
//     final adminModel = adminModelFromJson(jsonString);

import 'dart:convert';

AdminModel adminModelFromJson(String str) =>
    AdminModel.fromJson(json.decode(str));

String adminModelToJson(AdminModel data) => json.encode(data.toJson());

class AdminModel {
  int adminId;
  String name;
  String mobile;
  String profilePictureUrl;
  int isNewuser;
  int roleId;
  String position;

  AdminModel({
    this.adminId = 0,
    this.name = "",
    this.mobile = "",
    this.profilePictureUrl = "",
    this.isNewuser = 0,
    this.roleId = 0,
    this.position = "",
  });

  factory AdminModel.fromJson(Map<String, dynamic> json) => AdminModel(
        adminId: json["admin_id"] ?? 0,
        name: json["name"] ?? "",
        mobile: json["mobile"] ?? "",
        profilePictureUrl: json["profile_picture_url"] ?? "",
        isNewuser: json["is_newuser"] ?? 0,
        roleId: json["role_id"] ?? 0,
        position: json["position"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "admin_id": adminId,
        "name": name,
        "mobile": mobile,
        "profile_picture_url": profilePictureUrl,
        "is_newuser": isNewuser,
        "role_id": roleId,
        "position": position,
      };
}
