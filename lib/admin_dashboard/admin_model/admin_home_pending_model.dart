// To parse this JSON data, do
//
//     final pendingReviewModel = pendingReviewModelFromJson(jsonString);

import 'dart:convert';

PendingReviewModel pendingReviewModelFromJson(String str) =>
    PendingReviewModel.fromJson(json.decode(str));

String pendingReviewModelToJson(PendingReviewModel data) =>
    json.encode(data.toJson());

class PendingReviewModel {
  int classesCreated;
  int eventsCreated;
  int verifiedBusiness;
  int activeBusinesses;
  int activeProfiles;
  int kycPendingCount;
  int eventReviewPending;
  int classesReviewPending;
  int reportedEvents;
  int reportedClasses;
  int reportedProfiles;

  PendingReviewModel({
     this.classesCreated = 0,
     this.eventsCreated = 0,
     this.verifiedBusiness = 0,
     this.activeBusinesses = 0,
     this.activeProfiles = 0,
     this.kycPendingCount = 0,
     this.eventReviewPending = 0,
     this.classesReviewPending = 0,
     this.reportedEvents = 0,
     this.reportedClasses = 0,
     this.reportedProfiles = 0,
  });

  factory PendingReviewModel.fromJson(Map<String, dynamic> json) =>
      PendingReviewModel(
        classesCreated: json["classes_created"] ?? 0,
        eventsCreated: json["events_created"] ?? 0,
        verifiedBusiness: json["verified_business"] ?? 0,
        activeBusinesses: json["active_businesses"] ?? 0,
        activeProfiles: json["active_profiles"] ?? 0,
        kycPendingCount: json["kyc_pending_count"] ?? 0,
        eventReviewPending: json["event_review_pending"] ?? 0,
        classesReviewPending: json["classes_review_pending"] ?? 0,
        reportedEvents: json["reported_events"] ?? 0,
        reportedClasses: json["reported_classes"] ?? 0,
        reportedProfiles: json["reported_profiles"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "classes_created": classesCreated,
        "events_created": eventsCreated,
        "verified_business": verifiedBusiness,
        "active_businesses": activeBusinesses,
        "active_profiles": activeProfiles,
        "kyc_pending_count": kycPendingCount,
        "event_review_pending": eventReviewPending,
        "classes_review_pending": classesReviewPending,
        "reported_events": reportedEvents,
        "reported_classes": reportedClasses,
        "reported_profiles": reportedProfiles,
      };
}
