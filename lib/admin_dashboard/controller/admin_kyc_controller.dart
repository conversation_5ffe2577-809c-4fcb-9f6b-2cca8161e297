import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';

class AdminKycController extends GetxController {
  String fileName = "AdminKycController";

  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  //! to delete event details by id
  Future<bool> updateBusinessKyc() async {
    try {
      Map<String, dynamic> payload = {
        "admin_id": 1,
        "business_id": 1,
        "status": "approved/rejected",
        "reason": "Data verified by <PERSON><PERSON><PERSON>"
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.verifyKycApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        return true;
      } else {
        return false;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }
}
