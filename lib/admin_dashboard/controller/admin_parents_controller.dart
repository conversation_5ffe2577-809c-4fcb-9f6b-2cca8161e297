import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_business_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/parents/in_active_parent_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/parents/parent_details_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/parents/parents_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/parents/report_parent_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:url_launcher/url_launcher.dart';

class AdminParentsModelController extends GetxController {
  RxBool isParentLoading = true.obs;
  RxBool isInActiveParentLoading = true.obs;
  RxBool isRepotedParentLoading = true.obs;
  RxBool isParentDetailsLoading = true.obs;
  RxBool isParentSuspendLoading = true.obs;
  RxBool isParentAppealLoading = true.obs;
  var parentList = <AdminParentsModel>[].obs;
  var parentListTotalModel = <AdminBusinessListTotal>[].obs;
  var inActiveParentModel = <InActiveParentModel>[].obs;
  var reportParentList = <ReportedParentModel>[].obs;
  var parentsDetailsModel = ParentDetailsModel(
      children: [],
      latestAppeal: LatestAppeal(),
      activityLog: [],
      reportDetails: []).obs;
  final storage = GetStorage();
  String userID = "0";

  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  Future<bool> getAllParentList(
      {String startDate = "",
      String endDate = "",
      String search = "",
      int page = 0,
      String? action,
      String city = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "page": page,
      };

      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (search.isNotEmpty) payload["search"] = search;
      if (city.isNotEmpty) payload["city"] = city;
      if (action != null) payload["action"] = action;

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminParentList,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            parentListTotalModel.value = [];
            parentList.value = data
                .map<AdminParentsModel>((e) => AdminParentsModel.fromJson(e))
                .toList();
            parentListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isParentLoading.value = false;
            });
            return true;
          } else {
            parentList.value = [];
            parentListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isParentLoading.value = false;
            });
            return false;
          }
        }
      } else {
        parentList.value = [];
        parentListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isParentLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      parentList.value = [];
      parentListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isParentLoading.value = false;
      });
      if (kDebugMode) {
        print("=>fun getAllParentList  $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAllInActiveParentList(
      {String startDate = "",
      String endDate = "",
      String search = "",
      int page = 0,
      String? action,
      String city = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "page": page,
        "status": "disabled"
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (search.isNotEmpty) payload["search"] = search;
      if (city.isNotEmpty) payload["city"] = city;
      if (action != null) payload["action"] = action;

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminInParentList,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            parentListTotalModel.value = [];
            inActiveParentModel.value = data
                .map<InActiveParentModel>(
                    (e) => InActiveParentModel.fromJson(e))
                .toList();
            parentListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isInActiveParentLoading.value = false;
            });
            return true;
          } else {
            inActiveParentModel.value = [];
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isInActiveParentLoading.value = false;
            });
            return false;
          }
        }
      } else {
        inActiveParentModel.value = [];
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isInActiveParentLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      inActiveParentModel.value = [];
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isInActiveParentLoading.value = false;
      });
      if (kDebugMode) {
        print("=>fun getAllInActiveParentList  $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAllReportedParentList(
      {String startDate = "",
      int page = 0,
      String endDate = "",
      String search = "",
      String? action,
      String city = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "page": page,
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (search.isNotEmpty) payload["search"] = search;
      if (city.isNotEmpty) payload["city"] = city;
      if (action != null) payload["action"] = action;

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminReportedParentList,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            parentListTotalModel.value = [];
            reportParentList.value = data
                .map<ReportedParentModel>(
                    (e) => ReportedParentModel.fromJson(e))
                .toList();
            parentListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isRepotedParentLoading.value = false;
            });
            return true;
          } else {
            reportParentList.value = [];
            parentListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 300)).then((_) {
              isRepotedParentLoading.value = false;
            });
            return false;
          }
        }
      } else {
        reportParentList.value = [];
        parentListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isRepotedParentLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      reportParentList.value = [];
      parentListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isRepotedParentLoading.value = false;
      });
      if (kDebugMode) {
        print("=>fun getAllInActiveParentList  $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminParentDetailsData(int parentId) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "parent_id": parentId
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminParentDetails,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          parentsDetailsModel.value =
              ParentDetailsModel.fromJson(jsonMap['data']);
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isParentDetailsLoading.value = false;
          });
          return true;
        } else {
          parentsDetailsModel.value = ParentDetailsModel(
              children: [],
              latestAppeal: LatestAppeal(),
              activityLog: [],
              reportDetails: []);
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isParentDetailsLoading.value = false;
          });
          return false;
        }
      } else {
        parentsDetailsModel.value = ParentDetailsModel(
            children: [],
            latestAppeal: LatestAppeal(),
            activityLog: [],
            reportDetails: []);
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isParentDetailsLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      parentsDetailsModel.value = ParentDetailsModel(
        latestAppeal: LatestAppeal(),
        reportDetails: [],
        activityLog: [],
        children: [],
      );
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isParentDetailsLoading.value = false;
      });
      if (kDebugMode) {
        print("parent details $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the parent model after use
  void clearAdminParentData() {
    isParentDetailsLoading.value = true;
    parentsDetailsModel.value = ParentDetailsModel(
      latestAppeal: LatestAppeal(),
      reportDetails: [],
      activityLog: [],
      children: [],
    );
  }

  Future<bool> suspendParents(int parentId, String reason, String type) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "parent_id": parentId,
        "reason": reason,
        "type": type
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.suspendParentUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null) {
          isParentSuspendLoading.value = false;
          return true;
        } else {
          isParentSuspendLoading.value = false;
          return false;
        }
      } else {
        isParentSuspendLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isParentSuspendLoading.value = false;
      if (kDebugMode) {
        print("parent details $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> appealParentsApi(
      int parentId, String action, String reason) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "parent_id": parentId,
        "action": action,
        "message": reason,
      };
      if (kDebugMode) {
        print("appeal Parent Api Paylaod :::=> $payload");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.appealParentUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null) {
          isParentAppealLoading.value = false;
          return true;
        } else {
          isParentAppealLoading.value = false;
          return false;
        }
      } else {
        isParentAppealLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isParentAppealLoading.value = false;
      if (kDebugMode) {
        print("appealParentsApi $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<void> _launchURL(String urlString, {bool isPhone = false}) async {
    // Ensure URL has proper scheme for web URLs
    String processedUrl = urlString;
    if (!isPhone &&
        !urlString.startsWith('http://') &&
        !urlString.startsWith('https://')) {
      processedUrl = 'https://$urlString';
    }

    final Uri url = Uri.parse(processedUrl);
    final Uri launchPhone = Uri(
      scheme: 'tel',
      path: urlString,
    );
    final Uri targetUri = isPhone ? launchPhone : url;

    if (!await canLaunchUrl(targetUri)) {
      if (kIsWeb && !isPhone) {
        try {
          await launchUrl(
            targetUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return;
        } catch (e) {
          throw Exception('Could not launch $targetUri: $e');
        }
      }
      throw Exception('Could not launch $targetUri');
    }

    // Use different launch modes based on platform
    if (kIsWeb) {
      // For web, force external browser
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
        webOnlyWindowName: '_blank',
      );
    } else {
      // For mobile platforms
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
      );
    }
  }
}
