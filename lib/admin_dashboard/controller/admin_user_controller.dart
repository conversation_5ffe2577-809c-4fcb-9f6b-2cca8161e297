import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/admin_model/admin_details_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/users/admin_position_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/users/admin_user_model.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:parenthing_dashboard/view/common_widgets/loader.dart';



class AdminUserController extends GetxController {
  RxBool isUserLoading = true.obs;
  RxBool isEditUserLoading = true.obs;
  RxBool isAdminDataLoading = true.obs;
  RxBool isUserCreated = true.obs;
  RxBool isAdminUserDeleted = true.obs;
  var adminUserList = <AdminUsersModel>[].obs;
  var adminPositionList = <AdminPositionModel>[].obs;
  var adminDetailsModel = AdminModel().obs;
  String userID = "0";
  RxBool isImageUploading = true.obs;
  final adminNameController = TextEditingController().obs;
  final adminProfilePicture = TextEditingController().obs;

  late final ApiController apiController;


  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    getAllAdminPosition();
    super.onInit();
  }

  void clearAdminData() {
    adminDetailsModel.value = AdminModel();
  }

  void getAllAdminPosition() {
    apiController.getApi(AppUrl.adminPositionUrl).then((value) {
      if (value['success'] == true) {
        final adminPositionModel = value['data'];
        adminPositionList.value = adminPositionModel
            .map<AdminPositionModel>((e) => AdminPositionModel.fromJson(e))
            .toList();
      } else {
        adminPositionList.value = [];
        //classesCategoryListLoading.value = false;
      }
    }).onError((error, stackTrace) {
      adminPositionList.value = [];
      log('getAdminPosition : $error \n$stackTrace');
      //classesCategoryListLoading.value = false;
    });
  }

  Future<bool> getAllAdminUsers() async {
    try {
      Map<String, dynamic> payload = {};
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminUsersList,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          adminUserList.value = data
              .map<AdminUsersModel>((e) => AdminUsersModel.fromJson(e))
              .toList();
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isUserLoading.value = false;
          });
          return true;
        } else {
          adminUserList.value = [];
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isUserLoading.value = false;
          });
          return false;
        }
      } else {
        adminUserList.value = [];
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isUserLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminUserList.value = [];
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isUserLoading.value = false;
      });
      if (kDebugMode) {
        print("=>fun getAllParentList  $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminDetails() async {
    userID = storage.read("USER_ID") ?? "0";

    if (userID == "0" || userID.isEmpty) {
      isAdminDataLoading.value = false;
      return false;
    }
    try {
      Map<String, dynamic> payload = {"admin_id": int.parse(userID)};
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.getAdminDetails,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true && jsonMap['data'] != null) {
        final data = jsonMap['data'];
        adminDetailsModel.value = AdminModel.fromJson(data);
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isAdminDataLoading.value = false;
        });
        return true;
      } else {
        adminDetailsModel.value = AdminModel();
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isAdminDataLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminDetailsModel.value = AdminModel();
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isAdminDataLoading.value = false;
      });
      if (kDebugMode) {
        print("admin details $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> editAdminDetails() async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminEditProfilUrl,
        payload: adminDetailsModel.value,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isEditUserLoading.value = false;
        return true;
      } else {
        isEditUserLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isEditUserLoading.value = false;
      if (kDebugMode) {
        log("admin edit details $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> adminDashboardImageUploadApi(
      {required Map<String, dynamic> payload}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminImageUploadUrl,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isImageUploading.value = false;
        return true;
      } else {
        isImageUploading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isImageUploading.value = false;
      if (kDebugMode) {
        print(" adminUserController adminImageUploadApi $e \n  $stackTrace");
      }
      return false;
    }
  }

  //! create event fun
  Future<bool> createAdminUser(Map<String, dynamic> payload) async {
    userID = storage.read("USER_ID") ?? "0";

    try {
      Loader.showLoading();
      isUserCreated.value = true;
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.createAdminUrl,
        payload: payload,
      );
      var jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        await Future.delayed(const Duration(milliseconds: 300));
        Loader.hideLoading();
        isUserCreated.value = false;
        return true;
      } else {
        Loader.hideLoading();
        isUserCreated.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isUserCreated.value = false;
      if (kDebugMode) {
        print("=>fun createAdminUser $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> deleteAdminUser(int adminUserID) async {
    try {
      Loader.showLoading();
      isAdminUserDeleted.value = true;
      Map<String, dynamic> payload = {
        "admin_id": adminUserID,
      };

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.deleteAdminUser,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Loader.hideLoading();
          isAdminUserDeleted.value = false;
        });
        return true;
      } else {
        Loader.hideLoading();
        isAdminUserDeleted.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      Loader.hideLoading();
      isAdminUserDeleted.value = false;
      if (kDebugMode) {
        print("=>fun deleteAdminUser $e \n  $stackTrace");
      }
      return false;
    }
  }
}
