import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/admin_model/admin_home_pending_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_business_byid_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_business_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_business_report_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_class_details.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_class_list_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_event_details_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_event_list_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_kyc_list_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_report_class_by_id_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/admin_reportby_event_model.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/business/kyc_requestID_model.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';
import 'package:parenthing_dashboard/res/api_url.dart';
import 'package:url_launcher/url_launcher.dart';

class AdminBusinessController extends GetxController {
  String fileName = "AdminBusinessController";
  var pendingReviewList = PendingReviewModel().obs;
  RxBool isAdminBusinessListLoading = true.obs;
  RxBool isverifyKycLoading = true.obs;
  String userID = "0";
  var adminBusinessList = <AdminBusinessModel>[].obs;
  var businessListTotalModel = <AdminBusinessListTotal>[].obs;
  RxBool isAdminkycListLoading = true.obs;
  var adminKycList = <AdminKycListModel>[].obs;
  RxBool iskycByIDLoading = true.obs;
  var kycRequestByIDModel = KycRequestIdModel().obs;
  RxBool isAdminEventListLoading = true.obs;
  var adminEventList = <AdminEventListModel>[].obs;
  RxBool isAdminClassListLoading = true.obs;
  var adminClassList = <AdminClassListModel>[].obs;
  RxBool isBusinessByIDLoading = true.obs;
  RxBool isEventsLoading = true.obs;
  RxBool isClassesLoading = true.obs;
  var adminBusinessByIDModel = BusinessByIdModel(classes: [], events: []).obs;
  var eventsList = <EventModel>[].obs;
  var classesList = <ClassModel>[].obs;
  RxBool isEventByIDLoading = true.obs;
  var adminEventByIDModel =
      AdminEventDetailsModel(activityLogs: [], reportSummary: []).obs;
  RxBool isApproveEventLoading = true.obs;
  RxBool isApproveClassLoading = true.obs;
  RxBool isClassByIDLoading = true.obs;
  var adminClassByIDModel =
      AdminClassDetailsModel(activityLogs: [], reportSummary: []).obs;
  RxBool isAdminReportListLoading = true.obs;
  var adminBusinessReportList = <AdminBusinessReportModel>[].obs;
  RxBool isAdminReportEventByIDLoadiong = true.obs;
  var adminEventReportByID =
      AdminReportEventById(activityLogs: [], reportSummary: []).obs;
  RxBool isAdminActionReportEventLoading = true.obs;
  RxBool isadminClassReportByIDLoading = true.obs;
  var adminClassReportByID =
      AdminReportClassById(activityLogs: [], reportSummary: []).obs;

  late final ApiController apiController;

  @override
  void onInit() {
    apiController = Get.find<ApiController>();
    super.onInit();
  }

  Future<bool> getAllAdminBusinessList(
      {String startDate = "",
      String endDate = "",
      int page = 0,
      // int size = 0,
      String search = "",
      String? action,
      String city = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID), "page": page,
        // "size": size
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (search.isNotEmpty) payload["search"] = search.toLowerCase();
      if (city.isNotEmpty) payload["city"] = city.toLowerCase();
      if (action != null) payload["action"] = action;

      if (kDebugMode) {
        print("payload :::=> getAllAdminBusinessList $payload");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.businessListApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            businessListTotalModel.value = [];
            adminBusinessList.value = data
                .map<AdminBusinessModel>((e) => AdminBusinessModel.fromJson(e))
                .toList();
            businessListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminBusinessListLoading.value = false;
            });
            return true;
          } else {
            adminBusinessList.value = [];
            businessListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminBusinessListLoading.value = false;
            });
            return false;
          }
        }
      } else {
        adminBusinessList.value = [];
        businessListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 200)).then((_) {
          isAdminBusinessListLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminBusinessList.value = [];
      businessListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        isAdminBusinessListLoading.value = false;
      });
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminKycList(
      {String startDate = "",
      String endDate = "",
      int page = 0,
      // int size = 0,
      String status = "",
      String? action,
      String search = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID), "page": page,
        // "size": size
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (status.isNotEmpty) payload["status"] = status.toLowerCase();
      if (search.isNotEmpty) payload["search"] = search.toLowerCase();
      if (action != null) payload["action"] = action;

      if (kDebugMode) {
        print('payload :::=> getAdminKycList paylaod :::=> $payload');
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.kycListApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            businessListTotalModel.value = [];
            adminKycList.value = data
                .map<AdminKycListModel>((e) => AdminKycListModel.fromJson(e))
                .toList();
            businessListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminkycListLoading.value = false;
            });
            return true;
          } else {
            adminKycList.value = [];
            businessListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminkycListLoading.value = false;
            });
            return false;
          }
        }
      } else {
        adminKycList.value = [];
        businessListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 200)).then((_) {
          isAdminkycListLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminKycList.value = [];
      businessListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        isAdminkycListLoading.value = false;
      });
      if (kDebugMode) {
        log("$fileName=>admin business controller kycList $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getKycRequestByID(int requestID) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "request_id": requestID,
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.kycRequestIDApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          kycRequestByIDModel.value = KycRequestIdModel.fromJson(data);
          Future.delayed(const Duration(milliseconds: 200)).then((_) {
            iskycByIDLoading.value = false;
          });
          return true;
        } else {
          kycRequestByIDModel.value = KycRequestIdModel();
          Future.delayed(const Duration(milliseconds: 200)).then((_) {
            iskycByIDLoading.value = false;
          });
          return false;
        }
      } else {
        kycRequestByIDModel.value = KycRequestIdModel();
        Future.delayed(const Duration(milliseconds: 200)).then((_) {
          iskycByIDLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      kycRequestByIDModel.value = KycRequestIdModel();
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        iskycByIDLoading.value = false;
      });
      if (kDebugMode) {
        log("$fileName=>admin business controller kycList $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the event model after use
  void clearAdminKYCData() {
    iskycByIDLoading.value = true;
    kycRequestByIDModel.value = KycRequestIdModel();
  }

  Future<bool> verifyAdminKyc(
      {required int businesID,
      required String status,
      required String reason}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "business_id": businesID,
        "status": status,
        "reason": reason
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.verifyKycApi,
        payload: payload,
      );
      if (kDebugMode) {
        print("payload :::=> verify kyc payload $payload");
      }
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isverifyKycLoading.value = false;
        return true;
      } else {
        isverifyKycLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isverifyKycLoading.value = false;
      if (kDebugMode) {
        log("$fileName=>admin business Controller verifykyc $e \n  $stackTrace");
      }
      return false;
    }
  }

  void getAdminPendingReview() async {
    apiController.getApi(AppUrl.pendingReviewApi).then((value) {
      if (value != null) {
        if (kDebugMode) {
          print("Response: $value");
        }
        if (value['success'] == true) {
          final pendingReview = value['data'];
          pendingReviewList.value = PendingReviewModel.fromJson(pendingReview);
        } else {
          log("API response not successful: ${value['msg']}");
        }
      }
    }).onError((error, stackTrace) {
      log("payload :::=> getAdminPendingReview PendingReviewModel: $error \n$stackTrace");
    });
  }

  Future<bool> getAdminEventList(
      {String startDate = "",
      String endDate = "",
      String search = "",
      int page = 0,
      // int size = 0,
      String? action,
      String status = "",
      String eventType = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "page": page,
        // "size": size
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (status.isNotEmpty) payload["status"] = status.toLowerCase();
      if (search.isNotEmpty) payload["search"] = search.toLowerCase();
      if (eventType.isNotEmpty) payload["event_type"] = eventType.toLowerCase();
      if (action != null) payload["action"] = action;

      if (kDebugMode) {
        print("payload :::=> getAdminEventList $payload");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.eventListApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            businessListTotalModel.value = [];
            adminEventList.value = data
                .map<AdminEventListModel>(
                    (e) => AdminEventListModel.fromJson(e))
                .toList();
            businessListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminEventListLoading.value = false;
            });
            return true;
          } else {
            adminEventList.value = [];
            businessListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminEventListLoading.value = false;
            });
            return false;
          }
        }
      } else {
        adminEventList.value = [];
        businessListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 200)).then((_) {
          isAdminEventListLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminEventList.value = [];
      businessListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        isAdminEventListLoading.value = false;
      });
      if (kDebugMode) {
        print(
            "$fileName=>admin business controller eventList $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminClassList(
      {String startDate = "",
      String endDate = "",
      String search = "",
      String status = "",
      int page = 0,
      // int size = 0,
      String? action,
      String classType = ""}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "page": page,
        // "size": size
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (status.isNotEmpty) payload["status"] = status.toLowerCase();
      if (search.isNotEmpty) payload["search"] = search.toLowerCase();
      if (classType.isNotEmpty) payload["class_type"] = classType.toLowerCase();
      if (action != null) payload["action"] = action;

      if (kDebugMode) {
        print("payload :::=> getAdminClassList $payload");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.classListApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            businessListTotalModel.value = [];
            adminClassList.value = data
                .map<AdminClassListModel>(
                    (e) => AdminClassListModel.fromJson(e))
                .toList();
            businessListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminClassListLoading.value = false;
            });
            return true;
          } else {
            adminClassList.value = [];
            businessListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 200)).then((_) {
              isAdminClassListLoading.value = false;
            });
            return false;
          }
        }
      } else {
        adminClassList.value = [];
        businessListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 200)).then((_) {
          isAdminClassListLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminClassList.value = [];
      businessListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 200)).then((_) {
        isAdminClassListLoading.value = false;
      });
      if (kDebugMode) {
        log("$fileName=>admin business controller classList $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminBusinesstByID(
      {required int businessID, required String type}) async {
    userID = storage.read("USER_ID") ?? "0";

    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "business_id": businessID,
        "type": type
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminBusinessbyID,
        payload: payload,
      );

      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          final data = jsonMap['data'];
          adminBusinessByIDModel.value = BusinessByIdModel.fromJson(data);
          if (type == "events") {
            eventsList.value = List<EventModel>.from(
                data['events'].map((e) => EventModel.fromJson(e)));
          } else if (type == "classes") {
            classesList.value = List<ClassModel>.from(
                data['classes'].map((e) => ClassModel.fromJson(e)));
          }
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isBusinessByIDLoading.value = false;
            if (type == "events") isEventsLoading.value = false;
            if (type == "classes") isClassesLoading.value = false;
          });
          return true;
        } else {
          adminBusinessByIDModel.value = adminBusinessByIDModel.value =
              BusinessByIdModel(classes: [], events: []);
          Future.delayed(const Duration(milliseconds: 300)).then((_) {
            isBusinessByIDLoading.value = false;
            if (type == "events") isEventsLoading.value = false;
            if (type == "classes") isClassesLoading.value = false;
          });
          return false;
        }
      } else {
        adminBusinessByIDModel.value = adminBusinessByIDModel.value =
            BusinessByIdModel(classes: [], events: []);
        Future.delayed(const Duration(milliseconds: 300)).then((_) {
          isBusinessByIDLoading.value = false;
          if (type == "events") isEventsLoading.value = false;
          if (type == "classes") isClassesLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminBusinessByIDModel.value = BusinessByIdModel(classes: [], events: []);
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        isBusinessByIDLoading.value = false;
        if (type == "events") isEventsLoading.value = false;
        if (type == "classes") isClassesLoading.value = false;
      });
      if (kDebugMode) {
        log("$fileName=>admin business controller getBusinessByID $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the event model after use
  void clearAdminBusinessData() {
    isBusinessByIDLoading.value = true;
    adminBusinessByIDModel.value = BusinessByIdModel(classes: [], events: []);
  }

  Future<bool> getAdminEventDetailsData(int requestID) async {
    userID = storage.read("USER_ID") ?? "0";

    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "request_id": requestID
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminEventbyID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          adminEventByIDModel.value =
              AdminEventDetailsModel.fromJson(jsonMap['data']);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isEventByIDLoading.value = false;
          });
          return true;
        } else {
          adminEventByIDModel.value =
              AdminEventDetailsModel(activityLogs: [], reportSummary: []);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isEventByIDLoading.value = false;
          });
          return false;
        }
      } else {
        adminEventByIDModel.value =
            AdminEventDetailsModel(activityLogs: [], reportSummary: []);
        await Future.delayed(const Duration(milliseconds: 200)).then((value) {
          isEventByIDLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminEventByIDModel.value =
          AdminEventDetailsModel(activityLogs: [], reportSummary: []);
      isEventByIDLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the event model after use
  void clearAdminEventData() {
    isEventByIDLoading.value = true;
    adminEventByIDModel.value =
        AdminEventDetailsModel(activityLogs: [], reportSummary: []);
  }

  Future<bool> approveAdminEventsData(
      {required int eventId,
      required String reason,
      required String type}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "event_id": eventId,
        "reason": reason,
        "type": type
      };

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminApproveEventbyID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isApproveEventLoading.value = false;
        return true;
      } else {
        isApproveEventLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isApproveEventLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun approveAdminEventsData $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> approveAdminClassData(
      {required int classID,
      required String reason,
      required String type}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "class_id": classID,
        "reason": reason,
        "type": type
      };

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminApproveClassbyID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isApproveClassLoading.value = false;
        return true;
      } else {
        isApproveClassLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isApproveClassLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun approveAdminClassData $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminClassDetailsData(int requestID) async {
    userID = storage.read("USER_ID") ?? "0";

    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "request_id": requestID
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminClassbyID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          adminClassByIDModel.value =
              AdminClassDetailsModel.fromJson(jsonMap['data']);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isClassByIDLoading.value = false;
          });
          return true;
        } else {
          adminClassByIDModel.value =
              AdminClassDetailsModel(activityLogs: [], reportSummary: []);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isClassByIDLoading.value = false;
          });
          return false;
        }
      } else {
        adminClassByIDModel.value =
            AdminClassDetailsModel(activityLogs: [], reportSummary: []);
        await Future.delayed(const Duration(milliseconds: 200)).then((value) {
          isClassByIDLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminClassByIDModel.value =
          AdminClassDetailsModel(activityLogs: [], reportSummary: []);
      isClassByIDLoading.value = false;
      if (kDebugMode) {
        print("$fileName=>fun createEvent $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the class model after use
  void clearAdminClassData() {
    isClassByIDLoading.value = true;
    adminClassByIDModel.value =
        AdminClassDetailsModel(activityLogs: [], reportSummary: []);
  }

  Future<bool> getAdminBusinessReportsData(
      {String startDate = "",
      String endDate = "",
      String search = "",
      String status = "",
      String? action,
      int page = 0,
      // int size = 0,
      required String type}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "type": type,
        "page": page,
        // "size": size
      };
      if (startDate.isNotEmpty) payload["start_date"] = startDate;
      if (endDate.isNotEmpty) payload["end_date"] = endDate;
      if (status.isNotEmpty) payload["status"] = status.toLowerCase();
      if (search.isNotEmpty) payload["search"] = search.toLowerCase();
      if (action != null) payload["action"] = action;

      if (kDebugMode) {
        log("payload :::=> getAdminReportList $payload");
      }
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.reportListApi,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (action == 'export') {
          String fileUrl = jsonMap['data'];
          await _launchURL(fileUrl);
          return true;
        } else {
          if (jsonMap != null && jsonMap['data'] != null) {
            final data = jsonMap['data'];
            final metaList = jsonMap['meta'];
            adminBusinessReportList.value = [];
            businessListTotalModel.value = [];
            adminBusinessReportList.value = data
                .map<AdminBusinessReportModel>(
                    (e) => AdminBusinessReportModel.fromJson(e))
                .toList();
            businessListTotalModel.value = metaList
                .map<AdminBusinessListTotal>(
                    (e) => AdminBusinessListTotal.fromJson(e))
                .toList();
            Future.delayed(const Duration(milliseconds: 300)).then((value) {
              isAdminReportListLoading.value = false;
            });
            return true;
          } else {
            adminBusinessReportList.value = [];
            businessListTotalModel.value = [];
            Future.delayed(const Duration(milliseconds: 300)).then((value) {
              isAdminReportListLoading.value = false;
            });
            return false;
          }
        }
      } else {
        adminBusinessReportList.value = [];
        businessListTotalModel.value = [];
        Future.delayed(const Duration(milliseconds: 300)).then((value) {
          isAdminReportListLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminBusinessReportList.value = [];
      businessListTotalModel.value = [];
      Future.delayed(const Duration(milliseconds: 300)).then((value) {
        isAdminReportListLoading.value = false;
      });
      if (kDebugMode) {
        print(
            "$fileName=>admin business controller reported list $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminReportEventDetailsData(int eventID) async {
    userID = storage.read("USER_ID") ?? "0";

    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "event_id": eventID
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminReportByEventID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          adminEventReportByID.value =
              AdminReportEventById.fromJson(jsonMap['data']);
          await Future.delayed(const Duration(milliseconds: 200))
              .then((value) => {isAdminReportEventByIDLoadiong.value = false});
          return true;
        } else {
          adminEventReportByID.value =
              AdminReportEventById(activityLogs: [], reportSummary: []);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isAdminReportEventByIDLoadiong.value = false;
          });
          return false;
        }
      } else {
        adminEventReportByID.value =
            AdminReportEventById(activityLogs: [], reportSummary: []);
        await Future.delayed(const Duration(milliseconds: 200)).then((value) {
          isAdminReportEventByIDLoadiong.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminEventReportByID.value =
          AdminReportEventById(activityLogs: [], reportSummary: []);
      isAdminReportEventByIDLoadiong.value = false;
      if (kDebugMode) {
        print(
            "$fileName=> Admin Report Event byID Details Data $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose the event model after use
  void clearAdminReportEventData() {
    isAdminReportEventByIDLoadiong.value = true;
    adminEventReportByID.value =
        AdminReportEventById(activityLogs: [], reportSummary: []);
  }

  Future<bool> actionAdminReportData(
      {required int id,
      required String reason,
      required String type,
      required String action}) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "id": id,
        "reason": reason,
        "type": type,
        "action": action
      };
      if (kDebugMode) {
        log("admin Report $type Action paylod :::=> ${json.encode(payload)}");
      }

      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminActionReportEventbyID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        isAdminActionReportEventLoading.value = false;
        return true;
      } else {
        isAdminActionReportEventLoading.value = false;
        return false;
      }
    } catch (e, stackTrace) {
      isAdminActionReportEventLoading.value = false;
      if (kDebugMode) {
        log("$fileName=>fun actionAdminReportData $e \n  $stackTrace");
      }
      return false;
    }
  }

  Future<bool> getAdminReportClassDetailsData(int classID) async {
    userID = storage.read("USER_ID") ?? "0";
    try {
      Map<String, dynamic> payload = {
        "admin_id": int.parse(userID),
        "class_id": classID
      };
      var response = await apiController.apiHelperFn(
        apiUrl: AppUrl.adminReportByClassID,
        payload: payload,
      );
      final jsonMap = json.decode(response);
      if (jsonMap['success'] == true) {
        if (jsonMap != null && jsonMap['data'] != null) {
          adminClassReportByID.value =
              AdminReportClassById.fromJson(jsonMap['data']);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isadminClassReportByIDLoading.value = false;
          });
          return true;
        } else {
          adminClassReportByID.value =
              AdminReportClassById(activityLogs: [], reportSummary: []);
          await Future.delayed(const Duration(milliseconds: 200)).then((value) {
            isadminClassReportByIDLoading.value = false;
          });
          return false;
        }
      } else {
        adminClassReportByID.value =
            AdminReportClassById(activityLogs: [], reportSummary: []);
        await Future.delayed(const Duration(milliseconds: 200)).then((value) {
          isadminClassReportByIDLoading.value = false;
        });
        return false;
      }
    } catch (e, stackTrace) {
      adminClassReportByID.value =
          AdminReportClassById(activityLogs: [], reportSummary: []);
      isadminClassReportByIDLoading.value = false;
      if (kDebugMode) {
        print("$fileName=> getAdminReportClassDetailsData $e \n  $stackTrace");
      }
      return false;
    }
  }

// to dispose class the model after use
  void clearAdminReportClassData() {
    isadminClassReportByIDLoading.value = true;
    adminClassReportByID.value =
        AdminReportClassById(activityLogs: [], reportSummary: []);
  }

  Future<void> _launchURL(String urlString, {bool isPhone = false}) async {
    // Ensure URL has proper scheme for web URLs
    String processedUrl = urlString;
    if (!isPhone &&
        !urlString.startsWith('http://') &&
        !urlString.startsWith('https://')) {
      processedUrl = 'https://$urlString';
    }

    final Uri url = Uri.parse(processedUrl);
    final Uri launchPhone = Uri(
      scheme: 'tel',
      path: urlString,
    );
    final Uri targetUri = isPhone ? launchPhone : url;

    if (!await canLaunchUrl(targetUri)) {
      if (kIsWeb && !isPhone) {
        try {
          await launchUrl(
            targetUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return;
        } catch (e) {
          throw Exception('Could not launch $targetUri: $e');
        }
      }
      throw Exception('Could not launch $targetUri');
    }

    // Use different launch modes based on platform
    if (kIsWeb) {
      // For web, force external browser
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
        webOnlyWindowName: '_blank',
      );
    } else {
      // For mobile platforms
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
      );
    }
  }
}
