// To parse this JSON data, do
//
//     final parentDetailsModel = parentDetailsModelFromJson(jsonString);

import 'dart:convert';

ParentDetailsModel parentDetailsModelFromJson(String str) =>
    ParentDetailsModel.fromJson(json.decode(str));

String parentDetailsModelToJson(ParentDetailsModel data) =>
    json.encode(data.toJson());

class ParentDetailsModel {
  int parentId;
  String firstName;
  String lastName;
  String gender;
  String dob;
  String joinedOn;
  String aboutMe;
  String email;
  String mobile;
  String address;
  int kidsCount;
  String profilePictureUrl;
  int isDeleted;
  int isDisabled;
  int hasAppealed;
  int isReported;
  List<ActivityLog> activityLog;
  List<Child> children;
  LatestAppeal latestAppeal;
  List<ReportDetail> reportDetails;

  ParentDetailsModel({
    this.parentId = 0,
    this.joinedOn = "",
    this.firstName = "",
    this.lastName = "",
    this.profilePictureUrl = "",
    this.gender = "",
    this.dob = "",
    this.aboutMe = "",
    this.email = "",
    this.mobile = "",
    this.address = "",
    this.kidsCount = 0,
    this.isDeleted = 0,
    this.isDisabled = 0,
    this.hasAppealed = 0,
    this.isReported = 0,
    required this.activityLog,
    required this.children,
    required this.latestAppeal,
    required this.reportDetails,
  });

  factory ParentDetailsModel.fromJson(Map<String, dynamic> json) =>
      ParentDetailsModel(
        parentId: json["parent_id"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        gender: json["gender"],
        dob: json["dob"],
        aboutMe: json["about_me"],
        profilePictureUrl: json["profile_picture_url"],
        email: json["email"],
        mobile: json["mobile"],
        address: json["address"],
        kidsCount: json["kids_count"],
        isDeleted: json["is_deleted"],
        isDisabled: json["is_disabled"],
        hasAppealed: json["has_appealed"],
        joinedOn: json["joinedOn"] ?? "",
        isReported: json["is_reported"],
        activityLog: List<ActivityLog>.from(
            json["activity_log"].map((x) => ActivityLog.fromJson(x))),
        reportDetails: List<ReportDetail>.from(
            json["report_details"].map((x) => ReportDetail.fromJson(x))),
        children:
            List<Child>.from(json["children"].map((x) => Child.fromJson(x))),
        latestAppeal: LatestAppeal.fromJson(json["latest_appeal"]),
      );

  Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "first_name": firstName,
        "last_name": lastName,
        "gender": gender,
        "dob": dob,
        "joinedOn": joinedOn,
        "profile_picture_url": profilePictureUrl,
        "about_me": aboutMe,
        "email": email,
        "mobile": mobile,
        "address": address,
        "kids_count": kidsCount,
        "is_deleted": isDeleted,
        "is_disabled": isDisabled,
        "has_appealed": hasAppealed,
        "is_reported": isReported,
        "activity_log": List<dynamic>.from(activityLog.map((x) => x.toJson())),
        "children": List<dynamic>.from(children.map((x) => x.toJson())),
        "report_details":
            List<dynamic>.from(reportDetails.map((x) => x.toJson())),
        "latest_appeal": latestAppeal.toJson(),
      };
}

class ActivityLog {
  int logId;
  int parentId;
  String message;
  int adminId;
  String adminName;
  String activityTime;
  String activityType;
  int reportedByParentId;

  ActivityLog({
    this.logId = 0,
    this.parentId = 0,
    this.message = "",
    this.adminId = 0,
    this.adminName = "",
    this.activityTime = "",
    this.activityType = "",
    this.reportedByParentId = 0,
  });

  factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
        logId: json["log_id"],
        parentId: json["parent_id"],
        message: json["message"],
        adminId: json["admin_id"],
        adminName: json["admin_name"],
        activityTime: json["activity_time"],
        activityType: json["activity_type"],
        reportedByParentId: json["reported_by_parent_id"],
      );

  Map<String, dynamic> toJson() => {
        "log_id": logId,
        "parent_id": parentId,
        "admin_name": adminName,
        "message": message,
        "admin_id": adminId,
        "activity_time": activityTime,
        "activity_type": activityType,
        "reported_by_parent_id": reportedByParentId,
      };
}

class Child {
  String dob;
  String gender;
  int childId;
  List<Interest> interests;
  String lastName;
  String firstName;

  Child({
    this.gender = "",
    this.childId = 0,
    this.dob = "",
    required this.interests,
    this.lastName = "",
    this.firstName = "",
  });

  factory Child.fromJson(Map<String, dynamic> json) => Child(
        gender: json["gender"] ?? "",
        dob: json["dob"] ?? "",
        childId: json["child_id"] ?? 0,
        interests: List<Interest>.from(
            json["interests"].map((x) => Interest.fromJson(x))),
        lastName: json["last_name"] ?? "",
        firstName: json["first_name"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "gender": gender,
        "dob": dob,
        "child_id": childId,
        "interests": List<dynamic>.from(interests.map((x) => x.toJson())),
        "last_name": lastName,
        "first_name": firstName,
      };
}

class Interest {
  String category;
  int interestId;
  String subCategory;

  Interest({
    this.category = "",
    this.interestId = 0,
    this.subCategory = "",
  });

  factory Interest.fromJson(Map<String, dynamic> json) => Interest(
        category: json["category"],
        interestId: json["interest_id"],
        subCategory: json["sub_category"],
      );

  Map<String, dynamic> toJson() => {
        "category": category,
        "interest_id": interestId,
        "sub_category": subCategory,
      };
}

class LatestAppeal {
  int appealId;
  String imageUrl;
  String createdAt;
  String updatedAt;
  String explanation;

  LatestAppeal({
    this.appealId = 0,
    this.imageUrl = "",
    this.createdAt = "",
    this.updatedAt = "",
    this.explanation = "",
  });

  factory LatestAppeal.fromJson(Map<String, dynamic> json) => LatestAppeal(
        appealId: json["appeal_id"],
        imageUrl: json["image_url"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        explanation: json["explanation"],
      );

  Map<String, dynamic> toJson() => {
        "appeal_id": appealId,
        "image_url": imageUrl,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "explanation": explanation,
      };
}

class ReportDetail {
  String name;
  String reason;
  String description;
  String reportedAt;
  String imageUrl;

  ReportDetail({
    this.name = "",
    this.reason = "",
    this.description = "",
    this.imageUrl = "",
    this.reportedAt = "",
  });

  factory ReportDetail.fromJson(Map<String, dynamic> json) => ReportDetail(
        name: json["name"],
        reason: json["reason"],
        imageUrl: json["image_url"],
        description: json["description"],
        reportedAt: json["reported_at"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "reason": reason,
        "image_url": imageUrl,
        "description": description,
        "reported_at": reportedAt,
      };
}
