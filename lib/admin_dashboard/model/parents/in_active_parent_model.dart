// To parse this JSON data, do
//
//     final inActiveParentModel = inActiveParentModelFromJson(jsonString);

import 'dart:convert';

List<InActiveParentModel> inActiveParentModelFromJson(String str) =>
    List<InActiveParentModel>.from(
        json.decode(str).map((x) => InActiveParentModel.fromJson(x)));

String inActiveParentModelToJson(List<InActiveParentModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class InActiveParentModel {
  int parentId;
  String gender;
  String profilePictureUrl;
  int isDeleted;
  int isDisabled;
  String updatedAt;
  String adminName;
  String parentName;

  InActiveParentModel({
    this.parentId = 0,
    this.profilePictureUrl = "",
    this.isDeleted = 0,
    this.isDisabled = 0,
    this.updatedAt = "",
    this.gender = "",
    this.adminName = "",
    this.parentName = "",
  });

  factory InActiveParentModel.fromJson(Map<String, dynamic> json) =>
      InActiveParentModel(
        parentId: json["parent_id"],
        profilePictureUrl: json["profile_picture_url"],
        isDeleted: json["is_deleted"],
        isDisabled: json["is_disabled"],
        updatedAt: json["updated_at"],
        adminName: json["admin_name"] ?? "",
        gender: json["gender"] ?? "",
        parentName: json["parent_name"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "profile_picture_url": profilePictureUrl,
        "is_deleted": isDeleted,
        "is_disabled": isDisabled,
        "updated_at": updatedAt,
        "gender": gender,
        "admin_name": adminName,
        "parent_name": parentName,
      };
}
