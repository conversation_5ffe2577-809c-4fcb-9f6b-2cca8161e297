// To parse this JSON data, do
//
//     final adminParentsModel = adminParentsModelFromJson(jsonString);

import 'dart:convert';

List<AdminParentsModel> adminParentsModelFromJson(String str) =>
    List<AdminParentsModel>.from(
        json.decode(str).map((x) => AdminParentsModel.fromJson(x)));

String adminParentsModelToJson(List<AdminParentsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminParentsModel {
  int parentId;
  String profilePictureUrl;
  String fullName;
  String joinedOn;
  String gender;
  int kidsCount;
  String city;
  String deviceType;
  int isReported;

  AdminParentsModel({
    this.parentId = 0,
    this.profilePictureUrl = "",
    this.fullName = "",
    this.joinedOn = "",
    this.gender = "",
    this.kidsCount = 0,
    this.city = "",
    this.deviceType = "",
    this.isReported = 0,
  });

  factory AdminParentsModel.fromJson(Map<String, dynamic> json) =>
      AdminParentsModel(
        parentId: json["parent_id"],
        profilePictureUrl: json["profile_picture_url"],
        fullName: json["full_name"],
        joinedOn: json["joined_on"],
        gender: json["gender"],
        kidsCount: json["kids_count"],
        city: json["city"],
        deviceType: json["device_type"],
        isReported: json["is_reported"],
      );

  Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "profile_picture_url": profilePictureUrl,
        "full_name": fullName,
        "joined_on": joinedOn,
        "gender": gender,
        "kids_count": kidsCount,
        "city": city,
        "device_type": deviceType,
        "is_reported": isReported,
      };
}
