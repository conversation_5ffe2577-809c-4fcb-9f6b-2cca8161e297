// To parse this JSON data, do
//
//     final reportedParentModel = reportedParentModelFromJson(jsonString);

import 'dart:convert';

List<ReportedParentModel> reportedParentModelFromJson(String str) =>
    List<ReportedParentModel>.from(
        json.decode(str).map((x) => ReportedParentModel.fromJson(x)));

String reportedParentModelToJson(List<ReportedParentModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ReportedParentModel {
  int reportedParentId;
  String parentName;
  String profilePictureUrl;
  List<ReportedBy> reportedBy;
  String gender;
  String reportedOn;
  String status;
  String adminName;

  ReportedParentModel({
    this.reportedParentId = 0,
    this.parentName = "",
    this.gender = "",
    this.profilePictureUrl = "",
    required this.reportedBy,
    this.reportedOn = "",
    this.status = "",
    this.adminName = "",
  });

  factory ReportedParentModel.fromJson(Map<String, dynamic> json) =>
      ReportedParentModel(
        reportedParentId: json["reported_parent_id"],
        parentName: json["parent_name"],
        profilePictureUrl: json["profile_picture_url"] ?? "",
        gender: json["gender"] ?? "",
        reportedBy: List<ReportedBy>.from(
            json["reported_by"].map((x) => ReportedBy.fromJson(x))),
        reportedOn: json["reported_on"],
        status: json["status"] ?? "",
        adminName: json["admin_name"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "reported_parent_id": reportedParentId,
        "parent_name": parentName,
        "profile_picture_url": profilePictureUrl,
        "reported_by": List<dynamic>.from(reportedBy.map((x) => x.toJson())),
        "reported_on": reportedOn,
        "status": status,
        "gender": gender,
        "admin_name": adminName,
      };
}

class ReportedBy {
  int parentId;
  String parentName;

  ReportedBy({
    this.parentId = 0,
    this.parentName = "",
  });

  factory ReportedBy.fromJson(Map<String, dynamic> json) => ReportedBy(
        parentId: json["parent_id"],
        parentName: json["parent_name"],
      );

  Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "parent_name": parentName,
      };
}
