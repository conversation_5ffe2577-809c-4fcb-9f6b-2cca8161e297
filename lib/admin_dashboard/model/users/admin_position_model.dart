// To parse this JSON data, do
//
//     final adminPositionModel = adminPositionModelFromJson(jsonString);

import 'dart:convert';

List<AdminPositionModel> adminPositionModelFromJson(String str) =>
    List<AdminPositionModel>.from(
        json.decode(str).map((x) => AdminPositionModel.fromJson(x)));

String adminPositionModelToJson(List<AdminPositionModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminPositionModel {
  String position;

  AdminPositionModel({
    this.position = '',
  });

  factory AdminPositionModel.fromJson(Map<String, dynamic> json) =>
      AdminPositionModel(
        position: json["position"],
      );

  Map<String, dynamic> toJson() => {
        "position": position,
      };
}
