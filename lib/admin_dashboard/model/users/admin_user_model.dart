// To parse this JSON data, do
//
//     final adminUsersModel = adminUsersModelFromJson(jsonString);

import 'dart:convert';

List<AdminUsersModel> adminUsersModelFromJson(String str) =>
    List<AdminUsersModel>.from(
        json.decode(str).map((x) => AdminUsersModel.fromJson(x)));

String adminUsersModelToJson(List<AdminUsersModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminUsersModel {
  int adminId;
  String name;
  String mobile;
  String profilePictureUrl;
  int isNewuser;
  String position;

  AdminUsersModel(
      {this.adminId = 0,
      this.name = "",
      this.mobile = "",
      this.profilePictureUrl = "",
      this.isNewuser = 0,
      this.position = ""});

  factory AdminUsersModel.fromJson(Map<String, dynamic> json) =>
      AdminUsersModel(
          adminId: json["admin_id"],
          name: json["name"],
          mobile: json["mobile"] ?? "",
          profilePictureUrl: json["profile_picture_url"] ?? "",
          isNewuser: json["is_newuser"],
          position: json["position"]);

  Map<String, dynamic> toJson() => {
        "admin_id": adminId,
        "name": name,
        "mobile": mobile,
        "profile_picture_url": profilePictureUrl,
        "is_newuser": isNewuser,
        "position": position
      };
}
