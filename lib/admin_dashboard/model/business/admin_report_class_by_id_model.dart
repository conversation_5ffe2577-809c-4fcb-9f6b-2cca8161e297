// To parse this JSON data, do
//
//     final adminReportClassById = adminReportClassByIdFromJson(jsonString);

import 'dart:convert';

AdminReportClassById adminReportClassByIdFromJson(String str) => AdminReportClassById.fromJson(json.decode(str));

String adminReportClassByIdToJson(AdminReportClassById data) => json.encode(data.toJson());

class AdminReportClassById {
    int classId;
    String startDate;
    String endDate;
    int price;
    String ctaMobile;
    String city;
    String address;
    String title;
    String classType;
    int minAge;
    int maxAge;
    int duration;
    String description;
    String bannerUrl;
    String status;
    String profilePictureUrl;
    String businessName;
    List<ReportSummary> reportSummary;
    List<ActivityLog> activityLogs;

    AdminReportClassById({
        this.classId = 0,
        this.startDate = "",
        this.endDate = "",
        this.price = 0,
        this.ctaMobile = "",
        this.city = "",
        this.address = "",
        this.title = "",
        this.classType = "",
        this.minAge = 0,
        this.maxAge = 0,
        this.duration = 0,
        this.description = "",
        this.bannerUrl = "",
        this.status = "",
        this.profilePictureUrl = "",
        this.businessName = "",
       required this.reportSummary,
       required this.activityLogs,
    });

    factory AdminReportClassById.fromJson(Map<String, dynamic> json) => AdminReportClassById(
        classId: json["class_id"] ?? 0,
        startDate: json["start_date"] ?? "",
        endDate: json["end_date"] ?? "",
        price: json["price"] ?? 0,
        ctaMobile: json["cta_mobile"] ?? "",
        city: json["city"] ?? "",
        address: json["address"] ?? "",
        title: json["title"] ?? "",
        classType: json["class_type"] ?? "",
        minAge: json["min_age"] ?? 0,
        maxAge: json["max_age"] ?? 0,
        duration: json["duration"] ?? 0,
        description: json["description"] ?? "",
        bannerUrl: json["banner_url"] ?? "",
        status: json["status"] ?? "",
        profilePictureUrl: json["profile_picture_url"] ?? "",
        businessName: json["business_name"] ?? "",
        reportSummary: json["report_summary"] == null ? [] : List<ReportSummary>.from(json["report_summary"].map((x) => ReportSummary.fromJson(x))),
        activityLogs: json["activity_logs"] == null ? [] : List<ActivityLog>.from(json["activity_logs"].map((x) => ActivityLog.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "class_id": classId,
        "start_date": startDate,
        "end_date": endDate,
        "price": price,
        "cta_mobile": ctaMobile,
        "city": city,
        "address": address,
        "title": title,
        "class_type": classType,
        "min_age": minAge,
        "max_age": maxAge,
        "duration": duration,
        "description": description,
        "banner_url": bannerUrl,
        "status": status,
        "profile_picture_url": profilePictureUrl,
        "business_name": businessName,
        "report_summary": List<dynamic>.from(reportSummary.map((x) => x.toJson())),
        "activity_logs": List<dynamic>.from(activityLogs.map((x) => x.toJson())),
    };
}

class ActivityLog {
    String time;
    int logId;
    String message;

    ActivityLog({
        this.time = "",
        this.logId = 0,
        this.message = "",
    });

    factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
        time: json["time"] ?? "",
        logId: json["log_id"] ?? 0,
        message: json["message"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "time": time,
        "log_id": logId,
        "message": message,
    };
}

class ReportSummary {
    int id;
    String reason;
    String attachment;
    String eventName;
    String reportedBy;
    String reportedOn;

    ReportSummary({
        this.id = 0,
        this.reason = "",
        this.attachment = "",
        this.eventName = "",
        this.reportedBy = "",
        this.reportedOn = "",
    });

    factory ReportSummary.fromJson(Map<String, dynamic> json) => ReportSummary(
        id: json["id"] ?? 0,
        reason: json["reason"] ?? "",
        attachment: json["attachment"] ?? "",
        eventName: json["event_name"] ?? "",
        reportedBy: json["reported_by"] ?? "",
        reportedOn: json["reported_on"] ?? "  ",
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "reason": reason,
        "attachment": attachment,
        "event_name": eventName,
        "reported_by": reportedBy,
        "reported_on": reportedOn,
    };
}
