// To parse this JSON data, do
//
//     final adminBusinessModel = adminBusinessModelFromJson(jsonString);

import 'dart:convert';

List<AdminBusinessModel> adminBusinessModelFromJson(String str) =>
    List<AdminBusinessModel>.from(
        json.decode(str).map((x) => AdminBusinessModel.fromJson(x)));

String adminBusinessModelToJson(List<AdminBusinessModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminBusinessModel {
  int businessId;
  String profilePictureUrl;
  String businessName;
  String joinedOn;
  String email;
  String loginat;
  String officeAddress;
  String pocName;
  String pocNumber;
  String pocAltNumber;
  String latitude;
  String longitude;
  int locationId;
  int roleId;
  int isNewuser;
  String websiteLink;
  String instagramLink;
  String facebookLink;
  String youtubeLink;
  String businessType;
  String eventTypeDescription;
  String businessDescription;
  String additionalInfo;
  String howHeard;
  String sourceOfReferral;
  int kycDone;
  String status;
  List<LocationDetails> locationDetails;

  AdminBusinessModel({
    required this.businessId,
    required this.profilePictureUrl,
    required this.businessName,
    required this.joinedOn,
    required this.loginat,
    required this.email,
    required this.officeAddress,
    required this.pocName,
    required this.pocNumber,
    required this.pocAltNumber,
    required this.latitude,
    required this.longitude,
    this.locationId = 0,
    required this.roleId,
    required this.isNewuser,
    required this.websiteLink,
    required this.instagramLink,
    required this.facebookLink,
    required this.youtubeLink,
    required this.businessType,
    required this.eventTypeDescription,
    required this.businessDescription,
    required this.additionalInfo,
    required this.howHeard,
    required this.sourceOfReferral,
    required this.kycDone,
    required this.status,
    required this.locationDetails,
  });

  factory AdminBusinessModel.fromJson(Map<String, dynamic> json) =>
      AdminBusinessModel(
        businessId: json["business_id"],
        profilePictureUrl: json["profile_picture_url"],
        businessName: json["business_name"],
        joinedOn: json["joined_on"],
        email: json["email"],
        officeAddress: json["office_address"],
        pocName: json["poc_name"],
        loginat: json['login_at'],
        pocNumber: json["poc_number"],
        pocAltNumber: json["poc_alt_number"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        locationId: json["location_id"] ?? 0,
        roleId: json["role_id"],
        isNewuser: json["is_newuser"],
        websiteLink: json["website_link"],
        instagramLink: json["instagram_link"],
        facebookLink: json["facebook_link"],
        youtubeLink: json["youtube_link"],
        businessType: json["business_type"],
        eventTypeDescription: json["event_type_description"],
        businessDescription: json["business_description"],
        additionalInfo: json["additional_info"],
        howHeard: json["how_heard"],
        sourceOfReferral: json["source_of_referral"],
        kycDone: json["kyc_done"],
        status: json["status"],
        locationDetails: List<LocationDetails>.from(
          json["location_details"].map(
            (x) => LocationDetails.fromJson(x),
          ),
        ),
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "profile_picture_url": profilePictureUrl,
        "business_name": businessName,
        "joined_on": joinedOn,
        "email": email,
        "office_address": officeAddress,
        "poc_name": pocName,
        "poc_number": pocNumber,
        "login_at": loginat,
        "poc_alt_number": pocAltNumber,
        "latitude": latitude,
        "longitude": longitude,
        "location_id": locationId,
        "role_id": roleId,
        "is_newuser": isNewuser,
        "website_link": websiteLink,
        "instagram_link": instagramLink,
        "facebook_link": facebookLink,
        "youtube_link": youtubeLink,
        "business_type": businessType,
        "event_type_description": eventTypeDescription,
        "business_description": businessDescription,
        "additional_info": additionalInfo,
        "how_heard": howHeard,
        "source_of_referral": sourceOfReferral,
        "kyc_done": kycDone,
        "status": status,
        // "location_details": List<dynamic>.from(
        //   locationDetails.map(
        //     (x) => x.toJson(),
        //   ),
        //),
      };
}

List<AdminBusinessListTotal> adminBusinessListTotalFromJson(String str) =>
    List<AdminBusinessListTotal>.from(
        json.decode(str).map((x) => AdminBusinessListTotal.fromJson(x)));

String adminBusinessListTotalToJson(List<AdminBusinessListTotal> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminBusinessListTotal {
  int total;

  AdminBusinessListTotal({
    required this.total,
  });

  factory AdminBusinessListTotal.fromJson(Map<String, dynamic> json) =>
      AdminBusinessListTotal(
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "total": total,
      };
}

class LocationDetails {
  String area;
  String city;
  String state;
  String address;
  String country;
  double latitude;
  int pinCode;
  double longitude;
  int locationId;
  String subLocality;

  LocationDetails({
    required this.area,
    required this.city,
    required this.state,
    required this.address,
    required this.country,
    required this.latitude,
    required this.pinCode,
    required this.longitude,
    this.locationId = 0,
    required this.subLocality,
  });

  factory LocationDetails.fromJson(Map<String, dynamic> json) =>
      LocationDetails(
        area: json["area"],
        city: json["city"],
        state: json["state"],
        address: json["address"],
        country: json["country"],
        latitude: json["latitude"]?.toDouble(),
        pinCode: json["pin_code"],
        longitude: json["longitude"]?.toDouble(),
        locationId: json["location_id"] ?? 0,
        subLocality: json["sub_locality"],
      );

  Map<String, dynamic> toJson() => {
        "area": area,
        "city": city,
        "state": state,
        "address": address,
        "country": country,
        "latitude": latitude,
        "pin_code": pinCode,
        "longitude": longitude,
        "location_id": locationId,
        "sub_locality": subLocality,
      };
}
