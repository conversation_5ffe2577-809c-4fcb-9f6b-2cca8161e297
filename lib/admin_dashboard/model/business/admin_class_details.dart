// To parse this JSON data, do
//
//     final adminClassDetailsModel = adminClassDetailsModelFromJson(jsonString);

import 'dart:convert';

AdminClassDetailsModel adminClassDetailsModelFromJson(String str) =>
    AdminClassDetailsModel.fromJson(json.decode(str));

String adminClassDetailsModelToJson(AdminClassDetailsModel data) =>
    json.encode(data.toJson());

class AdminClassDetailsModel {
  int businessId;
  int classId;
  String bannerUrl;
  String submittedOn;
  String title;
  String classType;
  int minAge;
  int maxAge;
  int duration;
  int price;
  String description;
  String startDate;
  int requestId;
  String endDate;
  String ctaMobile;
  String category;
  String subCategory;
  String businessName;
  String city;
  String address;
  String sessionType;
  String businesslogo;
  String status;
  List<ReportSummary> reportSummary;
  List<ActivityLog> activityLogs;

  AdminClassDetailsModel(
      {this.businessId = 0,
      this.classId = 0,
      this.bannerUrl = "",
      this.submittedOn = "",
      this.title = "",
      this.classType = "",
      this.minAge = 0,
      this.maxAge = 0,
      this.duration = 0,
      this.price = 0,
      this.description = "",
      this.startDate = "",
      this.requestId = 0,
      this.endDate = "",
      this.ctaMobile = "",
      this.category = "",
      this.subCategory = "",
      this.businessName = "",
      this.city = "",
      this.address = "",
      this.sessionType = "",
      this.businesslogo = "",
        required this.reportSummary,
       required this.activityLogs,
      this.status = ""});

  factory AdminClassDetailsModel.fromJson(Map<String, dynamic> json) =>
      AdminClassDetailsModel(
          businessId: json["business_id"],
          classId: json["class_id"],
          bannerUrl: json["banner_url"],
          submittedOn: json["submitted_on"],
          title: json["title"],
          classType: json["class_type"],
          minAge: json["min_age"],
          maxAge: json["max_age"],
          duration: json["duration"],
          price: json["price"],
          description: json["description"],
          startDate: json["start_date"],
          requestId: json["request_id"],
          endDate: json["end_date"],
          ctaMobile: json["cta_mobile"],
          category: json["category"],
          subCategory: json["sub_category"],
          businessName: json["business_name"],
          city: json["city"],
          address: json["address"],
          sessionType: json["session_type"] ?? "",
          businesslogo: json["business_profile_url"] ?? "",
          status: json["status"]??"",
             reportSummary: json["report_summary"] == null ? [] : List<ReportSummary>.from(json["report_summary"].map((x) => ReportSummary.fromJson(x))),
        activityLogs: json["activity_logs"] == null ? [] : List<ActivityLog>.from(json["activity_logs"].map((x) => ActivityLog.fromJson(x))),
          );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "class_id": classId,
        "banner_url": bannerUrl,
        "submitted_on": submittedOn,
        "title": title,
        "class_type": classType,
        "min_age": minAge,
        "max_age": maxAge,
        "duration": duration,
        "price": price,
        "description": description,
        "start_date": startDate,
        "request_id": requestId,
        "end_date": endDate,
        "cta_mobile": ctaMobile,
        "category": category,
        "sub_category": subCategory,
        "business_name": businessName,
        "city": city,
        "address": address,
        "session_type": sessionType,
        "business_profile_url": businesslogo,
        "status": status,
            "report_summary": List<dynamic>.from(reportSummary.map((x) => x.toJson())),
        "activity_logs": List<dynamic>.from(activityLogs.map((x) => x.toJson())),
      };
}

class ActivityLog {
    String time;
    int logId;
    String message;

    ActivityLog({
        this.time = "",
        this.logId = 0,
        this.message = "",
    });

    factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
        time: json["time"] ?? "",
        logId: json["log_id"] ?? 0,
        message: json["message"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "time": time,
        "log_id": logId,
        "message": message,
    };
}

class ReportSummary {
    int id;
    String reason;
    String attachment;
    String eventName;
    String reportedBy;
    String reportedOn;

    ReportSummary({
        this.id = 0,
        this.reason = "",
        this.attachment = "",
        this.eventName = "",
        this.reportedBy = "",
        this.reportedOn = "",
    });

    factory ReportSummary.fromJson(Map<String, dynamic> json) => ReportSummary(
        id: json["id"] ?? 0,
        reason: json["reason"] ?? "",
        attachment: json["attachment"] ?? "",
        eventName: json["event_name"] ?? "",
        reportedBy: json["reported_by"] ?? "",
        reportedOn: json["reported_on"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "reason": reason,
        "attachment": attachment,
        "event_name": eventName,
        "reported_by": reportedBy,
        "reported_on": reportedOn,
    };
}
