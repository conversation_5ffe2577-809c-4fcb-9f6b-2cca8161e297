// To parse this JSON data, do
//
//     final adminReportEventById = adminReportEventByIdFromJson(jsonString);

import 'dart:convert';

AdminReportEventById adminReportEventByIdFromJson(String str) => AdminReportEventById.fromJson(json.decode(str));

String adminReportEventByIdToJson(AdminReportEventById data) => json.encode(data.toJson());

class AdminReportEventById {
    int eventId;
    String startDate;
    String bannerUrl;
    String endDate;
    int price;
    String ctaMobile;
    String ctaUrl;
    String city;
    String address;
    String title;
    String eventType;
    int minAge;
    int maxAge;
    String businessName;
    int duration;
    String description;
    String status;
    String? businesslogo;
    List<ReportSummary> reportSummary;
    List<ActivityLog> activityLogs;

    AdminReportEventById({
        this.eventId = 0,
        this.businesslogo,
        this.startDate = "",
        this.bannerUrl = "",
        this.endDate = "",
        this.price = 0,
        this.ctaMobile = "",
        this.ctaUrl = "",
        this.city = "",
        this.address = "",
        this.title = "",
        this.businessName = "",
        this.eventType = "",
        this.minAge = 0,
        this.status = "",
        this.maxAge = 0,
        this.duration = 0,
        this.description = "",
       required this.reportSummary,       
       required this.activityLogs ,
    });

    factory AdminReportEventById.fromJson(Map<String, dynamic> json) => AdminReportEventById(
        eventId: json["event_id"] ?? 0,
        startDate: json["start_date"] ?? "",
        bannerUrl: json['banner_url'] ?? "",
        endDate: json["end_date"] ?? "",
        price: json["price"] ?? 0,
        status: json["status"] ?? "",
        ctaMobile: json["cta_mobile"] ?? "",
        ctaUrl: json["cta_url"] ?? "",
        city: json["city"] ?? "",
        address: json["address"] ?? "",
        title: json["title"] ?? "",
        eventType: json["event_type"] ?? "",
        businessName: json["business_name"] ?? "",
        minAge: json["min_age"] ?? 0,
        maxAge: json["max_age"] ?? 0,
        duration: json["duration"] ?? 0,
        description: json["description"] ?? "",
         businesslogo: json["profile_picture_url"],
        reportSummary: json["report_summary"] == null ? [] : List<ReportSummary>.from(json["report_summary"].map((x) => ReportSummary.fromJson(x))),
        activityLogs: json["activity_logs"] == null ? [] : List<ActivityLog>.from(json["activity_logs"].map((x) => ActivityLog.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "event_id": eventId,
        "start_date": startDate,
        "banner_url":bannerUrl,
        "end_date": endDate,
        "price": price,
        "cta_mobile": ctaMobile,
        "cta_url": ctaUrl,
        "business_name": businessName,
        "city": city,
        "profile_picture_url": businesslogo,
        "status":status,
        "address": address,
        "title": title,
        "event_type": eventType,
        "min_age": minAge,
        "max_age": maxAge,
        "duration": duration,
        "description": description,
        "report_summary": List<dynamic>.from(reportSummary.map((x) => x.toJson())),
        "activity_logs": List<dynamic>.from(activityLogs.map((x) => x.toJson())),
    };
}

class ActivityLog {
    String time;
    int logId;
    String message;

    ActivityLog({
        this.time = "",
        this.logId = 0,
        this.message = "",
    });

    factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
        time: json["time"] ?? "",
        logId: json["log_id"] ?? 0,
        message: json["message"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "time": time,
        "log_id": logId,
        "message": message,
    };
}

class ReportSummary {
    int id;
    String reason;
    String attachment;
    String eventName;
    String reportedBy;
    String reportedOn;

    ReportSummary({
        this.id = 0,
        this.reason = "",
        this.attachment = "",
        this.eventName = "",
        this.reportedBy = "",
        this.reportedOn = "",
    });

    factory ReportSummary.fromJson(Map<String, dynamic> json) => ReportSummary(
        id: json["id"],
        reason: json["reason"] ?? "",
        attachment: json["attachment"] ?? "",
        eventName: json["event_name"] ?? "",
        reportedBy: json["reported_by"] ?? "",
        reportedOn: json["reported_on"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "reason": reason,
        "attachment": attachment,
        "event_name": eventName,
        "reported_by": reportedBy,
        "reported_on": reportedOn,
    };
}
