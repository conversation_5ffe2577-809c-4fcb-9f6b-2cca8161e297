// To parse this JSON data, do
//
//     final adminBusinessReportModel = adminBusinessReportModelFromJson(jsonString);

import 'dart:convert';

List<AdminBusinessReportModel> adminBusinessReportModelFromJson(String str) => List<AdminBusinessReportModel>.from(json.decode(str).map((x) => AdminBusinessReportModel.fromJson(x)));

String adminBusinessReportModelToJson(List<AdminBusinessReportModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminBusinessReportModel {
    int eventId;
    int id;
    String eReason;
    String eTitle;
    String eBusinessName;
    int eIsPublished;
    String eDoneBy;
    String eStatus;
    int eNumberOfReports;
    List<ReportedBy> eReportedBy;
    int classId;
    String cReason;
    String cTitle;
    String cBusinessName;
    int cIsPublished;
    String cDoneBy;
    String cStatus;
    int cNumberOfReports;
    List<ReportedBy> cReportedBy;

    AdminBusinessReportModel({
        this.eventId = 0,
        this.id = 0,
        this.eReason = "",
        this.eTitle= "",
        this.eBusinessName = "",
        this.eIsPublished = 0,
        this.eDoneBy = "",
        this.eStatus = "",
        this.eNumberOfReports = 0,
       required this.eReportedBy ,
        this.classId = 0,
        this.cReason = "",
        this.cTitle = "",
        this.cBusinessName = "",
        this.cIsPublished = 0,
        this.cDoneBy = "",
        this.cStatus = "",
        this.cNumberOfReports = 0,
       required this.cReportedBy,
    });

    factory AdminBusinessReportModel.fromJson(Map<String, dynamic> json) => AdminBusinessReportModel(
        eventId: json["event_id"] ?? 0,
        id: json["id"] ?? 0,
        eReason: json["e_reason"] ?? "",
        eTitle: json["e_title"] ?? "",
        eBusinessName: json["e_business_name"] ?? "",
        eIsPublished: json["e_is_published"] ?? 0,
        eDoneBy: json["e_done_by"] ?? "",
        eStatus: json["e_status"] ?? "",
        eNumberOfReports: json["e_number_of_reports"] ?? 0,
        eReportedBy:  json["e_reported_by"] != null ? List<ReportedBy>.from(json["e_reported_by"].map((x) => ReportedBy.fromJson(x))) : [],
        classId: json["class_id"] ?? 0,
        cReason: json["c_reason"] ?? "",
        cTitle: json["c_title"] ?? "",
        cBusinessName: json["c_business_name"]?? "",
        cIsPublished: json["c_is_published"] ?? 0,
        cDoneBy: json["c_done_by"] ?? "",
        cStatus: json["c_status"] ?? "",
        cNumberOfReports: json["c_number_of_reports"] ?? 0,
        cReportedBy:  json["c_reported_by"] != null ? List<ReportedBy>.from(json["c_reported_by"].map((x) => ReportedBy.fromJson(x))): [],
    );

    Map<String, dynamic> toJson() => {
        "event_id": eventId,
        "id": id,
        "e_reason": eReason,
        "e_title": eTitle,
        "e_business_name": eBusinessName,
        "e_is_published": eIsPublished,
        "e_done_by": eDoneBy,
        "e_status": eStatus,
        "e_number_of_reports": eNumberOfReports,
        "e_reported_by": List<dynamic>.from(eReportedBy.map((x) => x.toJson())),
        "class_id": classId,
        "c_reason": cReason,
        "c_title": cTitle,
        "c_business_name": cBusinessName,
        "c_is_published": cIsPublished,
        "c_done_by": cDoneBy,
        "c_status": cStatus,
        "c_number_of_reports": cNumberOfReports,
        "c_reported_by": List<dynamic>.from(cReportedBy.map((x) => x.toJson())),
    };
}

class ReportedBy {
    int parentId;
    String firstName;
    String lastName;

    ReportedBy({
        this.parentId = 0,
        this.firstName = "",
        this.lastName = "",
    });

    factory ReportedBy.fromJson(Map<String, dynamic> json) => ReportedBy(
        parentId: json["parent_id"] ?? 0,
        firstName: json["first_name"] ?? "",
        lastName: json["last_name"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "parent_id": parentId,
        "first_name": firstName,
        "last_name": lastName,
    };
}
