import 'dart:convert';

AdminEventDetailsModel adminEventDetailsModelFromJson(String str) => AdminEventDetailsModel.fromJson(json.decode(str));

String adminEventDetailsModelToJson(AdminEventDetailsModel data) => json.encode(data.toJson());

class AdminEventDetailsModel {
  int businessId;
  int eventId;
  String bannerUrl;
  String submittedOn;
  String title;
  String eventType;
  int minAge;
  int maxAge;
  int duration;
  int price;
  String description;
  String startDate;
  int requestId;
  String endDate;
  String ctaMobile;
  String ctaUrl;
  String businessName;
  String city;
  String address;
  String businesslogo;
  String status;
  List<ReportSummary> reportSummary;
  List<ActivityLog> activityLogs;

  AdminEventDetailsModel({
    this.businessId = 0,
    this.eventId = 0,
    this.bannerUrl = "",
    this.submittedOn = "",
    this.title = "",
    this.eventType = "",
    this.minAge = 0,
    this.maxAge = 0,
    this.duration = 0,
    this.price = 0,
    this.description = "",
    this.startDate = "",
    this.requestId = 0,
    this.endDate = "",
    this.ctaMobile = "",
    this.ctaUrl = "",
    this.businessName = "",
    this.city = "",
    this.address = "",
    this.businesslogo = "",
    this.status = "",
    required this.reportSummary,
    required this.activityLogs,
  });

  factory AdminEventDetailsModel.fromJson(Map<String, dynamic> json) => AdminEventDetailsModel(
    businessId: json["business_id"] ?? 0,
    eventId: json["event_id"] ?? 0,
    bannerUrl: json["banner_url"] ?? "",
    submittedOn: json["submitted_on"] ?? "",
    title: json["title"] ?? "",
    eventType: json["event_type"] ?? "",
    minAge: json["min_age"] ?? 0,
    maxAge: json["max_age"] ?? 0,
    duration: json["duration"] ?? 0,
    price: json["price"] ?? 0,
    description: json["description"] ?? "",
    startDate: json["start_date"] ?? "",
    requestId: json["request_id"] ?? 0,
    endDate: json["end_date"] ?? "",
    ctaMobile: json["cta_mobile"] ?? "",
    ctaUrl: json["cta_url"] ?? "",
    businessName: json["business_name"] ?? "",
    city: json["city"] ?? "",
    address: json["address"] ?? "",
    businesslogo: json["business_profile_url"] ?? "",
    status: json["status"] ?? "",
    reportSummary: json["report_summary"] == null
      ? []
      : List<ReportSummary>.from(json["report_summary"].map((x) => ReportSummary.fromJson(x))),
    activityLogs: json["activity_logs"] == null
      ? []
      : List<ActivityLog>.from(json["activity_logs"].map((x) => ActivityLog.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "business_id": businessId,
    "event_id": eventId,
    "banner_url": bannerUrl,
    "submitted_on": submittedOn,
    "title": title,
    "event_type": eventType,
    "min_age": minAge,
    "max_age": maxAge,
    "duration": duration,
    "price": price,
    "description": description,
    "start_date": startDate,
    "request_id": requestId,
    "end_date": endDate,
    "cta_mobile": ctaMobile,
    "cta_url": ctaUrl,
    "business_name": businessName,
    "city": city,
    "address": address,
    "business_profile_url": businesslogo,
    "status": status,
    "report_summary": List<dynamic>.from(reportSummary.map((x) => x.toJson())),
    "activity_logs": List<dynamic>.from(activityLogs.map((x) => x.toJson())),
  };
}

class ActivityLog {
  String time;
  int logId;
  String message;

  ActivityLog({
    this.time = "",
    this.logId = 0,
    this.message = "",
  });

  factory ActivityLog.fromJson(Map<String, dynamic> json) => ActivityLog(
    time: json["time"] ?? "",
    logId: json["log_id"] ?? 0,
    message: json["message"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "time": time,
    "log_id": logId,
    "message": message,
  };
}

class ReportSummary {
  int id;
  String reason;
  String attachment;
  String eventName;
  String reportedBy;
  String reportedOn;

  ReportSummary({
    this.id = 0,
    this.reason = "",
    this.attachment = "",
    this.eventName = "",
    this.reportedBy = "",
    this.reportedOn = "",
  });

  factory ReportSummary.fromJson(Map<String, dynamic> json) => ReportSummary(
    id: json["id"] ?? 0,
    reason: json["reason"] ?? "",
    attachment: json["attachment"] ?? "",
    eventName: json["event_name"] ?? "",
    reportedBy: json["reported_by"] ?? "",
    reportedOn: json["reported_on"] ?? "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "reason": reason,
    "attachment": attachment,
    "event_name": eventName,
    "reported_by": reportedBy,
    "reported_on": reportedOn,
  };
}
