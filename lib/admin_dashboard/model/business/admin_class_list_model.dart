// To parse this JSON data, do
//
//     final adminClassListModel = adminClassListModelFromJson(jsonString);

import 'dart:convert';

List<AdminClassListModel> adminClassListModelFromJson(String str) =>
    List<AdminClassListModel>.from(
        json.decode(str).map((x) => AdminClassListModel.fromJson(x)));

String adminClassListModelToJson(List<AdminClassListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminClassListModel {
  int businessId;
  int classId;
  String bannerUrl;
  String submittedOn;
  String title;
  String category;
  String classType;
  int price;
  String description;
  String startDate;
  String businessName;
  String city;
  String status;
  String reason;
  String doneBy;
  int requestID;

  AdminClassListModel({
    this.businessId = 0,
    this.classId = 0,
    this.bannerUrl = "",
    this.submittedOn = "",
    this.title = "",
    this.category = "",
    this.classType = "",
    this.price = 0,
    this.description = "",
    this.startDate = "",
    this.businessName = "",
    this.city = "",
    this.status = "",
    this.reason = "",
    this.doneBy = "",
    this.requestID = 0,
  });

  factory AdminClassListModel.fromJson(Map<String, dynamic> json) =>
      AdminClassListModel(
        businessId: json["business_id"],
        classId: json["class_id"],
        bannerUrl: json["banner_url"],
        submittedOn: json["submitted_on"],
        title: json["title"],
        category: json["category"],
        classType: json["class_type"],
        price: json["price"],
        description: json["description"],
        startDate: json["start_date"],
        businessName: json["business_name"],
        city: json["city"],
        status: json["status"],
        reason: json["reason"],
        doneBy: json["done_by"],
        requestID: json["request_id"] ?? 0
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "class_id": classId,
        "banner_url": bannerUrl,
        "submitted_on": submittedOn,
        "title": title,
        "category": category,
        "class_type": classType,
        "price": price,
        "description": description,
        "start_date": startDate,
        "business_name": businessName,
        "city": city,
        "status": status,
        "reason": reason,
        "done_by": doneBy,
        "request_id": requestID
      };
}
