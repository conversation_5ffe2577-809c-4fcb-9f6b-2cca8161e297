// To parse this JSON data, do
//
//     final businessByIdModel = businessByIdModelFromJson(jsonString);

import 'dart:convert';

import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';

BusinessByIdModel businessByIdModelFromJson(String str) =>
    BusinessByIdModel.fromJson(json.decode(str));

String businessByIdModelToJson(BusinessByIdModel data) =>
    json.encode(data.toJson());

class BusinessByIdModel {
  int businessId;
  String businessName;
  String email;
  String officeAddress;
  String pocName;
  String pocNumber;
  String pocAltNumber;
  String businessPhone;
  String profilePictureUrl;
  String websiteLink;
  String instagramLink;
  String facebookLink;
  String youtubeLink;
  String businessType;
  String eventTypeDescription;
  String businessDescription;
  String additionalInfo;
  String howHeard;
  String sourceOfReferral;
  int kycDone;
  String kycStatus;
  String reason;
  String createdAt;
  List<ClassModel>? classes;
  List<EventModel>? events;

  BusinessByIdModel(
      {this.businessId = 0,
      this.businessName = "",
      this.email = "",
      this.officeAddress = "",
      this.pocName = "",
      this.pocNumber = "",
      this.pocAltNumber = "",
      this.businessPhone = "",
      this.profilePictureUrl = "",
      this.websiteLink = "",
      this.instagramLink = "",
      this.facebookLink = "",
      this.youtubeLink = "",
      this.businessType = "",
      this.eventTypeDescription = "",
      this.businessDescription = "",
      this.additionalInfo = "",
      this.howHeard = "",
      this.sourceOfReferral = "",
      this.kycDone = 0,
      this.kycStatus = "",
      this.reason = "",
      this.createdAt = "",
      this.classes,
      this.events});

  factory BusinessByIdModel.fromJson(Map<String, dynamic> json) =>
      BusinessByIdModel(
        businessId: json["business_id"] ?? 0,
        businessName: json["business_name"] ?? "",
        email: json["email"] ?? "",
        officeAddress: json["office_address"] ?? "",
        pocName: json["poc_name"] ?? "",
        pocNumber: json["poc_number"] ?? "",
        pocAltNumber: json["poc_alt_number"] ?? "",
        businessPhone: json["business_phone"] ?? "",
        profilePictureUrl: json["profile_picture_url"] ?? "",
        websiteLink: json["website_link"] ?? "",
        instagramLink: json["instagram_link"] ?? "",
        facebookLink: json["facebook_link"] ?? "",
        youtubeLink: json["youtube_link"] ?? "",
        businessType: json["business_type"] ?? "",
        eventTypeDescription: json["event_type_description"] ?? "",
        businessDescription: json["business_description"] ?? "",
        additionalInfo: json["additional_info"] ?? "",
        howHeard: json["how_heard"] ?? "",
        sourceOfReferral: json["source_of_referral"] ?? "",
        kycDone: json["kyc_done"] ?? 0,
        kycStatus: json["kyc_status"] ?? "",
        reason: json["reason"] ?? "",
        createdAt: json["created_at"] ?? "",
        events: json["events"] == null
            ? []
            : List<EventModel>.from(
                json["events"].map((x) => EventModel.fromJson(x))),
        classes: json["classes"] == null
            ? []
            : List<ClassModel>.from(
                json["classes"].map((x) => ClassModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "business_name": businessName,
        "email": email,
        "office_address": officeAddress,
        "poc_name": pocName,
        "poc_number": pocNumber,
        "poc_alt_number": pocAltNumber,
        "business_phone": businessPhone,
        "profile_picture_url": profilePictureUrl,
        "website_link": websiteLink,
        "instagram_link": instagramLink,
        "facebook_link": facebookLink,
        "youtube_link": youtubeLink,
        "business_type": businessType,
        "event_type_description": eventTypeDescription,
        "business_description": businessDescription,
        "additional_info": additionalInfo,
        "how_heard": howHeard,
        "source_of_referral": sourceOfReferral,
        "kyc_done": kycDone,
        "kyc_status": kycStatus,
        "reason": reason,
        "created_at": createdAt,
        "events": events == null
            ? []
            : List<dynamic>.from(events!.map((x) => x.toJson())),
        "classes": classes == null
            ? []
            : List<dynamic>.from(classes!.map((x) => x.toJson())),
      };
}

// class ClassList {
//   int price;
//   String title;
//   String reason;
//   String status;
//   int classId;
//   String bannerUrl;
//   String classType;
//   String eventDate;
//   int requestId;

//   ClassList({
//     this.price = 0,
//     this.title = "",
//     this.reason = "",
//     this.status = "",
//     this.classId = 0,
//     this.bannerUrl = "",
//     this.classType = "",
//     this.eventDate = "",
//     this.requestId = 0,
//   });

//   factory ClassList.fromJson(Map<String, dynamic> json) => ClassList(
//         price: json["price"],
//         title: json["title"],
//         reason: json["reason"],
//         status: json["status"],
//         classId: json["class_id"],
//         bannerUrl: json["banner_url"],
//         classType: json["class_type"],
//         eventDate: json["event_date"],
//         requestId: json["request_id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "price": price,
//         "title": title,
//         "reason": reason,
//         "status": status,
//         "class_id": classId,
//         "banner_url": bannerUrl,
//         "class_type": classType,
//         "event_date": eventDate,
//         "request_id": requestId,
//       };
// }

// class EventList {
//   int price;
//   String title;
//   String reason;
//   String status;
//   int eventId;
//   String bannerUrl;
//   String classType;
//   String eventDate;
//   int requestId;

//   EventList({
//     this.price = 0,
//     this.title = "",
//     this.reason = "",
//     this.status = "",
//     this.eventId = 0,
//     this.bannerUrl = "",
//     this.classType = "",
//     this.eventDate = "",
//     this.requestId = 0,
//   });

//   factory EventList.fromJson(Map<String, dynamic> json) => EventList(
//         price: json["price"],
//         title: json["title"],
//         reason: json["reason"],
//         status: json["status"],
//         eventId: json["event_id"],
//         bannerUrl: json["banner_url"],
//         classType: json["class_type"],
//         eventDate: json["event_date"],
//         requestId: json["request_id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "price": price,
//         "title": title,
//         "reason": reason,
//         "status": status,
//         "event_id": eventId,
//         "banner_url": bannerUrl,
//         "class_type": classType,
//         "event_date": eventDate,
//         "request_id": requestId,
//       };
// }
