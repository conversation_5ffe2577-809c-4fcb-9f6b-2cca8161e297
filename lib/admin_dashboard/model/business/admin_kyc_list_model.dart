// To parse this JSON data, do
//
//     final adminKycListModel = adminKycListModelFromJson(jsonString);

import 'dart:convert';

List<AdminKycListModel> adminKycListModelFromJson(String str) => List<AdminKycListModel>.from(json.decode(str).map((x) => AdminKycListModel.fromJson(x)));

String adminKycListModelToJson(List<AdminKycListModel> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminKycListModel {
    String businessName;
    int requestId;
    String profilePictureUrl;
    String doneBy;
    String status;
    String reason;
    String submittedOn;

    AdminKycListModel({
         this.businessName = "",
         this.requestId = 0,
         this.profilePictureUrl = "",
         this.doneBy = "",
         this.status = "",
         this.reason = "",
         this.submittedOn = "",
    });

    factory AdminKycListModel.fromJson(Map<String, dynamic> json) => AdminKycListModel(
        businessName: json["business_name"],
        requestId: json["request_id"],
        profilePictureUrl: json["profile_picture_url"],
        doneBy: json["done_by"],
        status: json["status"],
        reason: json["reason"],
        submittedOn:json["submitted_on"],
    );

    Map<String, dynamic> toJson() => {
        "business_name": businessName,
        "request_id": requestId,
        "profile_picture_url": profilePictureUrl,
        "done_by": doneBy,
        "status": status,
        "reason": reason,
        "submitted_on": submittedOn,
    };
}
