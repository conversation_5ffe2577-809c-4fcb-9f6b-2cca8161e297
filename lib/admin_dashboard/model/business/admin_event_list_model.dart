// To parse this JSON data, do
//
//     final adminEventListModel = adminEventListModelFromJson(jsonString);

import 'dart:convert';

List<AdminEventListModel> adminEventListModelFromJson(String str) =>
    List<AdminEventListModel>.from(
        json.decode(str).map((x) => AdminEventListModel.fromJson(x)));

String adminEventListModelToJson(List<AdminEventListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class AdminEventListModel {
  int businessId;
  int eventId;
  String bannerUrl;
  String submittedOn;
  String title;
  String eventType;
  int price;
  String description;
  String startDate;
  String businessName;
  String city;
  String status;
  String reason;
  String doneBy;
  int requestID;

  AdminEventListModel({
    this.businessId = 0,
    this.eventId = 0,
    this.bannerUrl = "",
    this.submittedOn = "",
    this.title = "",
    this.eventType = "",
    this.price = 0,
    this.description = "",
    this.startDate = "",
    this.businessName = "",
    this.city = "",
    this.status = "",
    this.reason = "",
    this.doneBy = "",
    this.requestID = 0,
  });

  factory AdminEventListModel.fromJson(Map<String, dynamic> json) =>
      AdminEventListModel(
        businessId: json["business_id"],
        eventId: json["event_id"],
        bannerUrl: json["banner_url"],
        submittedOn: json["submitted_on"],
        title: json["title"],
        eventType: json["event_type"],
        price: json["price"],
        description: json["description"],
        startDate: json["start_date"],
        businessName: json["business_name"],
        city: json["city"],
        status: json["status"],
        reason: json["reason"],
        doneBy: json["done_by"],
        requestID: json["request_id"] ?? 0
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "event_id": eventId,
        "banner_url": bannerUrl,
        "submitted_on": submittedOn,
        "title": title,
        "event_type": eventType,
        "price": price,
        "description": description,
        "start_date": startDate,
        "business_name": businessName,
        "city": city,
        "status": status,
        "reason": reason,
        "done_by": doneBy,
        "request_id": requestID
      };
}
