// To parse this JSON data, do
//
//     final kycRequestIdModel = kycRequestIdModelFromJson(jsonString);

import 'dart:convert';

KycRequestIdModel kycRequestIdModelFromJson(String str) => KycRequestIdModel.fromJson(json.decode(str));

String kycRequestIdModelToJson(KycRequestIdModel data) => json.encode(data.toJson());

class KycRequestIdModel {
    int requestId;
    int businessId;
    String businessName;
    String businessAddress;
    String documentType;
    String documentNumber;
    String documentUrl;
    String idType;
    String date;
    String status;

    KycRequestIdModel({
         this.requestId = 0,
         this.businessId = 0,
         this.businessName = "",
         this.businessAddress = "",
         this.documentType = "",
         this.documentNumber = "",
         this.documentUrl = "",
         this.idType = "",
         this.date = "",
         this.status = "",
    });

    factory KycRequestIdModel.fromJson(Map<String, dynamic> json) => KycRequestIdModel(
        requestId: json["request_id"],
        businessId: json["business_id"],
        businessName: json["business_name"],
        businessAddress: json["business_address"],
        documentType: json["document_type"],
        documentNumber: json["document_number"],
        documentUrl: json["document_url"],
        idType: json["id_type"],
        date: json["date"],
        status: json["status"],
    );

    Map<String, dynamic> toJson() => {
        "request_id": requestId,
        "business_id": businessId,
        "business_name": businessName,
        "business_address": businessAddress,
        "document_type": documentType,
        "document_number": documentNumber,
        "document_url": documentUrl,
        "id_type": idType,
         "date": date,
        "status": status,
    };
}
