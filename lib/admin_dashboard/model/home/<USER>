// To parse this JSON data, do
//
//     final analyticsModel = analyticsModelFromJson(jsonString);

import 'dart:convert';

AnalyticsModel analyticsModelFromJson(String str) => AnalyticsModel.fromJson(json.decode(str));

String analyticsModelToJson(AnalyticsModel data) => json.encode(data.toJson());

class AnalyticsModel {
    AnalyticsGraphResult analyticsResult;

    AnalyticsModel({
        required this.analyticsResult,
    });

    factory AnalyticsModel.fromJson(Map<String, dynamic> json) => AnalyticsModel(
        analyticsResult: AnalyticsGraphResult.fromJson(json["analytics_result"]),
    );

    Map<String, dynamic> toJson() => {
        "analytics_result": analyticsResult.toJson(),
    };
}

class AnalyticsGraphResult {
    List<EventsAnalytic> eventsAnalytics;
    GenderAnalytics genderAnalytics;
    List<ClassesAnalytic> classesAnalytics;
    List<ParentsAnalytic> parentsAnalytics;
    List<BusinessAnalytic> businessAnalytics;
    List<ConnectionsAnalytic> connectionsAnalytics;
    UserStatusAnalytics userStatusAnalytics;

    AnalyticsGraphResult({
        required this.eventsAnalytics,
        required this.genderAnalytics,
        required this.classesAnalytics,
        required this.parentsAnalytics,
        required this.businessAnalytics,
        required this.connectionsAnalytics,
        required this.userStatusAnalytics,
    });

    factory AnalyticsGraphResult.fromJson(Map<String, dynamic> json) => AnalyticsGraphResult(
        eventsAnalytics: json["events_analytics"] == null ? [] : List<EventsAnalytic>.from(json["events_analytics"].map((x) => EventsAnalytic.fromJson(x))),
        genderAnalytics: GenderAnalytics.fromJson(json["gender_analytics"]),
        classesAnalytics: json["classes_analytics"] == null ? [] : List<ClassesAnalytic>.from(json["classes_analytics"].map((x) => ClassesAnalytic.fromJson(x))),
        parentsAnalytics: json["parents_analytics"] == null ? [] : List<ParentsAnalytic>.from(json["parents_analytics"].map((x) => ParentsAnalytic.fromJson(x))),
        businessAnalytics: json["business_analytics"] == null ? [] : List<BusinessAnalytic>.from(json["business_analytics"].map((x) => BusinessAnalytic.fromJson(x))),
        connectionsAnalytics: json["connections_analytics"] == null ? [] : List<ConnectionsAnalytic>.from(json["connections_analytics"].map((x) => ConnectionsAnalytic.fromJson(x))),
        userStatusAnalytics: UserStatusAnalytics.fromJson(json["user_status_analytics"]),
    );

    Map<String, dynamic> toJson() => {
        "events_analytics": List<dynamic>.from(eventsAnalytics.map((x) => x.toJson())),
        "gender_analytics": genderAnalytics.toJson(),
        "classes_analytics": List<dynamic>.from(classesAnalytics.map((x) => x.toJson())),
        "parents_analytics": List<dynamic>.from(parentsAnalytics.map((x) => x.toJson())),
        "business_analytics": List<dynamic>.from(businessAnalytics.map((x) => x.toJson())),
        "connections_analytics": List<dynamic>.from(connectionsAnalytics.map((x) => x.toJson())),
        "user_status_analytics": userStatusAnalytics.toJson(),
    };
}

class BusinessAnalytic {
    String date;
    int newBusiness;

    BusinessAnalytic({
         this.date = '',
         this.newBusiness = 0,
    });

    factory BusinessAnalytic.fromJson(Map<String, dynamic> json) => BusinessAnalytic(
        date: json["date"] ?? "",
        newBusiness: json["new_business"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "date": date,
        "new_business": newBusiness,
    };
}

class ClassesAnalytic {
    String date;
    int newClasses;

    ClassesAnalytic({
         this.date = '',
         this.newClasses = 0,
    });

    factory ClassesAnalytic.fromJson(Map<String, dynamic> json) => ClassesAnalytic(
        date: json["date"] ?? "",
        newClasses: json["new_classes"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "date": date,
        "new_classes": newClasses,
    };
}

class ConnectionsAnalytic {
    String date;
    int newConnections;

    ConnectionsAnalytic({
         this.date = '',
         this.newConnections = 0,
    });

    factory ConnectionsAnalytic.fromJson(Map<String, dynamic> json) => ConnectionsAnalytic(
        date: json["date"] ?? "",
        newConnections: json["new_connections"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "date": date,
        "new_connections": newConnections,
    };
}

class EventsAnalytic {
    String date;
    int newEvents;

    EventsAnalytic({
         this.date = '',
         this.newEvents = 0,
    });

    factory EventsAnalytic.fromJson(Map<String, dynamic> json) => EventsAnalytic(
        date: json["date"] ?? "",
        newEvents: json["new_events"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "date": date,
        "new_events": newEvents,
    };
}

class GenderAnalytics {
    int male;
    int female;

    GenderAnalytics({
         this.male = 0,
         this.female = 0,
    });

    factory GenderAnalytics.fromJson(Map<String, dynamic> json) => GenderAnalytics(
        male: json["male"],
        female: json["female"],
    );

    Map<String, dynamic> toJson() => {
        "male": male,
        "female": female,
    };
}

class ParentsAnalytic {
    String date;
    int newParents;

    ParentsAnalytic({
         this.date = "",
         this.newParents = 0,
    });

    factory ParentsAnalytic.fromJson(Map<String, dynamic> json) => ParentsAnalytic(
        date: json["date"] ?? "",
        newParents: json["new_parents"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "date": date,
        "new_parents": newParents,
    };
}

class UserStatusAnalytics {
    int active;
    int inactive;

    UserStatusAnalytics({
         this.active = 0,
         this.inactive = 0,
    });

    factory UserStatusAnalytics.fromJson(Map<String, dynamic> json) => UserStatusAnalytics(
        active: json["active"] ?? 0,
        inactive: json["inactive"] ?? 0,
    );

    Map<String, dynamic> toJson() => {
        "active": active,
        "inactive": inactive,
    };
}
