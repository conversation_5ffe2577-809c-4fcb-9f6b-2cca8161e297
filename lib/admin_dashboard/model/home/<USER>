// To parse this JSON data, do
//
//     final homeGraphModel = homeGraphModelFromJson(jsonString);

import 'dart:convert';

HomeGraphModel homeGraphModelFromJson(String str) =>
    HomeGraphModel.fromJson(json.decode(str));

String homeGraphModelToJson(HomeGraphModel data) => json.encode(data.toJson());

class HomeGraphModel {
  AnalyticsResult analyticsResult;

  HomeGraphModel({
    required this.analyticsResult,
  });

  factory HomeGraphModel.fromJson(Map<String, dynamic> json) => HomeGraphModel(
        analyticsResult: AnalyticsResult.fromJson(json["analytics_result"]),
      );

  Map<String, dynamic> toJson() => {
        "analytics_result": analyticsResult.toJson(),
      };
}

class AnalyticsResult {
  TotalEvents totalEvents;
  TotalClasses totalClasses;
  ParentsDetails parentsDetails;
  BusinessDetails businessDetails;
  List<ParentsAnalytic> parentsAnalytics;
  List<BusinessAnalytic> businessAnalytics;

  AnalyticsResult({
    required this.totalEvents,
    required this.totalClasses,
    required this.parentsDetails,
    required this.businessDetails,
    required this.parentsAnalytics,
    required this.businessAnalytics,
  });

  factory AnalyticsResult.fromJson(Map<String, dynamic> json) =>
      AnalyticsResult(
        totalEvents: TotalEvents.fromJson(json["total_events"]),
        totalClasses: TotalClasses.fromJson(json["total_classes"]),
        parentsDetails: ParentsDetails.fromJson(json["parents_details"]),
        businessDetails: BusinessDetails.fromJson(json["business_details"]),
        parentsAnalytics: json["parents_analytics"] == null
            ? []
            : List<ParentsAnalytic>.from(json["parents_analytics"]
                .map((x) => ParentsAnalytic.fromJson(x))),
        businessAnalytics: json["business_analytics"] == null
            ? []
            : List<BusinessAnalytic>.from(json["business_analytics"]
                .map((x) => BusinessAnalytic.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total_events": totalEvents.toJson(),
        "total_classes": totalClasses.toJson(),
        "parents_details": parentsDetails.toJson(),
        "business_details": businessDetails.toJson(),
        "parents_analytics":
            List<dynamic>.from(parentsAnalytics.map((x) => x.toJson())),
        "business_analytics":
            List<dynamic>.from(businessAnalytics.map((x) => x.toJson())),
      };
}

class BusinessAnalytic {
  String date;
  int newBusiness;

  BusinessAnalytic({
    this.date = "",
    this.newBusiness = 0,
  });

  factory BusinessAnalytic.fromJson(Map<String, dynamic> json) =>
      BusinessAnalytic(
        date: json["date"] ?? "",
        newBusiness: json["new_business"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "date": date,
        "new_business": newBusiness,
      };
}

class BusinessDetails {
  double change;
  int totalBusiness;
  int thisWeekTotal;

  BusinessDetails({
    this.change = 0.0,
    this.totalBusiness = 0,
    this.thisWeekTotal = 0,
  });

  factory BusinessDetails.fromJson(Map<String, dynamic> json) =>
      BusinessDetails(
        change: json["%change"] ?? 0.0,
        totalBusiness: json["total_business"] ?? 0,
        thisWeekTotal: json["this_week_total"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "%change": change,
        "total_business": totalBusiness,
        "this_week_total": thisWeekTotal,
      };
}

class ParentsAnalytic {
  String date;
  int newParents;

  ParentsAnalytic({
    this.date = "",
    this.newParents = 0,
  });

  factory ParentsAnalytic.fromJson(Map<String, dynamic> json) =>
      ParentsAnalytic(
        date: json["date"] ?? "",
        newParents: json["new_parents"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "date": date,
        "new_parents": newParents,
      };
}

class ParentsDetails {
  dynamic change;
  int totalParents;
  int thisWeekTotal;

  ParentsDetails({
    this.change = 0,
    this.totalParents = 0,
    this.thisWeekTotal = 0,
  });

  factory ParentsDetails.fromJson(Map<String, dynamic> json) => ParentsDetails(
        change: json["%change"] ?? 0,
        totalParents: json["total_parents"] ?? 0,
        thisWeekTotal: json["this_week_total"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "%change": change,
        "total_parents": totalParents,
        "this_week_total": thisWeekTotal,
      };
}

class TotalClasses {
  double change;
  int totalClasses;
  int thisWeekTotal;

  TotalClasses({
    this.change = 0.0,
    this.totalClasses = 0,
    this.thisWeekTotal = 0,
  });

  factory TotalClasses.fromJson(Map<String, dynamic> json) => TotalClasses(
        change: json["%change"]?.toDouble(),
        totalClasses: json["total_classes"] ?? 0,
        thisWeekTotal: json["this_week_total"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "%change": change,
        "total_classes": totalClasses,
        "this_week_total": thisWeekTotal,
      };
}

class TotalEvents {
  double change;
  int totalEvents;
  int thisWeekTotal;

  TotalEvents({
    this.change = 0.0,
    this.totalEvents = 0,
    this.thisWeekTotal = 0,
  });

  factory TotalEvents.fromJson(Map<String, dynamic> json) => TotalEvents(
        change: json["%change"]?.toDouble(),
        totalEvents: json["total_events"] ?? 0,
        thisWeekTotal: json["this_week_total"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "%change": change,
        "total_events": totalEvents,
        "this_week_total": thisWeekTotal,
      };
}
