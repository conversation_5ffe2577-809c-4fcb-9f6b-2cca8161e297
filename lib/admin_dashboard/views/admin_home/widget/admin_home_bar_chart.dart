import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class AdminHomeBarChart extends StatefulWidget {
   final String city;
  final String periodType;
  const AdminHomeBarChart({super.key, required this.city,  this.periodType = "weekly"});

  @override
  State<AdminHomeBarChart> createState() => _AdminHomeBarChartState();
}

class _AdminHomeBarChartState extends State<AdminHomeBarChart> {
  final HomePageController homePageController = Get.find<HomePageController>();


  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.backcolor, width: 1.0),
        borderRadius: BorderRadius.circular(11),
      ),
      child: Obx(
        () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Parents Signups',
                  style: body3TextRegular,
                ),
                Text(
                  homePageController.homePageGraphData.value.analyticsResult.parentsDetails.totalParents.toString(),
                  style: title3TextMedium,
                ),
                Text(
                  '${homePageController.homePageGraphData.value.analyticsResult.parentsDetails.change}%',
                  style: body3TextRegular,
                ),
              ],
            ),
            const Gap(30),
            SizedBox(
              child: AspectRatio(
                aspectRatio: 1.70,
                child: homePageController.isGraphDataLoading.value
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : homePageController.homePageGraphData.value.analyticsResult.parentsAnalytics.isEmpty
                        ? const Center(
                            child: Text('No graph data'),
                          )
                        : Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: BarChartUI(
                              data: homePageController.homePageGraphData.value.analyticsResult.parentsAnalytics,
                              dataKey: "new_parents",
                            ),
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


class BarChartUI extends StatelessWidget {
  final List<dynamic> data;
  final String dataKey;

  const BarChartUI({
    required this.data,
    required this.dataKey,
    super.key,
  });

  double calculateMaxY() {
    double maxY = data.fold(0.0, (max, item) {
      final value = item?.toJson()?[dataKey]?.toDouble() ?? 0.0;
      return value > max ? value : max;
    });
    return maxY > 0 ? maxY : 100; 
  }

  @override
  Widget build(BuildContext context) {
    final maxY = calculateMaxY();

    return BarChart(
      BarChartData(
        maxY: maxY,
        minY: 0,
        gridData: const FlGridData(show: false),
        titlesData: FlTitlesData(
          show: true,
          leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < data.length) {
                  final dateString = data[value.toInt()]?.date;
                  if (dateString != null) {
                    final date = DateTime.tryParse(dateString);
                    if (date != null) {
                      return Text(DateFormat('d MMM').format(date), style: const TextStyle(fontSize: 10));
                    }
                  }
                }
                return const Text('');
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        barGroups: data.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final value = item?.toJson()?[dataKey]?.toDouble() ?? 0.0;
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: value,
                width: 25,
                color: AppColors.kprimarycolor,
                borderRadius: BorderRadius.zero,
                backDrawRodData: BackgroundBarChartRodData(show: true, toY: maxY,),
              ),
            ],
          );
        }).toList(),
        barTouchData: BarTouchData(
          
        touchTooltipData: BarTouchTooltipData(
          getTooltipColor: (context) => AppColors.lightpurple,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return BarTooltipItem(
              rod.toY.toStringAsFixed(0),
               const TextStyle(
                color: AppColors.txtprimary,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            );
          },
        ),
      ),
    ));
  }
}

// class GraphParentsChartUI extends StatelessWidget {
//   const GraphParentsChartUI({super.key});

//   double calculateMinY() {
//     double minY = double.infinity;
//     for (var entry in homePageController
//         .homePageGraphData.value.analyticsResult.parentsAnalytics) {
//       if (entry.newParents.toDouble() < minY) {
//         minY = entry.newParents.toDouble();
//       }
//     }
//     return minY;
//   }

//   double calculateMaxY() {
//     double maxY = -double.infinity;
//     for (var entry in homePageController
//         .homePageGraphData.value.analyticsResult.parentsAnalytics) {
//       if (entry.newParents.toDouble() > maxY) {
//         maxY = entry.newParents.toDouble();
//       }
//     }
//     return maxY;
//   }

//   Color hslStringToColor(String hslString) {
//     RegExp regex = RegExp(r"hsl\((\d+),\s?(\d+)%,\s?(\d+)%\)");
//     Match match = regex.firstMatch(hslString) as Match;

//     if (match.groupCount == 3) {
//       double hue = double.parse(match.group(1).toString());
//       double saturation = double.parse(match.group(2).toString());
//       double lightness = double.parse(match.group(3).toString());

//       return HSLColor.fromAHSL(1.0, hue, saturation / 100, lightness / 100)
//           .toColor();
//     } else {
//       return Colors.grey;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Obx(
//       () {
//         return LineChart(
//           LineChartData(
//             lineBarsData: [
//               LineChartBarData(
//                 spots: homePageController
//                     .homePageGraphData.value.analyticsResult.parentsAnalytics
//                     .map((data) {
//                   DateTime date = DateTime.parse(data.date);
//                   double x = date.millisecondsSinceEpoch.toDouble();
//                   double y = data.newParents.toDouble();
//                   return FlSpot(x, y);
//                 }).toList(),
//                 isCurved: true,
//                 curveSmoothness: 0.5,
//                 barWidth: 2,
//                 color: AppColors.kprimarycolor, // You can customize the color
//                 isStrokeCapRound: true,
//                 dotData: const FlDotData(show: false),
//                 belowBarData: BarAreaData(
//                   show: true,
//                   gradient: LinearGradient(
//                     colors: [
//                       AppColors.kprimarycolor.withOpacity(0.5),
//                       AppColors.kprimarycolor.withOpacity(0.0)
//                     ],
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                   ),
//                 ),
//               ),
//             ],
//             lineTouchData: LineTouchData(
//               getTouchedSpotIndicator:
//                   (LineChartBarData barData, List<int> spotIndexes) {
//                 return spotIndexes.map((spotIndex) {
//                   final spot = barData.spots[spotIndex];
//                   if (spot.x == 0 || spot.x == 6) {
//                     return null;
//                   }
//                   return TouchedSpotIndicatorData(
//                     const FlLine(
//                       strokeWidth: 2,
//                       color: Colors.grey,
//                     ),
//                     FlDotData(
//                       getDotPainter: (spot, percent, barData, index) {
//                         return FlDotCirclePainter(
//                           radius: 3,
//                           color: Colors.white,
//                           strokeWidth: 2,
//                         );
//                       },
//                     ),
//                   );
//                 }).toList();
//               },
//               touchTooltipData: LineTouchTooltipData(
//                 maxContentWidth: 300,
//                 tooltipRoundedRadius: 6,
//                 fitInsideHorizontally: true,
//                 getTooltipColor: (touchedSpot) {
//                   return AppColors.kprimarycolor.withOpacity(.1);
//                 },
//                 getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
//                   return touchedBarSpots.map((barSpot) {
//                     final flSpot = barSpot;
//                     if (flSpot.x == 0 || flSpot.x == 6) {
//                       return null;
//                     }
//                     DateTime date =
//                         DateTime.fromMillisecondsSinceEpoch(flSpot.x.toInt());
//                     String formattedDate =
//                         DateFormat('dd-MM-yyyy hh:mm:ss a').format(date);
//                     double value = flSpot.y;
//                     return LineTooltipItem(
//                       '$value signups \nDate:$formattedDate',
//                       const TextStyle(
//                           color: AppColors
//                               .kprimarycolor, // You can customize the color
//                           fontWeight: FontWeight.w400,
//                           fontSize: 13.0),
//                     );
//                   }).toList();
//                 },
//               ),
//               touchCallback:
//                   (FlTouchEvent event, LineTouchResponse? lineTouch) {
//                 if (!event.isInterestedForInteractions ||
//                     lineTouch == null ||
//                     lineTouch.lineBarSpots == null) {
//                   return;
//                 }
//                 final value = lineTouch.lineBarSpots![0].x;

//                 if (value == 0 || value == 6) {
//                   return;
//                 }
//               },
//             ),
//             minX: homePageController
//                 .homePageGraphData.value.analyticsResult.parentsAnalytics
//                 .map((data) =>
//                     DateTime.parse(data.date).millisecondsSinceEpoch.toDouble())
//                 .reduce((a, b) => a < b ? a : b),
//             maxX: homePageController
//                 .homePageGraphData.value.analyticsResult.parentsAnalytics
//                 .map((data) =>
//                     DateTime.parse(data.date).millisecondsSinceEpoch.toDouble())
//                 .reduce((a, b) => a > b ? a : b),
//             minY: calculateMinY(),
//             maxY: calculateMaxY(),
//             gridData: const FlGridData(
//               show: false,
//               drawHorizontalLine: true,
//               drawVerticalLine: false,
//             ),
//             borderData: FlBorderData(
//               show: true,
//               border: Border(
//                 bottom:
//                     BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
//                 left: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
//                 right: const BorderSide(color: Colors.transparent),
//                 top: const BorderSide(color: Colors.transparent),
//               ),
//             ),
//             titlesData: FlTitlesData(
//               bottomTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: true,
//                   reservedSize: 22,
//                   interval: 500000000,

//                   //  sensorsController.selectedGraphType == "1 Day"
//                   //     ? 7200000
//                   //     : sensorsController.selectedGraphType == "1 Week"
//                   //         ? 86400000
//                   //         : sensorsController.selectedGraphType == "Custom"
//                   //             ? 500000000
//                   //             : 600000,
//                   getTitlesWidget: (double value, TitleMeta meta) {
//                     String formattedDate = DateFormat('dd').format(
//                         DateTime.fromMillisecondsSinceEpoch(value.toInt()));
//                     return Padding(
//                       padding: const EdgeInsets.only(top: 8.0),
//                       child: Text(
//                         formattedDate,
//                         style: const TextStyle(
//                           fontSize: 10,
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//                 axisNameSize: 20.0,
//               ),
//               leftTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: false,
//                   //interval: (calculateMaxY() - calculateMinY()) / 4,
//                   reservedSize: 40,
//                   getTitlesWidget: (double value, TitleMeta meta) {
//                     return Padding(
//                       padding: const EdgeInsets.only(top: 8.0),
//                       child: Text(
//                         value.toString(),
//                         style: const TextStyle(
//                           fontSize: 10,
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//                 axisNameSize: 20.0,
//               ),
//               topTitles:
//                   const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//               rightTitles:
//                   const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
