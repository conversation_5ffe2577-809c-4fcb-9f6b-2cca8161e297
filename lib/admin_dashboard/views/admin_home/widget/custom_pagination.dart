import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:parenthing_dashboard/res/app_color.dart';

class CustomPagination extends StatelessWidget {
  final int currentPage;
  final int totalItems;
  final int itemsPerPage;
  final Function(int) onPageChanged;

  const CustomPagination({
    super.key,
    required this.currentPage,
    required this.totalItems,
    required this.itemsPerPage,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final int totalPages = (totalItems / itemsPerPage).ceil();

    List<Widget> pageButtons = [];

    // Add left arrow
    pageButtons.add(IconButton(
      icon: const Icon(Icons.chevron_left, color: AppColors.midGrey),
      onPressed: currentPage > 0 ? () => onPageChanged(currentPage - 1) : null,
    ));

    // Calculate the range of page numbers to display
    int startPage = math.max(0, math.min(currentPage - 2, totalPages - 5));
    int endPage = math.min(startPage + 4, totalPages - 1);

    // Add first page button if not in range
    if (startPage > 0) {
      pageButtons.add(_buildPageButton(0));
      if (startPage > 1) pageButtons.add(_buildEllipsis());
    }

    // Add page number buttons
    for (int i = startPage; i <= endPage; i++) {
      pageButtons.add(_buildPageButton(i));
    }

    // Add last page button if not in range
    if (endPage < totalPages - 1) {
      if (endPage < totalPages - 2) pageButtons.add(_buildEllipsis());
      pageButtons.add(_buildPageButton(totalPages - 1));
    }

    // Add right arrow
    pageButtons.add(IconButton(
      icon: const Icon(Icons.chevron_right, color: AppColors.midGrey),
      onPressed: currentPage < totalPages - 1 ? () => onPageChanged(currentPage + 1) : null,
    ));

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: pageButtons,
    );
  }

  Widget _buildPageButton(int pageNumber) {
    return GestureDetector(
      child: Container(
        height: 36,
        width: 36,
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppColors.kwhite,
          border: Border.all(
            color: pageNumber == currentPage ? AppColors.kprimarycolor : AppColors.scaffoldColor,
          ),
        ),
        child: Center(
          child: Text(
            '${pageNumber + 1}',
            style: TextStyle(
              color: pageNumber == currentPage ? AppColors.kprimarycolor : AppColors.txtsecondary,
              fontWeight: pageNumber == currentPage ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
      onTap: () => onPageChanged(pageNumber),
    );
  }

  Widget _buildEllipsis() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Text('...', style: TextStyle(color: AppColors.txtsecondary)),
    );
  }
}