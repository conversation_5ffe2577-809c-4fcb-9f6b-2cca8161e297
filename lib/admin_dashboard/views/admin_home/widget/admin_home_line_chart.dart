import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class LineChartSample2 extends StatefulWidget {
  final String city;
  final String periodType;

  const LineChartSample2(
      {super.key, required this.city,  this.periodType = "weekly"});

  @override
  State<LineChartSample2> createState() => _LineChartSample2State();
}

class _LineChartSample2State extends State<LineChartSample2> {
  final HomePageController homePageController = Get.find<HomePageController>();


  List<Color> gradientColors = [
    AppColors.kSecondaryColor1,
    AppColors.kprimarycolor,
  ];
  bool showAvg = false;

  @override
  void initState() {
    homePageController.getGraphData(widget.city, widget.periodType);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
          border: Border.all(color: AppColors.backcolor, width: 1.0),
          borderRadius: BorderRadius.circular(11)),
      child: Obx(
        () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Business Signups',
                  style: body3TextRegular,
                ),
                Text(
                  homePageController.homePageGraphData.value.analyticsResult
                      .businessDetails.totalBusiness
                      .toString(),
                  style: title3TextMedium,
                ),
                Text(
                  '${homePageController.homePageGraphData.value.analyticsResult.businessDetails.totalBusiness}%',
                  style: body3TextRegular,
                ),
              ],
            ),
            const Gap(30),
            SizedBox(
              child: AspectRatio(
                aspectRatio: 1.70,
                child: homePageController.isGraphDataLoading.value
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : homePageController.homePageGraphData.value.analyticsResult
                            .businessAnalytics.isEmpty
                        ? const Center(
                            child: Text('No graph data'),
                          )
                        :  Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: _buildLineChart( homePageController
        .homePageGraphData.value.analyticsResult.businessAnalytics, "business_analytics"),
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }


Widget _buildLineChart(List<dynamic> data, String key) {
  if (data.isEmpty) {
    return const Center(child: Text('No data available'));
  }


  final spots = data.asMap().entries.map((entry) {
    final index = entry.key.toDouble();
    final value = entry.value.toJson()['new_business'] as int; 
    return FlSpot(index, value.toDouble());
  }).toList();


  // Sort spots by x value (date)
  spots.sort((a, b) => a.x.compareTo(b.x));

if (spots.length == 1) {
    spots.add(FlSpot(1, spots[0].y));
  }

  double minY = spots.map((spot) => spot.y).reduce(min);
  double maxY = spots.map((spot) => spot.y).reduce(max);

  // Ensure there's always some vertical range
  if (minY == maxY) {
    minY = minY * 0.9;
    maxY = maxY * 1.1;
  }

  return LineChart(
    LineChartData(
      gridData: const FlGridData(show: false),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              if (value.toInt() >= 0 && value.toInt() < data.length) {
                final date = DateTime.parse(data[value.toInt()].date);
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    DateFormat('d MMM').format(date),
                    style: body3TextRegular.copyWith(fontSize: 10),
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(show: false),
      minX: 0,
      maxX: spots.length - 1.0,
      minY: minY - (maxY - minY) * 0.1,
      maxY: maxY + (maxY - minY) * 0.1,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: spots.length > 2,
          curveSmoothness: 0.3,
          color: AppColors.kprimarycolor,
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(show: spots.length <= 5),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [AppColors.kSecondaryColor1, AppColors.kSecondaryColor1.withOpacity(0.1)],
            ),
          ),
        ),
      ],
      lineTouchData: LineTouchData(
        handleBuiltInTouches: true,
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) => AppColors.lightpurple,
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              final flSpot = barSpot;
              
              if (flSpot.x.toInt() >= 0 && flSpot.x.toInt() < data.length) {
                final date = DateTime.parse(data[flSpot.x.toInt()].date);
                return LineTooltipItem(
                  '${DateFormat('d MMM').format(date)}\n${flSpot.y.toStringAsFixed(0)}',
                  const TextStyle(color: AppColors.txtprimary),
                );
              }
              return null;
            }).toList();
          },
        ),
      ),
    ),
  );
}

}

// class GraphChartUI extends StatelessWidget {
//   const GraphChartUI({super.key});

//   double calculateMinY() {
//     double minY = double.infinity;
//     for (var entry in homePageController
//         .homePageGraphData.value.analyticsResult.businessAnalytics) {
//       if (entry.newBusiness.toDouble() < minY) {
//         minY = entry.newBusiness.toDouble();
//       }
//     }
//     return minY == double.infinity ? 0 : minY;
//   }

//   double calculateMaxY() {
//     double maxY = -double.infinity;
//     for (var entry in homePageController
//         .homePageGraphData.value.analyticsResult.businessAnalytics) {
//       if (entry.newBusiness.toDouble() > maxY) {
//         maxY = entry.newBusiness.toDouble();
//       }
//     }
//     return maxY == -double.infinity ? 1 : maxY;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Obx(
//       () {
//         double minY = calculateMinY();
//         double maxY = calculateMaxY();
//         double yInterval = (maxY - minY) / 4;
//         yInterval = yInterval <= 0
//             ? 1
//             : yInterval; // Ensure yInterval is positive and non-zero

//         double minX = homePageController
//             .homePageGraphData.value.analyticsResult.businessAnalytics
//             .map((data) =>
//                 DateTime.parse(data.date).millisecondsSinceEpoch.toDouble())
//             .reduce((a, b) => a < b ? a : b);
//         double maxX = homePageController
//             .homePageGraphData.value.analyticsResult.businessAnalytics
//             .map((data) =>
//                 DateTime.parse(data.date).millisecondsSinceEpoch.toDouble())
//             .reduce((a, b) => a > b ? a : b);
//         double xInterval = (maxX - minX) / 5;
//         xInterval = xInterval <= 0
//             ? 1
//             : xInterval; // Ensure xInterval is positive and non-zero

//         return LineChart(
//           LineChartData(
//             lineBarsData: [
//               LineChartBarData(
//                 spots: homePageController
//                     .homePageGraphData.value.analyticsResult.businessAnalytics
//                     .map((data) {
//                   DateTime date = DateTime.parse(data.date);
//                   double x = date.millisecondsSinceEpoch.toDouble();
//                   double y = data.newBusiness.toDouble();
//                   return FlSpot(x, y);
//                 }).toList(),
//                 isCurved: true,
//                 curveSmoothness: 0.5,
//                 barWidth: 2,
//                 color: AppColors.kprimarycolor,
//                 isStrokeCapRound: true,
//                 dotData: const FlDotData(show: false),
//                 belowBarData: BarAreaData(
//                   show: true,
//                   gradient: LinearGradient(
//                     colors: [
//                       AppColors.kprimarycolor.withOpacity(0.5),
//                       AppColors.kprimarycolor.withOpacity(0.0)
//                     ],
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                   ),
//                 ),
//               ),
//             ],
//             lineTouchData: const LineTouchData(
//                 // touch data setup
//                 ),
//             minX: minX,
//             maxX: maxX,
//             minY: minY,
//             maxY: maxY,
//             gridData: const FlGridData(
//               show: false,
//               drawHorizontalLine: true,
//               drawVerticalLine: false,
//             ),
//             borderData: FlBorderData(
//               show: true,
//               border: Border(
//                 bottom:
//                     BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
//                 left: BorderSide(color: Colors.grey.withOpacity(0.2), width: 1),
//                 right: const BorderSide(color: Colors.transparent),
//                 top: const BorderSide(color: Colors.transparent),
//               ),
//             ),
//             titlesData: FlTitlesData(
//               bottomTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: true,
//                   reservedSize: 22,
//                   interval: xInterval,
//                   getTitlesWidget: (double value, TitleMeta meta) {
//                     String formattedDate = DateFormat('dd').format(
//                         DateTime.fromMillisecondsSinceEpoch(value.toInt()));
//                     return Padding(
//                       padding: const EdgeInsets.only(top: 8.0),
//                       child: Text(
//                         formattedDate,
//                         style: const TextStyle(
//                           fontSize: 10,
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//                 axisNameSize: 20.0,
//               ),
//               leftTitles: AxisTitles(
//                 sideTitles: SideTitles(
//                   showTitles: true,
//                   interval: yInterval,
//                   reservedSize: 40,
//                   getTitlesWidget: (double value, TitleMeta meta) {
//                     return Padding(
//                       padding: const EdgeInsets.only(top: 8.0),
//                       child: Text(
//                         value.toString(),
//                         style: const TextStyle(
//                           fontSize: 10,
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//                 axisNameSize: 20.0,
//               ),
//               topTitles:
//                   const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//               rightTitles:
//                   const AxisTitles(sideTitles: SideTitles(showTitles: false)),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }



