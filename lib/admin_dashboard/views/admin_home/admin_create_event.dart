import 'dart:developer';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/ShimmerWidget.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/model/event/event_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/event/widgets/date_time_widget.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';

class AdminCreateEventPage extends StatefulWidget {
  const AdminCreateEventPage({super.key});

  @override
  State<AdminCreateEventPage> createState() => _AdminCreateEventPageState();
}

class _AdminCreateEventPageState extends State<AdminCreateEventPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String eventType = '';
  RxString ticketType = ''.obs;
  RxBool isTicketPaid = true.obs;
  RxBool isTicketFree = false.obs;
  RxString multipleBooking = ''.obs;
  RxBool isMultipleTicketYes = true.obs;
  RxBool isMultipleTicketNo = false.obs;
  RxBool isWebUrl = false.obs;
  RxBool isWhatsAppNumber = true.obs;
  RxBool is1Selected = false.obs;
  RxBool is2Selected = false.obs;
  RxBool is3Selected = false.obs;
  String? _selectedMinAge;
  String? _selectedMaxAge;
  RxBool isOfflineSelected = true.obs;
  RxBool isOnlineSelected = false.obs;
  bool isDisplayBusinessList = true;
  String selectedAddress = '';
  String selectedBusinessName = '';
  int selectedBusinesID = 0;
  RxBool isOfflineOnlineSelected = false.obs;
  final List<String> _ageOptions =
      List<String>.generate(17, (index) => (index + 2).toString());

  final List<String> _maxAgeOptions =
      List<String>.generate(17, (index) => (index + 2).toString())..add("18+");
  EventController eventController = EventController();
  var createEventModel = EventModel(locationDetails: LocationDetails());
  String _imageUrl = '';
  final TextEditingController _descriptionController = TextEditingController();
  final int _maxLength = 500;
  final TextEditingController _titleController = TextEditingController();
  final AdminBusinessController adminBusinessController =
      Get.find<AdminBusinessController>();
  final int _maxTitleLength = 50;

  bool _isLoading = false;
  bool isBusinessSelected = false;
  final BusinessController businessController = Get.find<BusinessController>();

  @override
  void initState() {
    super.initState();
    adminBusinessController.getAllAdminBusinessList().then((value) {
      if (value) {
        setState(() {
          isDisplayBusinessList = true;
        });
      } else {
        setState(() {
          isDisplayBusinessList = false;
        });
      }
    });

    if (isOfflineSelected.value) {
      createEventModel.eventType = "offline";
    }
    if (isTicketPaid.value) {
      createEventModel.ticketType = "paid";
    }
    if (isMultipleTicketYes.value) {
      createEventModel.multiplePricing = "yes";
    }
    businessController.businessProfileDetails();
    _descriptionController.addListener(_updateDescCharaCount);
    _titleController.addListener(_updatetitleCharaCount);
  }

  @override
  void dispose() {
    _descriptionController.removeListener(_updateDescCharaCount);
    _titleController.removeListener(_updatetitleCharaCount);
    _descriptionController.dispose();
    _titleController.dispose();
    super.dispose();
  }

  void _updateDescCharaCount() {
    setState(() {});
  }

  void _updatetitleCharaCount() {
    setState(() {});
  }

  void showLoadingIndicator() {
    setState(() {
      _isLoading = true;
    });
  }

  void hideLoadingIndicator() {
    setState(() {
      _isLoading = false;
    });
  }

  void _updateDuration() {
    DateTime startDateTime = getValidDateTime(createEventModel.startDate,
        defaultValue: DateTime.now());
    DateTime endDateTime = getValidDateTime(createEventModel.endDate,
        defaultValue: startDateTime.add(const Duration(minutes: 30)));

    Duration difference = endDateTime.difference(startDateTime);

    createEventModel.duration = difference.inMinutes;
  }

  void resetEventModel() {
    setState(() {
      createEventModel = EventModel(locationDetails: LocationDetails());
      selectedAddress = '';
      _selectedMinAge = null;
      _selectedMaxAge = null;
      isOfflineSelected.value = true;
      isOnlineSelected.value = false;
      isOfflineOnlineSelected.value = false;
      _imageUrl = '';
      _descriptionController.clear();
      _titleController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Container(
          width: Get.width * .7,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(
              color: AppColors.kgrey,
            ),
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        hoverColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        onTap: () {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return CustomDialog(
                                onConfirmTxt: "Yes, leave",
                                onCancelText: "No",
                                title: 'Leave this page?',
                                content:
                                    'Are you sure you want to leave this page? All field details will be discarded',
                                image: "assets/icons/WarningCircle.svg",
                                onConfirm: () {
                                  Navigator.of(context).pop();
                                  locator<NavigationServices>().goBack();
                                },
                                onCancel: () {
                                  Navigator.of(context).pop();
                                },
                              );
                            },
                          );
                        },
                        child: SvgPicture.asset('assets/icons/arrow-left.svg',
                            fit: BoxFit.fill, height: 32, width: 32),
                      ),
                      mdWidth,
                      Text("Create an Event", style: heading2TextRegular),
                    ],
                  ),
                ),
                const Divider(
                  thickness: 1.0,
                  color: AppColors.kgrey,
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            isDisplayBusinessList
                                ? CustomDropdownUI(
                                    isValidatorReq: true,
                                    title: "Organised by*",
                                    items: adminBusinessController
                                        .adminBusinessList
                                        .map((business) {
                                      // items: adminBusinessController
                                      //     .adminBusinessList
                                      //     .where(
                                      //         (business) => business.kycDone == 1)
                                      //     .map((business) {
                                      return DropdownMenuItem(
                                        onTap: () {
                                          setState(() {
                                            selectedBusinesID =
                                                business.businessId;
                                            resetEventModel();
                                            isBusinessSelected = true;
                                          });
                                          businessController
                                              .businessProfileDetails(
                                                  isBusiness: true,
                                                  businessID: selectedBusinesID
                                                      .toString())
                                              .then((_) {
                                            Future.delayed(const Duration(
                                                    milliseconds: 200))
                                                .then((value) {
                                              setState(() {});
                                            });
                                          });
                                        },
                                        value: business.businessName,
                                        child: Text(
                                          business.businessName.toString(),
                                          style: body2TextBold,
                                        ),
                                      );
                                    }).toList(),
                                    value: selectedBusinessName == ''
                                        ? null
                                        : selectedBusinessName,
                                    onChanged: (value) {
                                      setState(() {
                                        selectedBusinessName = value.toString();
                                      });
                                    },
                                  )
                                : const SizedBox(),
                            const Gap(20),
                            Visibility(
                              visible: isBusinessSelected,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Event name*',
                                    style: body2TextRegular,
                                  ),
                                  const Gap(10),
                                  CustomTextFormField(
                                    hintText: 'e.g Art attack',
                                    maxLength: 50,
                                    controller: _titleController,
                                    onChanged: (val) {
                                      createEventModel.title = val;
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Event Title Required';
                                      }
                                      return null;
                                    },
                                  ),
                                  const Gap(10),
                                  Align(
                                      alignment: Alignment.centerRight,
                                      child: Text(
                                          '${_titleController.text.length}/$_maxTitleLength characters',
                                          style: body3TextRegular)),
                                  const Gap(15),
                                  Text('Description', style: body2TextRegular),
                                  const Gap(10),
                                  CustomTextFormField(
                                    controller: _descriptionController,
                                    onChanged: (val) {
                                      createEventModel.description = val;
                                    },
                                    hintText: "Type here",
                                    maxLines: 60,
                                    maxLength: 500,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Event Desc. Required';
                                      }
                                      return null;
                                    },
                                  ),
                                  const Gap(10),
                                  Align(
                                      alignment: Alignment.centerRight,
                                      child: Text(
                                          '${_descriptionController.text.length}/$_maxLength characters',
                                          style: body3TextRegular)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: isBusinessSelected,
                        child: Column(
                          children: [
                            const Gap(20),
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("Event banner*",
                                      style: body2TextRegular),
                                  const Gap(10),
                                  _imageUrl.isEmpty
                                      ? DottedBorder(
                                          color: AppColors.bordergrey,
                                          strokeWidth: 1,
                                          dashPattern: const [6, 3],
                                          borderType: BorderType.RRect,
                                          radius: const Radius.circular(4),
                                          child: Container(
                                            height: 260,
                                            margin:
                                                const EdgeInsets.only(top: 10),
                                            padding: const EdgeInsets.all(16),
                                            decoration: BoxDecoration(
                                              color: AppColors.kwhite,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  const Icon(
                                                      Icons
                                                          .add_photo_alternate_outlined,
                                                      size: 60,
                                                      color: AppColors
                                                          .kprimarycolor),
                                                  const Gap(10),
                                                  Text(
                                                    "JPEG or PNG file formats supported in\n1080 x 1080px dimension, up to 1 MB",
                                                    style: body2TextRegular
                                                        .copyWith(
                                                            color: AppColors
                                                                .txtsecondary),
                                                  ),
                                                  const Gap(15),
                                                  TextButton(
                                                    child: Text(
                                                      'Upload Image',
                                                      textAlign:
                                                          TextAlign.center,
                                                      style:
                                                          bodyTextBold.copyWith(
                                                        color: const Color(
                                                            0xff5E57E1),
                                                      ),
                                                    ),
                                                    onPressed: () async {
                                                      log("Upload Image button pressed");
                                                      FilePickerResult? result =
                                                          await FilePicker
                                                              .platform
                                                              .pickFiles();

                                                      if (result != null) {
                                                        PlatformFile
                                                            pickedFile =
                                                            result.files.first;
                                                        Uint8List? fileBytes =
                                                            result.files.first
                                                                .bytes;
                                                        log("File selected: ${pickedFile.name}");
                                                        await _uploadFileHelper(
                                                            selectedBusinesID
                                                                .toString(),
                                                            pickedFile,
                                                            null,
                                                            fileBytes!);
                                                      } else {
                                                        log('No file selected.');
                                                      }
                                                    },
                                                  ),
                                                ]),
                                          ),
                                        )
                                      : Stack(
                                          children: [
                                            ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              child: Container(
                                                height: 260,
                                                width: 260,
                                                decoration: BoxDecoration(
                                                  image: DecorationImage(
                                                    image:
                                                        NetworkImage(_imageUrl),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Positioned(
                                              bottom: 5,
                                              right: 4,
                                              child: InkWell(
                                                hoverColor: Colors.transparent,
                                                splashColor: Colors.transparent,
                                                highlightColor:
                                                    Colors.transparent,
                                                overlayColor:
                                                    MaterialStateProperty.all(
                                                        Colors.transparent),
                                                onTap: () {
                                                  setState(() {
                                                    _imageUrl = '';
                                                  });
                                                },
                                                child: Container(
                                                  padding:
                                                      const EdgeInsets.all(6),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        AppColors.kprimarycolor,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                  ),
                                                  child: const Icon(
                                                    Icons.edit,
                                                    size: 20,
                                                    color: AppColors.kwhite,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: isBusinessSelected,
                  child: Column(
                    children: [
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: ShimmerWidget(
                                isLoading:
                                    eventController.isStartDateLoading.value,
                                child: DateTimeWidget(
                                  title1: "Start Date*",
                                  title2: "Start Time*",
                                  onTapDate: () {
                                    final DateTime now = DateTime.now();
                                    final DateTime minimumStartDate =
                                        now.add(const Duration(days: 2));
                                    showDatePicker(
                                      context: context,
                                      initialDate: minimumStartDate,
                                      firstDate: minimumStartDate,
                                      lastDate: DateTime(2101),
                                    ).then((selectedDate) {
                                      if (selectedDate != null) {
                                        setState(() {
                                          createEventModel.startDate =
                                              formatDateTime(selectedDate);
                                          // Check if the end date is now before the start date
                                          DateTime startDate = getValidDateTime(
                                              createEventModel.startDate,
                                              defaultValue: minimumStartDate);
                                          DateTime endDate = getValidDateTime(
                                              createEventModel.endDate,
                                              defaultValue: startDate.add(
                                                  const Duration(minutes: 30)));

                                          if (endDate.isBefore(startDate)) {
                                            // Set end date to start date + 30 minutes
                                            createEventModel.endDate =
                                                formatDateTime(startDate.add(
                                                    const Duration(
                                                        minutes: 30)));
                                          }
                                        });
                                      }
                                    });
                                  },

                                  //   eventController
                                  //       .selectDateFn(context, true)
                                  //       .then((value) {
                                  //     setState(() {
                                  // createEventModel.startDate =
                                  //     eventController.startDate.value;
                                  //     });
                                  //   });
                                  // },
                                  onTapTime: () {
                                    eventController
                                        .selectTimeFn(context, true)
                                        .then((value) {
                                      setState(() {
                                        DateTime originalDate =
                                            getValidDateTime(
                                                createEventModel.startDate,
                                                defaultValue: DateTime.now());
                                        List<String> parts = eventController
                                            .startTime.value
                                            .split(':');
                                        int hours = int.tryParse(parts[0]) ?? 0;
                                        int minutes =
                                            int.tryParse(parts[1]) ?? 0;
                                        Duration timeToAdd = Duration(
                                            hours: hours, minutes: minutes);
                                        DateTime newDateTime =
                                            originalDate.add(timeToAdd);

                                        createEventModel.startDate =
                                            formatDateTime(newDateTime);
                                      });
                                    });
                                  },
                                  selectedDate: createEventModel.startDate,
                                  selectedTime: createEventModel.startDate,
                                ),
                              ),
                            ),
                            const Gap(20),
                            Expanded(
                              child: ShimmerWidget(
                                isLoading:
                                    eventController.isEndDateLoading.value,
                                child: DateTimeWidget(
                                  title1: "End Date*",
                                  title2: "End Time*",
                                  onTapDate: () {
                                    DateTime startDate = getValidDateTime(
                                        createEventModel.startDate,
                                        defaultValue: DateTime.now());
                                    DateTime endDate = getValidDateTime(
                                        createEventModel.endDate,
                                        defaultValue: startDate
                                            .add(const Duration(minutes: 30)));

                                    showDatePicker(
                                      context: context,
                                      initialDate: endDate.isAfter(startDate)
                                          ? endDate
                                          : startDate
                                              .add(const Duration(days: 1)),
                                      firstDate: startDate,
                                      lastDate: DateTime(2101),
                                    ).then((selectedDate) {
                                      if (selectedDate != null) {
                                        setState(() {
                                          DateTime currentEndDateTime =
                                              getValidDateTime(
                                                  createEventModel.endDate,
                                                  defaultValue: endDate);
                                          DateTime newEndDateTime = DateTime(
                                            selectedDate.year,
                                            selectedDate.month,
                                            selectedDate.day,
                                            currentEndDateTime.hour,
                                            currentEndDateTime.minute,
                                          );
                                          createEventModel.endDate =
                                              formatDateTime(newEndDateTime);
                                        });
                                      }
                                    });
                                  },
                                  onTapTime: () {
                                    DateTime startDateTime = getValidDateTime(
                                        createEventModel.startDate,
                                        defaultValue: DateTime.now());
                                    DateTime currentEndDateTime =
                                        getValidDateTime(
                                            createEventModel.endDate,
                                            defaultValue: startDateTime.add(
                                                const Duration(minutes: 30)));

                                    DateTime minEndDateTime = startDateTime
                                        .add(const Duration(minutes: 30));

                                    TimeOfDay initialTime = currentEndDateTime
                                            .isAfter(minEndDateTime)
                                        ? TimeOfDay.fromDateTime(
                                            currentEndDateTime)
                                        : TimeOfDay.fromDateTime(
                                            minEndDateTime);

                                    showTimePicker(
                                      context: context,
                                      initialTime: initialTime,
                                      builder: (BuildContext context,
                                          Widget? child) {
                                        return MediaQuery(
                                          data: MediaQuery.of(context).copyWith(
                                              alwaysUse24HourFormat: false),
                                          child: child!,
                                        );
                                      },
                                    ).then((TimeOfDay? timeOfDay) {
                                      if (timeOfDay != null) {
                                        setState(() {
                                          DateTime selectedEndDateTime =
                                              DateTime(
                                            currentEndDateTime.year,
                                            currentEndDateTime.month,
                                            currentEndDateTime.day,
                                            timeOfDay.hour,
                                            timeOfDay.minute,
                                          );
                                          if (selectedEndDateTime
                                              .isBefore(minEndDateTime)) {
                                            selectedEndDateTime =
                                                minEndDateTime;
                                          }
                                          createEventModel.endDate =
                                              formatDateTime(
                                                  selectedEndDateTime);
                                        });
                                      }
                                    });
                                  },
                                  selectedDate: createEventModel.endDate,
                                  selectedTime: createEventModel.endDate,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Column(
                            children: [
                              Text("Duration", style: body2TextRegular),
                              const Gap(10),
                              GetBuilder<EventController>(
                                builder: (controller) {
                                  _updateDuration();
                                  String durationText = _formatDuration(
                                      Duration(
                                          minutes: createEventModel.duration));

                                  return Text(
                                    durationText,
                                    style: body2TextRegular.copyWith(
                                        fontWeight: FontWeight.bold),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Gap(10),
                      const Divider(thickness: 1.0, color: AppColors.kgrey),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // First set of controls (Event Type)
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("For age group*",
                                          style: body2TextRegular),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: CustomDropdownFormField(
                                              items: _ageOptions.map((age) {
                                                return DropdownMenuItem(
                                                  value: age,
                                                  child: Text(age),
                                                );
                                              }).toList(),
                                              value: _selectedMinAge,
                                              hintText: 'Min.age',
                                              onChanged: (value) {
                                                setState(() {
                                                  _selectedMinAge = value;
                                                  createEventModel.minAge =
                                                      int.parse(value!);
                                                  log("Selected min age: $value");
                                                });
                                              },
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Min Age Required';
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                          const Gap(10),
                                          Text(
                                            'To',
                                            style: body3TextRegular.copyWith(
                                                color: AppColors.txtsecondary),
                                          ),
                                          const SizedBox(width: 10),
                                          Expanded(
                                            child: CustomDropdownFormField(
                                              items: _maxAgeOptions.map((age) {
                                                return DropdownMenuItem(
                                                  value: age,
                                                  child: Text(age),
                                                );
                                              }).toList(),
                                              value: _selectedMaxAge,
                                              hintText: 'Max.age',
                                              onChanged: (value) {
                                                setState(() {
                                                  _selectedMaxAge = value;

                                                  if (_selectedMaxAge ==
                                                      "18+") {
                                                    createEventModel.maxAge =
                                                        19;
                                                    log("Selected max age: 19");
                                                  } else {
                                                    try {
                                                      int selectedMaxAge =
                                                          int.parse(value!);
                                                      int selectedMinAge =
                                                          _selectedMinAge ==
                                                                  "18+"
                                                              ? 18
                                                              : int.parse(
                                                                  _selectedMinAge!);

                                                      if (selectedMaxAge <=
                                                          selectedMinAge) {
                                                        CustomSnackBar.showError(
                                                            "Error",
                                                            "The maximum age should be greater than the minimum age.");
                                                      } else {
                                                        createEventModel
                                                                .maxAge =
                                                            selectedMaxAge;
                                                        log("Selected max age: ${createEventModel.maxAge}");
                                                      }
                                                    } catch (e) {
                                                      log("Error parsing age value: $e");
                                                      CustomSnackBar.showError(
                                                          "Error",
                                                          "Invalid age value.");
                                                    }
                                                  }
                                                });
                                              },
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Max Age Required';
                                                } else if (_selectedMinAge ==
                                                        "18+" &&
                                                    value == "18+") {
                                                  return null;
                                                } else if (value == "18+") {
                                                  return null;
                                                } else {
                                                  try {
                                                    int selectedMaxAge =
                                                        int.parse(value);
                                                    int selectedMinAge =
                                                        _selectedMinAge == "18+"
                                                            ? 18
                                                            : int.parse(
                                                                _selectedMinAge!);

                                                    if (selectedMaxAge <=
                                                        selectedMinAge) {
                                                      return "The maximum age should be greater than the minimum age.";
                                                    }
                                                  } catch (e) {
                                                    return "Invalid age value.";
                                                  }
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const Gap(20),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Event type*",
                                          style: body2TextRegular),
                                      const Gap(8),
                                      Row(
                                        children: [
                                          Flexible(
                                            child: InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              overlayColor:
                                                  MaterialStateProperty.all(
                                                      Colors.transparent),
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected.value =
                                                      true;
                                                  isOnlineSelected.value =
                                                      false;
                                                  isOfflineOnlineSelected
                                                      .value = false;
                                                  createEventModel.eventType =
                                                      "offline";
                                                });
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.circular(8),
                                                    right: Radius.zero,
                                                  ),
                                                  color: isOfflineSelected.value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                    color: isOfflineSelected
                                                            .value
                                                        ? AppColors.txtprimary
                                                        : AppColors.bordergrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Offline",
                                                    style: body2TextSemiBold.copyWith(
                                                        color: isOfflineOnlineSelected
                                                                .value
                                                            ? AppColors
                                                                .txtprimary
                                                            : AppColors
                                                                .txtsecondary),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              overlayColor:
                                                  MaterialStateProperty.all(
                                                      Colors.transparent),
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected.value =
                                                      false;
                                                  isOnlineSelected.value = true;
                                                  isOfflineOnlineSelected
                                                      .value = false;
                                                  createEventModel.eventType =
                                                      "online";
                                                });
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                    color:
                                                        isOnlineSelected.value
                                                            ? AppColors.kwhite
                                                            : AppColors
                                                                .scaffoldColor,
                                                    border: Border.all(
                                                      color: isOnlineSelected
                                                              .value
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .bordergrey,
                                                    )),
                                                child: Center(
                                                  child: Text("Online",
                                                      style: body2TextSemiBold.copyWith(
                                                          color: isOnlineSelected
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .txtsecondary)),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Flexible(
                                            child: InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              overlayColor:
                                                  MaterialStateProperty.all(
                                                      Colors.transparent),
                                              onTap: () {
                                                setState(() {
                                                  isOfflineSelected.value =
                                                      false;
                                                  isOnlineSelected.value =
                                                      false;
                                                  isOfflineOnlineSelected
                                                      .value = true;
                                                  createEventModel.eventType =
                                                      "offline + Online";
                                                });
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    right: Radius.circular(8),
                                                  ),
                                                  color: isOfflineOnlineSelected
                                                          .value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                      color:
                                                          isOfflineOnlineSelected
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .bordergrey),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                      "Offline + Online",
                                                      style: body2TextSemiBold.copyWith(
                                                          color: isOfflineOnlineSelected
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .txtsecondary)),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Visibility(
                            visible: !isOnlineSelected.value,
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("City*", style: body2TextRegular),
                                        const Gap(10),
                                        CustomDropDown(
                                          items:
                                              eventController.serviceableCity,
                                          hinttext: "Select City",
                                          onChanged: (value) {
                                            createEventModel.city = value!;
                                          },
                                          initialValue: null,
                                          onSaved: (p0) {},
                                          onTap: () {},
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'City Required';
                                            }
                                            return null;
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Gap(30),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Obx(
                                          () => _isLoading
                                              ? const SizedBox.shrink()
                                              : CustomDropdownUI(
                                                  isValidatorReq: true,
                                                  title: "Address",
                                                  items: businessController
                                                      .userModel.value.location
                                                      .map((location) {
                                                    return DropdownMenuItem(
                                                      onTap: () {
                                                        var locationDetails = LocationDetails(
                                                            area: location.area,
                                                            city: location.city,
                                                            state: location
                                                                .state,
                                                            address: location
                                                                .address,
                                                            country: location
                                                                .country,
                                                            latitude: double
                                                                .parse(location
                                                                    .latitude),
                                                            pinCode: location
                                                                .pinCode,
                                                            longitude: double
                                                                .parse(location
                                                                    .longitude),
                                                            locationId: location
                                                                .locationId,
                                                            subLocality: location
                                                                .subLocality);

                                                        createEventModel
                                                                .locationDetails =
                                                            locationDetails;

                                                        eventController
                                                                .eventDetailsModel
                                                                .value
                                                                .address =
                                                            location.address;
                                                      },
                                                      value: location.address,
                                                      child: Text(
                                                        location.address
                                                            .toString(),
                                                        style: body2TextBold,
                                                      ),
                                                    );
                                                  }).toList(),
                                                  value: selectedAddress == ''
                                                      ? null
                                                      : selectedAddress,
                                                  onChanged: (value) {
                                                    setState(() {
                                                      selectedAddress =
                                                          value.toString();
                                                    });
                                                  },
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Ticket Type*",
                                          style: body2TextRegular),
                                      const Gap(8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              overlayColor:
                                                  MaterialStateProperty.all(
                                                      Colors.transparent),
                                              onTap: () {
                                                setState(() {
                                                  isTicketPaid.value = true;
                                                  isTicketFree.value = false;
                                                  createEventModel.ticketType =
                                                      "paid";
                                                });
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.circular(12),
                                                  ),
                                                  color: isTicketPaid.value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                    color: isTicketPaid.value
                                                        ? AppColors.txtprimary
                                                        : AppColors.bordergrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Paid",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                            color: isTicketPaid
                                                                    .value
                                                                ? AppColors
                                                                    .txtprimary
                                                                : AppColors
                                                                    .txtsecondary),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
                                              hoverColor: Colors.transparent,
                                              splashColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              overlayColor:
                                                  MaterialStateProperty.all(
                                                      Colors.transparent),
                                              onTap: () {
                                                setState(() {
                                                  isTicketPaid.value = false;
                                                  isTicketFree.value = true;
                                                  createEventModel.ticketType =
                                                      "free";
                                                });
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    right: Radius.circular(12),
                                                  ),
                                                  color: isTicketFree.value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                    color: isTicketFree.value
                                                        ? AppColors.txtprimary
                                                        : AppColors.bordergrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Free",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                            color: isTicketFree
                                                                    .value
                                                                ? AppColors
                                                                    .txtprimary
                                                                : AppColors
                                                                    .txtsecondary),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const Gap(30),
                                isTicketFree.value
                                    ? const Spacer()
                                    : Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                                "Do you have multiple ticket pricing?*",
                                                style: body2TextRegular),
                                            const Gap(8),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: InkWell(
                                                    hoverColor:
                                                        Colors.transparent,
                                                    splashColor:
                                                        Colors.transparent,
                                                    highlightColor:
                                                        Colors.transparent,
                                                    overlayColor:
                                                        MaterialStateProperty
                                                            .all(Colors
                                                                .transparent),
                                                    onTap: () {
                                                      setState(() {
                                                        isMultipleTicketYes
                                                            .value = true;
                                                        isMultipleTicketNo
                                                            .value = false;
                                                        createEventModel
                                                                .multiplePricing =
                                                            "yes";
                                                      });
                                                    },
                                                    child: Container(
                                                      height: 42,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            const BorderRadius
                                                                .horizontal(
                                                          left: Radius.circular(
                                                              8),
                                                        ),
                                                        color: isMultipleTicketYes
                                                                .value
                                                            ? AppColors.kwhite
                                                            : AppColors
                                                                .scaffoldColor,
                                                        border: Border.all(
                                                          color: isMultipleTicketYes
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .bordergrey,
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text("Yes",
                                                            style: body2TextSemiBold.copyWith(
                                                                color: isMultipleTicketYes
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary)),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  child: InkWell(
                                                    hoverColor:
                                                        Colors.transparent,
                                                    splashColor:
                                                        Colors.transparent,
                                                    highlightColor:
                                                        Colors.transparent,
                                                    overlayColor:
                                                        MaterialStateProperty
                                                            .all(Colors
                                                                .transparent),
                                                    onTap: () {
                                                      setState(() {
                                                        isMultipleTicketYes
                                                            .value = false;
                                                        isMultipleTicketNo
                                                            .value = true;
                                                        createEventModel
                                                                .multiplePricing =
                                                            "no";
                                                      });
                                                    },
                                                    child: Container(
                                                      height: 42,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            const BorderRadius
                                                                .horizontal(
                                                          right:
                                                              Radius.circular(
                                                                  12),
                                                        ),
                                                        color: isMultipleTicketNo
                                                                .value
                                                            ? AppColors.kwhite
                                                            : AppColors
                                                                .scaffoldColor,
                                                        border: Border.all(
                                                          color: isMultipleTicketNo
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .bordergrey,
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text("No",
                                                            style: body2TextSemiBold.copyWith(
                                                                color: isMultipleTicketNo
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary)),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                              ],
                            ),
                          ),
                          isTicketFree.value
                              ? const SizedBox()
                              : Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("Price starts at*",
                                          style: body2TextRegular),
                                      const Gap(10),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: CustomTextFormField(
                                              initialValue: "",
                                              onChanged: (val) {
                                                int price =
                                                    int.tryParse(val) ?? 0;
                                                createEventModel.price = price;
                                              },
                                              hintText: "Enter amount",
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Amount Required';
                                                }
                                                return null;
                                              },
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              maxLength: 8,
                                              prefixIcon: const Padding(
                                                padding:
                                                    EdgeInsets.only(right: 8),
                                                child: Icon(
                                                    Icons.currency_rupee,
                                                    size: 20,
                                                    color: AppColors
                                                        .bottomlightgrey),
                                              ),
                                            ),
                                          ),
                                          const Spacer()
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                          const Gap(10),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Bookings", style: title3TextSemiBold),
                                const Gap(30),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text("Preferred ticket booking*",
                                              style: body2TextRegular),
                                          const Gap(8),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: InkWell(
                                                  hoverColor:
                                                      Colors.transparent,
                                                  splashColor:
                                                      Colors.transparent,
                                                  highlightColor:
                                                      Colors.transparent,
                                                  overlayColor:
                                                      MaterialStateProperty.all(
                                                          Colors.transparent),
                                                  onTap: () {
                                                    setState(() {
                                                      isWebUrl.value = true;
                                                      isWhatsAppNumber.value =
                                                          false;
                                                      createEventModel
                                                          .ctaMobile = "";
                                                    });
                                                  },
                                                  child: Container(
                                                    height: 42,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          const BorderRadius
                                                              .horizontal(
                                                        left:
                                                            Radius.circular(12),
                                                      ),
                                                      color: isWebUrl.value
                                                          ? AppColors.kwhite
                                                          : AppColors
                                                              .scaffoldColor,
                                                      border: Border.all(
                                                        color: isWebUrl.value
                                                            ? AppColors
                                                                .txtprimary
                                                            : AppColors
                                                                .bordergrey,
                                                      ),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        "Web URL",
                                                        style: body2TextSemiBold
                                                            .copyWith(
                                                                color: isWebUrl
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                child: InkWell(
                                                  hoverColor:
                                                      Colors.transparent,
                                                  splashColor:
                                                      Colors.transparent,
                                                  highlightColor:
                                                      Colors.transparent,
                                                  overlayColor:
                                                      MaterialStateProperty.all(
                                                          Colors.transparent),
                                                  onTap: () {
                                                    setState(() {
                                                      isWebUrl.value = false;
                                                      isWhatsAppNumber.value =
                                                          true;
                                                      createEventModel.ctaUrl =
                                                          "";
                                                    });
                                                  },
                                                  child: Container(
                                                    height: 42,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          const BorderRadius
                                                              .horizontal(
                                                        right:
                                                            Radius.circular(12),
                                                      ),
                                                      color: isWhatsAppNumber
                                                              .value
                                                          ? AppColors.kwhite
                                                          : AppColors
                                                              .scaffoldColor,
                                                      border: Border.all(
                                                        color: isWhatsAppNumber
                                                                .value
                                                            ? AppColors
                                                                .txtprimary
                                                            : AppColors
                                                                .bordergrey,
                                                      ),
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        "WhatsApp number",
                                                        style: body2TextSemiBold
                                                            .copyWith(
                                                                color: isWhatsAppNumber
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Gap(30),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              isWebUrl.value
                                                  ? "Booking URL*"
                                                  : "WhatsApp Number*",
                                              style: body2TextRegular),
                                          const Gap(8),
                                          CustomTextFormField(
                                            onChanged: (val) {
                                              if (isWebUrl.value) {
                                                createEventModel.ctaUrl = val;
                                              } else {
                                                createEventModel.ctaMobile =
                                                    val;
                                              }
                                            },
                                            inputFormatters: isWebUrl.value
                                                ? []
                                                : [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return isWebUrl.value
                                                    ? 'URL Required'
                                                    : 'WhatsApp Number Required';
                                              }

                                              return null;
                                            },
                                            hintText: isWebUrl.value
                                                ? "e.g https://www.google.com"
                                                : "+91-9001122445",
                                            maxLength:
                                                isWebUrl.value ? null : 10,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Gap(30),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const Expanded(
                                  child: Row(
                                    children: [
                                      Icon(Icons.info_outline),
                                      Gap(8),
                                      Text(
                                          "Please fill out all required fields to submit for review"),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(right: 16.0),
                                        child: SecondaryButton(
                                          textColor: AppColors.kprimarycolor,
                                          backgroundColor: AppColors.kwhite,
                                          text: 'Save as draft',
                                          onTap: () async {
                                            if (businessController
                                                    .userModel.value.kycDone ==
                                                1) {
                                              Map<String, dynamic>
                                                  draftEventPayload = {
                                                "business_id":
                                                    selectedBusinesID,
                                                "title": createEventModel.title,
                                                "banner_url": _imageUrl,
                                                "price": createEventModel.price,
                                                "description": createEventModel
                                                    .description,
                                                "duration":
                                                    createEventModel.duration,
                                                "cta_url":
                                                    createEventModel.ctaUrl,
                                                "event_type":
                                                    createEventModel.eventType,
                                                "city": createEventModel.city,
                                                "location_details": {
                                                  "area": createEventModel
                                                      .locationDetails.area,
                                                  "city": createEventModel
                                                      .locationDetails.city,
                                                  "state": createEventModel
                                                      .locationDetails.state,
                                                  "address": createEventModel
                                                      .locationDetails.address,
                                                  "country": createEventModel
                                                      .locationDetails.country,
                                                  "latitude": createEventModel
                                                      .locationDetails.latitude,
                                                  "pin_code": createEventModel
                                                      .locationDetails.pinCode,
                                                  "longitude": createEventModel
                                                      .locationDetails
                                                      .longitude,
                                                  "location_id":
                                                      createEventModel
                                                          .locationDetails
                                                          .locationId,
                                                  "sub_locality":
                                                      createEventModel
                                                          .locationDetails
                                                          .subLocality
                                                },
                                                "ticket_type":
                                                    createEventModel.ticketType,
                                                "cta_mobile":
                                                    createEventModel.ctaMobile,
                                                "multiple_pricing":
                                                    createEventModel
                                                        .multiplePricing,
                                                "min_age":
                                                    createEventModel.minAge,
                                                "publish": 0,
                                                "max_age":
                                                    createEventModel.maxAge,
                                                "start_date":
                                                    createEventModel.startDate,
                                                "end_date":
                                                    createEventModel.endDate,
                                                "status": "draft"
                                              };

                                              createEvent(
                                                  draftEventPayload, "draft");
                                            }
                                          },
                                        ),
                                      ),
                                      const Gap(20),
                                      PrimaryButton(
                                        text: 'Submit for review',
                                        textColor: AppColors.kwhite,
                                        backgroundColor:
                                            AppColors.kprimarycolor,
                                        onTap: () async {
                                          Map<String, dynamic>
                                              reviewEventPayload = {
                                            "business_id": selectedBusinesID,
                                            "title": createEventModel.title,
                                            "banner_url": _imageUrl,
                                            "price": createEventModel.price,
                                            "description":
                                                createEventModel.description,
                                            "duration":
                                                createEventModel.duration,
                                            "cta_url": createEventModel.ctaUrl,
                                            "event_type":
                                                createEventModel.eventType,
                                            "city": createEventModel.city,
                                            "location_details": {
                                              "area": createEventModel
                                                  .locationDetails.area,
                                              "city": createEventModel
                                                  .locationDetails.city,
                                              "state": createEventModel
                                                  .locationDetails.state,
                                              "address": createEventModel
                                                  .locationDetails.address,
                                              "country": createEventModel
                                                  .locationDetails.country,
                                              "latitude": createEventModel
                                                  .locationDetails.latitude,
                                              "pin_code": createEventModel
                                                  .locationDetails.pinCode,
                                              "longitude": createEventModel
                                                  .locationDetails.longitude,
                                              "location_id": createEventModel
                                                  .locationDetails.locationId,
                                              "sub_locality": createEventModel
                                                  .locationDetails.subLocality
                                            },
                                            "ticket_type":
                                                createEventModel.ticketType,
                                            "cta_mobile":
                                                createEventModel.ctaMobile,
                                            "multiple_pricing": createEventModel
                                                .multiplePricing,
                                            "min_age": createEventModel.minAge,
                                            "publish": 1,
                                            "max_age": createEventModel.maxAge,
                                            "start_date":
                                                createEventModel.startDate,
                                            "end_date":
                                                createEventModel.endDate,
                                            "status": "inreview"
                                          };

                                          createEvent(
                                              reviewEventPayload, "inreview");
                                        },
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    int days = duration.inDays;
    int hours = duration.inHours % 24;
    int minutes = duration.inMinutes % 60;

    List<String> parts = [];

    if (days > 0) {
      parts.add('$days ${days == 1 ? 'day' : 'days'}');
    }
    if (hours > 0 || days > 0) {
      // Always show hours if there are days
      parts.add('$hours ${hours == 1 ? 'hour' : 'hours'}');
    }
    if (minutes > 0 || hours > 0 || days > 0) {
      // Always show minutes
      parts.add('$minutes ${minutes == 1 ? 'minute' : 'minutes'}');
    }

    if (parts.isEmpty) {
      return '0 minutes';
    }

    return parts.join(' ');
  }

  createEvent(Map<String, dynamic> payload, String type) async {
    if (_formKey.currentState?.validate() ?? false) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return ConfirmPopup(
            dialogHeight: type == "inreview" ? 220 : 220,
            dialogWidth: 300,
            title: type == "inreview" ? 'Submit for review' : 'Save as draft',
            message: type == "inreview"
                ? 'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.'
                : 'Are you sure you want to save this as a draft?',
            onConfirm: () async {
              log(type == "draft" ? "Submit for review" : "Save as draft");
              bool isSuccess = await eventController.createEvent(payload);
              if (isSuccess) {
                await Future.delayed(const Duration(milliseconds: 300))
                    .then((value) {
                  Navigator.pop(Get.context!);
                  locator<NavigationServices>().goBack();
                  CustomSnackBar.showInfo(
                      "Success",
                      type == "inreview"
                          ? "Event submited for review."
                          : "Saved as draft.");
                });
              } else {
                CustomSnackBar.showError(
                  "Failed",
                  type == "inreview"
                      ? "Failed to submit for review. Please try again.."
                      : "Failed to save as draft. Please try again.",
                );
              }
            },
            icon: SvgPicture.asset(
              type == "inreview"
                  ? 'assets/icons/Checks.svg'
                  : 'assets/icons/PencilLine.svg',
              height: 50,
              fit: BoxFit.fill,
            ),
            confirmText: type == "inreview" ? 'Submit' : 'Save as draft',
            cancelText: 'Cancel',
          );
        },
      );
    } else {
      CustomSnackBar.showError(
        "Error",
        "All field is required.",
      );
    }
  }

  Future<void> _uploadFileHelper(String businessId, PlatformFile pickedFile,
      File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];
    if (!allowedExtensions.contains(fileExtension)) {
      CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
      );
      return;
    }

    if (pickedFile.size > 1 * 1024 * 1024) {
      CustomSnackBar.showError(
        "Error",
        "File size exceeds 1 MB limit.",
      );
      return;
    }

    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath,Content Type $contentType");
    bool value = await eventController.createFileNameEntry(
        businessID: businessId,
        pickedFile.name,
        contentType,
        filePath,
        fileBytes,
        "banner");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        _imageUrl = newImageUrl;
        createEventModel.bannerUrl = newImageUrl;
        log("_imageUrl set to: $newImageUrl");
      });

      CustomSnackBar.showInfo("Success", "Event Banner uploaded successfully");
    } else {
      log("unable to upload the image");
      CustomSnackBar.showError(
        "Failed",
        "Unable to upload event banner... try again later",
      );
    }
  }
}
