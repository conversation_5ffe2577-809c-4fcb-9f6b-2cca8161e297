import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';

class QuickActionContainer extends StatelessWidget {
  final String iconPath;
  final String menuTitle;

  const QuickActionContainer({
    super.key,
    required this.iconPath,
    required this.menuTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      width: 200,
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(9),
        border: Border.all(color: AppColors.placeHolder, width: 1.0),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(iconPath),
            const Gap(15),
            Text(menuTitle,style: title3TextRegular,),
          ],
        ),
      ),
    );
  }
}

class HomePageOverViewSection extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  final Function(int index, List<Map<String, dynamic>> data) onTapGenerator;

  const HomePageOverViewSection(
      {super.key, required this.data, required this.onTapGenerator});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.only(top: 16),
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
        // childAspectRatio: 3,
        mainAxisExtent: 150,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        return HomePageOverViewCard(
          iconPath: data[index]['iconPath'],
          title: data[index]['title'],
          subtitle: data[index]['subtitle'],
          pendingCount: data[index]['pendingCount'],
          isParents: data[index]["parents"],
          onTap: onTapGenerator(index, data),
        );
      },
    );
  }
}

class HomePageOverViewCard extends StatelessWidget {
  final String iconPath;
  final String title;
  final String subtitle;
  final int pendingCount;
  final bool isParents;
  final void Function() onTap;

  const HomePageOverViewCard({
    super.key,
    required this.iconPath,
    required this.title,
    required this.subtitle,
    required this.pendingCount,
    required this.isParents,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.kblack.withOpacity(.2), width: 1.0),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleIconsFilled(
                iconPath: iconPath,
                title: title,
                isParents: isParents,
              ),
            ],
          ),
          const Gap(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "$pendingCount pending",
                style: body2TextRegular.copyWith(color: AppColors.errorRed),
              ),
              SecondaryButton(
                text: "Review",
                onTap: onTap,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class CircleIconsFilled extends StatelessWidget {
  const CircleIconsFilled({
    super.key,
    required this.iconPath,
    required this.title,
    required this.isParents,
  });

  final String iconPath;
  final String title;
  final bool isParents;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: AppColors.kwhite,
            border: Border.all(color: AppColors.kblack.withOpacity(.5)),
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(iconPath),
        ),
        const Gap(10),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: bodyTextRegular),
            const Gap(5),
            Container(
              height: 18,
              width: 70,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: isParents
                    ? AppColors.ksecondary3
                    : AppColors.kSecondaryColor1,
              ),
              child: Text(isParents ? "Parents" : "Business",
                  style: body3TextRegular),
            ),
          ],
        ),
      ],
    );
  }
}
