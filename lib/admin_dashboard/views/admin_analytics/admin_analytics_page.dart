import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';

class AdminAnalyticsPage extends StatefulWidget {
  const AdminAnalyticsPage({super.key});

  @override
  State<AdminAnalyticsPage> createState() => _AdminAnalyticsPageState();
}

class _AdminAnalyticsPageState extends State<AdminAnalyticsPage> {
  final HomePageController homePageController = Get.find<HomePageController>();
  String selectedCity = 'all';
  String selectedPeriod = 'weekly';
  String selectedClassType = 'all';
  String selectedEventType = 'all';

  @override
  void initState() {
    super.initState();
    homePageController.getAnalyticsGraphData(
        selectedCity, selectedPeriod, selectedClassType, selectedEventType);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      appBar: AppBar(
        title: Row(
          children: [
            Text(
              'Users As on:- ',
              style: heading2TextRegular,
            ),
            const Gap(10),
            Text(
              '${DateFormat('d MMM yyyy').format(DateTime.now())} ${DateFormat.jm().format(DateTime.now())}',
              style: title2TextMedium,
            )
          ],
        ),
        automaticallyImplyLeading: false,
        centerTitle: false,
        backgroundColor: AppColors.kwhite,
        surfaceTintColor: AppColors.kwhite,
      ),
      body: Obx(() {
        if (homePageController.isAnalyticsDataLoading.value) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFilterRow(),
              const SizedBox(height: 16),
              GridView.count(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildChartCard(
                    'Total users (parents)',
                    _calculateTotal(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.parentsAnalytics,
                        'new_parents'),
                    _calculatePercentageChange('Total users (parents)'),
                    _buildLineChart(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.parentsAnalytics,
                        'new_parents'),
                  ),
                  _buildChartCard(
                    'Total businesses',
                    _calculateTotal(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.businessAnalytics,
                        'new_business'),
                    _calculatePercentageChange('Total businesses'),
                    _buildLineChart(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.businessAnalytics,
                        'new_business'),
                  ),
                  _buildChartCard(
                    'Total Events',
                    _calculateTotal(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.eventsAnalytics,
                        'new_events'),
                    _calculatePercentageChange('Total Events'),
                    _buildLineChart(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.eventsAnalytics,
                        'new_events'),
                    dropdownValue: 'All events',
                  ),
                  _buildChartCard(
                    'Total Classes',
                    _calculateTotal(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.classesAnalytics,
                        'new_classes'),
                    _calculatePercentageChange('Total Classes'),
                    _buildLineChart(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.classesAnalytics,
                        'new_classes'),
                    dropdownValue: 'Online classes',
                  ),
                  _buildChartCard(
                    'Total matches',
                    _calculateTotal(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.connectionsAnalytics,
                        'new_connections'),
                    _calculatePercentageChange('Total matches'),
                    _buildLineChart(
                        homePageController.analyticsGraphData.value
                            .analyticsResult.connectionsAnalytics,
                        'new_connections'),
                  ),
                  _buildChartCard(
                    'Gender specific users',
                    '',
                    '',
                    GenderPieChartData(selectedCity: selectedCity),
                  ),
                  _buildChartCard(
                    'User status',
                    '',
                    '',
                    UserStatusPieChart(selectedCity: selectedCity),
                  ),
                ],
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildFilterRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildDropdown(
          hintText: "All cities",
          value: selectedCity,
          items: ['all', 'mumbai', 'hyderabad', 'bangalore', 'pune'],
          onChanged: (value) {
            setState(() {
              selectedCity = value!;
              homePageController.getAnalyticsGraphData(selectedCity,
                  selectedPeriod, selectedClassType, selectedEventType);
            });
          },
        ),
        Text("Last 7 days Data", style: heading2TextRegular)
        // _buildDropdown(
        //   value: selectedPeriod,
        //   items: ['all', 'daily', 'weekly', 'monthly'],
        //   onChanged: (value) {
        //     setState(() {
        //       selectedPeriod = value!;
        //       homePageController.getAnalyticsGraphData(selectedCity, selectedPeriod, selectedClassType, selectedEventType);
        //     });
        //   },
        // ),
      ],
    );
  }

  Widget _buildDropdown({
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
    required String hintText,
  }) {
    return SizedBox(
      width: 200,
      child: CustomDropdownFormField(
        items: items
            .map((item) => DropdownMenuItem(
                value: item,
                child: Text(item.capitalizeFirst.toString(),
                    overflow: TextOverflow.ellipsis)))
            .toList(),
        hintText: hintText,
        onChanged: onChanged,
        textStyle: bodyTextRegular,
      ),
    );
  }

  Widget _buildChartCard(
      String title, String value, String change, Widget chart,
      {String? dropdownValue}) {
    return Card(
      color: AppColors.kwhite,
      surfaceTintColor: AppColors.kwhite,
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: bodyTextMedium),
                if (dropdownValue != null)
                  _buildDropdown(
                    hintText:
                        title.contains('Events') ? "All events" : "All classes",
                    value: dropdownValue,
                    items: title.contains('Events')
                        ? [
                            'All events',
                            'Online events',
                            'Offline events',
                            'Offline + Online'
                          ]
                        : [
                            'All classes',
                            'Online classes',
                            'Offline classes',
                            'Offline + Online'
                          ],
                    onChanged: (value) {
                      setState(() {
                        if (title.contains('Events')) {
                          selectedEventType =
                              value!.toLowerCase().replaceAll(' events', '');
                          if (selectedEventType == 'all events') {
                            selectedEventType = 'all';
                          }
                        } else {
                          selectedClassType =
                              value!.toLowerCase().replaceAll(' classes', '');
                          if (selectedClassType == 'all classes') {
                            selectedClassType = 'all';
                          }
                        }
                        homePageController.getAnalyticsGraphData(
                            selectedCity,
                            selectedPeriod,
                            selectedClassType,
                            selectedEventType);
                      });
                    },
                  ),
              ],
            ),
            const Gap(10),
            Row(
              children: [
                Text(value, style: bodyTextSemiBold),
                const SizedBox(width: 8),
                Text(
                  change,
                  style: bodyTextRegular.copyWith(
                    color: _calculatePercentageChange(title).startsWith('-')
                        ? Colors.red
                        : Colors.green,
                  ),
                ),
              ],
            ),
            const Gap(15),
            Expanded(
              child: value == '0'
                  ? const Center(child: Text('No data available'))
                  : LayoutBuilder(
                      builder: (context, constraints) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: SizedBox(
                            width: constraints.maxWidth,
                            height: constraints.maxHeight,
                            child: title == 'Gender specific users'
                                ? GenderPieChartData(selectedCity: selectedCity)
                                : title == 'User status'
                                    ? UserStatusPieChart(
                                        selectedCity: selectedCity)
                                    : chart,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  String _calculateTotal(List<dynamic>? data, String key) {
    if (data == null || data.isEmpty) return '0';
    return data
        .fold<int>(0, (sum, item) => sum + item.toJson()[key] as int)
        .toString();
  }

  String _calculatePercentageChange(String title) {
    List<dynamic>? data;
    String key;

    switch (title) {
      case 'Total users (parents)':
        data = homePageController
            .analyticsGraphData.value.analyticsResult.parentsAnalytics;
        key = 'new_parents';
        break;
      case 'Total businesses':
        data = homePageController
            .analyticsGraphData.value.analyticsResult.businessAnalytics;
        key = 'new_business';
        break;
      case 'Total Events':
        data = homePageController
            .analyticsGraphData.value.analyticsResult.eventsAnalytics;
        key = 'new_events';
        break;
      case 'Total Classes':
        data = homePageController
            .analyticsGraphData.value.analyticsResult.classesAnalytics;
        key = 'new_classes';
        break;
      case 'Total matches':
        data = homePageController
            .analyticsGraphData.value.analyticsResult.connectionsAnalytics;
        key = 'new_connections';
        break;
      default:
        return '0%';
    }

    if (data == null || data.length < 2) return '0%';

    double firstValue = data.first.toJson()[key].toDouble();
    double lastValue = data.last.toJson()[key].toDouble();

    if (firstValue == 0) return '100%';

    double percentageChange = ((lastValue - firstValue) / firstValue) * 100;
    return '${percentageChange.toStringAsFixed(1)}%';
  }

  // String _calculatePercentageChange(String title) {
  //   List<dynamic>? data;
  //   String key;

  //   switch (title) {
  //     case 'Total users (parents)':
  //       data = homePageController
  //           .analyticsGraphData.value.analyticsResult.parentsAnalytics;
  //       key = 'new_parents';
  //       break;
  //     case 'Total businesses':
  //       data = homePageController
  //           .analyticsGraphData.value.analyticsResult.businessAnalytics;
  //       key = 'new_business';
  //       break;
  //     case 'Total Events':
  //       data = homePageController
  //           .analyticsGraphData.value.analyticsResult.eventsAnalytics;
  //       key = 'new_events';
  //       break;
  //     case 'Total Classes':
  //       data = homePageController
  //           .analyticsGraphData.value.analyticsResult.classesAnalytics;
  //       key = 'new_classes';
  //       break;
  //     case 'Total matches':
  //       data = homePageController
  //           .analyticsGraphData.value.analyticsResult.connectionsAnalytics;
  //       key = 'new_connections';
  //       break;
  //     default:
  //       return '0%';
  //   }

  //   if (data == null || data.isEmpty) return '0%';

  //   // Calculate the average of the first half of the data
  //   int midPoint = data.length ~/ 2;
  //   double firstHalfAverage = data
  //           .sublist(0, midPoint)
  //           .fold<double>(0, (sum, item) => sum + item.toJson()[key]) /
  //       midPoint;

  //   // Calculate the average of the second half of the data
  //   double secondHalfAverage = data
  //           .sublist(midPoint)
  //           .fold<double>(0, (sum, item) => sum + item.toJson()[key]) /
  //       (data.length - midPoint);

  //   if (firstHalfAverage == 0) return '100%';

  //   double percentageChange =
  //       ((secondHalfAverage - firstHalfAverage) / firstHalfAverage) * 100;
  //   return '${percentageChange.toStringAsFixed(1)}%';
  // }

  Widget _buildLineChart(List<dynamic> data, String key) {
    if (data.isEmpty) {
      return const Center(child: Text('No data available'));
    }

    final spots = data.asMap().entries.map((entry) {
      final index = entry.key.toDouble();
      final value = entry.value.toJson()[key].toDouble();
      return FlSpot(index, value);
    }).toList();

    // Sort spots by x value (date)
    spots.sort((a, b) => a.x.compareTo(b.x));

    if (spots.length == 1) {
      spots.add(FlSpot(1, spots[0].y));
    }

    double minY = spots.map((spot) => spot.y).reduce(min);
    double maxY = spots.map((spot) => spot.y).reduce(max);

    // Ensure there's always some vertical range
    if (minY == maxY) {
      minY = minY * 0.9;
      maxY = maxY * 1.1;
    }

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: false),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < data.length) {
                  final date = DateTime.parse(data[value.toInt()].date);
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      DateFormat('d MMM').format(date),
                      style: body3TextRegular.copyWith(fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        minX: 0,
        maxX: spots.length - 1.0,
        minY: minY - (maxY - minY) * 0.1,
        maxY: maxY + (maxY - minY) * 0.1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: spots.length > 2,
            curveSmoothness: 0.3,
            color: AppColors.kprimarycolor,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(show: spots.length <= 5),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppColors.kSecondaryColor1,
                  AppColors.kSecondaryColor1.withOpacity(0.1)
                ],
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          handleBuiltInTouches: true,
          touchTooltipData: LineTouchTooltipData(
            // tooltipBgColor: Colors.blueAccent,
            getTooltipColor: (touchedSpot) => AppColors.lightpurple,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final flSpot = barSpot;
                if (flSpot.x.toInt() >= 0 && flSpot.x.toInt() < data.length) {
                  final date = DateTime.parse(data[flSpot.x.toInt()].date);
                  return LineTooltipItem(
                    '${DateFormat('d MMM').format(date)}\n${flSpot.y.toStringAsFixed(0)}',
                    const TextStyle(color: AppColors.txtprimary),
                  );
                }
                return null;
              }).toList();
            },
          ),
        ),
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

class GenderPieChartData extends StatelessWidget {
  final String selectedCity;

  const GenderPieChartData({super.key, required this.selectedCity});

  @override
  Widget build(BuildContext context) {
    final HomePageController homePageController =
        Get.find<HomePageController>();

    return Obx(() {
      final genderData = homePageController
          .analyticsGraphData.value.analyticsResult.genderAnalytics;
      final total = genderData.male + genderData.female;
      final maleCount = genderData.male;
      final femaleCount = genderData.female;
      final malePercentage = total > 0 ? (maleCount / total * 100).round() : 0;
      final femalePercentage =
          total > 0 ? (femaleCount / total * 100).round() : 0;

      return total > 0
          ? Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLegendItem("Male", AppColors.purplecolor),
                    const Gap(20),
                    _buildLegendItem("Female", AppColors.ksecondary3),
                  ],
                ),
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 1.3,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 0,
                        centerSpaceRadius: 0,
                        sections: [
                          PieChartSectionData(
                            color: AppColors.purplecolor,
                            value: malePercentage.toDouble(),
                            title: '$maleCount ($malePercentage%)',
                            radius: 140.0,
                            titleStyle: body2TextRegular.copyWith(
                              fontSize: 16.0,
                              color: AppColors.kwhite,
                            ),
                          ),
                          PieChartSectionData(
                            color: AppColors.ksecondary3,
                            value: femalePercentage.toDouble(),
                            title: '$femaleCount ($femalePercentage%)',
                            radius: 140.0,
                            titleStyle: body2TextRegular.copyWith(
                              fontSize: 16.0,
                              color: AppColors.kwhite,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )
          : const Center(child: Text("No Data Available"));
    });
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(height: 30, width: 12, color: color),
        const Gap(5),
        Text(label, style: body2TextRegular)
      ],
    );
  }
}

class UserStatusPieChart extends StatelessWidget {
  final String selectedCity;

  const UserStatusPieChart({super.key, required this.selectedCity});

  @override
  Widget build(BuildContext context) {
    final HomePageController homePageController =
        Get.find<HomePageController>();

    return Obx(() {
      final statusData = homePageController
          .analyticsGraphData.value.analyticsResult.userStatusAnalytics;
      final total = statusData.active + statusData.inactive;
      final activeCount = statusData.active;
      final inactiveCount = statusData.inactive;
      final activePercentage =
          total > 0 ? (activeCount / total * 100).round() : 0;
      final inactivePercentage =
          total > 0 ? (inactiveCount / total * 100).round() : 0;

      return total > 0
          ? Row(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLegendItem("Active users", AppColors.purplecolor),
                    const Gap(20),
                    _buildLegendItem("Inactive users", AppColors.ksecondary3),
                  ],
                ),
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 1.3,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 0,
                        centerSpaceRadius: 0,
                        sections: [
                          PieChartSectionData(
                            color: AppColors.purplecolor,
                            value: activePercentage.toDouble(),
                            title: '$activeCount ($activePercentage%)',
                            radius: 140.0,
                            titleStyle: body2TextRegular.copyWith(
                              fontSize: 16.0,
                              color: AppColors.kwhite,
                            ),
                          ),
                          PieChartSectionData(
                            color: AppColors.ksecondary3,
                            value: inactivePercentage.toDouble(),
                            title: '$inactiveCount ($inactivePercentage%)',
                            radius: 140.0,
                            titleStyle: body2TextRegular.copyWith(
                              fontSize: 16.0,
                              color: AppColors.kwhite,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )
          : const Center(child: Text("No Data Available"));
    });
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(height: 30, width: 12, color: color),
        const Gap(5),
        Text(label, style: body2TextRegular)
      ],
    );
  }
}
