import 'dart:developer';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:shimmer/shimmer.dart';

class AdminProfilePage extends StatefulWidget {
  const AdminProfilePage({super.key});

  @override
  State<AdminProfilePage> createState() => _AdminProfilePageState();
}

class _AdminProfilePageState extends State<AdminProfilePage> {
  final EventController eventController = Get.find<EventController>();
  final List<String> _businessBannerUrls = ['', '', ''];
  final List<String> _newBusinessBannerUrls = ['', '', ''];
  final UserController userController = Get.find<UserController>();
  final AdminUserController adminUserController =
      Get.find<AdminUserController>();
  String appVersion = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
      _getAppVersion();
    });
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      appVersion = 'version ${packageInfo.version}+${packageInfo.buildNumber}';
    });
  }

  Future<void> _loadInitialData() async {
    await adminUserController.getAdminDetails();
    userController.getHomePageImages();
    userController.getMobileHomePageImages();
  }

  @override
  void dispose() {
    userController.clearAdminImageData();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: AppColors.backcolor, width: 1.0),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Settings',
                      style: heading2TextRegular,
                    ),
                    const Gap(10),
                    const Divider(
                      color: AppColors.backcolor,
                      thickness: 1.0,
                    ),
                    const Gap(10),
                    Obx(
                      () => adminUserController.isAdminDataLoading.value
                          ? _buildDetailsShimmerEffect()
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    adminUserController.adminDetailsModel.value
                                            .profilePictureUrl.isEmpty
                                        ? SvgPicture.asset(
                                            "assets/svg/logo_placeholder.svg",
                                            height: 100,
                                          )
                                        : CircleAvatar(
                                            minRadius: 50,
                                            maxRadius: 50,
                                            backgroundImage: NetworkImage(
                                                adminUserController
                                                    .adminDetailsModel
                                                    .value
                                                    .profilePictureUrl),
                                          ),
                                    const Gap(12),
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              adminUserController
                                                  .adminDetailsModel.value.name,
                                              // "Business name",
                                              style: heading2TextMedium),
                                          Row(
                                            children: [
                                              SvgPicture.asset(
                                                "assets/icons/Phone.svg",
                                                fit: BoxFit.contain,
                                              ),
                                              const Gap(5),
                                              Text(
                                                adminUserController
                                                    .adminDetailsModel
                                                    .value
                                                    .mobile,
                                                style: bodyTextRegular.copyWith(
                                                    color: AppColors.secondary),
                                              ),
                                            ],
                                          ),
                                          Row(
                                            children: [
                                              SvgPicture.asset(
                                                "assets/icons/MapPin.svg",
                                                fit: BoxFit.contain,
                                              ),
                                              const Gap(5),
                                              Text(
                                                adminUserController
                                                    .adminDetailsModel
                                                    .value
                                                    .position,
                                                style: bodyTextRegular.copyWith(
                                                    color: AppColors.secondary),
                                              ),
                                            ],
                                          ),
                                          const Gap(5),
                                        ],
                                      ),
                                    ),
                                    const Spacer(),
                                    SizedBox(
                                      width: 80,
                                      child: SecondaryButton(
                                        text: "Edit",
                                        backgroundColor: AppColors.kwhite,
                                        textColor: AppColors.kprimarycolor,
                                        onTap: () {
                                          _showEditDialog(context);
                                        },
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                    ),
                    const Gap(10),
                    const Divider(
                      color: AppColors.backcolor,
                      thickness: 1.0,
                    ),
                    const Gap(10),
                    Obx(
                      () => userController.isMobileImageLoading.value
                          ? _buildMobileImageShimmerEffect()
                          : Column(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      "Mobile banner",
                                      style: bodyTextMedium,
                                    ),
                                    const Spacer(),
                                    SizedBox(
                                      width: 80,
                                      child: SecondaryButton(
                                        text: "Save",
                                        backgroundColor: AppColors.kwhite,
                                        textColor: AppColors.kprimarycolor,
                                        onTap: () async {
                                          String userID =
                                              storage.read("USER_ID") ?? "0";
                                          String imageUrl = userController
                                              .homePageMobileBannerModel
                                              .first
                                              .imagesUrl;
                                          bool success =
                                              await adminUserController
                                                  .adminDashboardImageUploadApi(
                                                      payload: {
                                                "admin_id": int.parse(userID),
                                                "id": 4,
                                                "type": "mobile",
                                                "event": "share",
                                                "images_url": imageUrl
                                              });
                                          if (success) {
                                            CustomSnackBar.showInfo(
                                              'Success',
                                              'Image edited successfully!',
                                            );
                                            userController
                                                .getMobileHomePageImages();
                                          } else {
                                            CustomSnackBar.showError(
                                              'Error',
                                              'Failed to edit the image.',
                                            );
                                            userController
                                                .getMobileHomePageImages();
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                const Gap(10),
                                Row(
                                  children: [
                                    const Icon(Icons.info),
                                    const Gap(10),
                                    Text(
                                      'Upload 1 image here for mobile banner (140 X 340).',
                                      style: captionTextRegular,
                                    ),
                                  ],
                                ),
                                const Gap(10),
                                Row(
                                  children: [
                                    Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: Image.network(
                                            userController
                                                    .homePageMobileBannerModel
                                                    .isNotEmpty
                                                ? userController
                                                    .homePageMobileBannerModel
                                                    .first
                                                    .imagesUrl
                                                : "assets/png/Banner_Placeholder.png",
                                            height: 140,
                                            width: 340,
                                            fit: BoxFit.contain,
                                            loadingBuilder: (context, child,
                                                loadingProgress) {
                                              if (loadingProgress == null) {
                                                return child;
                                              }
                                              return const CircularProgressIndicator();
                                            },
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Image.asset(
                                                "assets/png/Banner_Placeholder.png",
                                                width: 340,
                                                height: 140,
                                                fit: BoxFit.cover,
                                              );
                                            },
                                          ),
                                        ),
                                        Positioned(
                                          top: 5,
                                          right: 4,
                                          child: InkWell(
                                            hoverColor: Colors.transparent,
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                            overlayColor:
                                                MaterialStateProperty.all(
                                                    Colors.transparent),
                                            onTap: () async {
                                              log("Upload Image button pressed");
                                              FilePickerResult? result =
                                                  await FilePicker.platform
                                                      .pickFiles();

                                              if (result != null) {
                                                PlatformFile pickedFile =
                                                    result.files.first;
                                                Uint8List? fileBytes =
                                                    result.files.first.bytes;
                                                log("File selected: ${pickedFile.name}");
                                                await _uploadFileHelper(
                                                    pickedFile,
                                                    null,
                                                    fileBytes!,
                                                    4);
                                              } else {
                                                log('No file selected.');
                                              }
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(6),
                                              decoration: BoxDecoration(
                                                color: AppColors.kprimarycolor,
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              child: const Icon(
                                                Icons.edit,
                                                size: 20,
                                                color: AppColors.kwhite,
                                              ),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    const Gap(30),
                                  ],
                                ),
                              ],
                            ),
                    ),
                    const Gap(10),
                    const Divider(
                      color: AppColors.backcolor,
                      thickness: 1.0,
                    ),
                    const Gap(10),
                    Row(
                      children: [
                        Text(
                          "Business tips banner",
                          style: title3TextSemiBold,
                        ),
                        const Spacer(),
                        SizedBox(
                          width: 80,
                          child: SecondaryButton(
                            text: "Save",
                            backgroundColor: AppColors.kwhite,
                            textColor: AppColors.kprimarycolor,
                            onTap: _saveChanges,
                          ),
                        ),
                      ],
                    ),
                    const Gap(10),
                    Row(
                      children: [
                        const Icon(Icons.info),
                        const Gap(10),
                        Text(
                          'Upload 3 images here for mobile banner (360 x 578px).',
                          style: captionTextRegular,
                        ),
                      ],
                    ),
                    const Gap(10),
                    Obx(() {
                      if (userController.isImageLoading.value) {
                        return _buildDashboardImageShimmerEffect();
                      } else {
                        return Row(
                          children: List.generate(
                              3, (index) => _buildImageStack(index)),
                        );
                      }
                    }),
                    Center(
                      child: Text(appVersion,
                          style: GoogleFonts.rubik(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.txtprimary)),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: AppColors.kwhite,
              surfaceTintColor: AppColors.kwhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: const Text("Edit User Details"),
              content: SingleChildScrollView(
                child: Center(
                  child: ListBody(
                    children: <Widget>[
                      Obx(() => _buildProfileImage(setState)),
                      TextFormField(
                        onChanged: (value) {
                          adminUserController.adminDetailsModel.value.name =
                              value;
                        },
                        initialValue:
                            adminUserController.adminDetailsModel.value.name,
                        decoration: const InputDecoration(labelText: 'Name'),
                      ),
                    ],
                  ),
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    adminUserController.getAdminDetails();
                  },
                ),
                TextButton(
                  child: const Text('Save'),
                  onPressed: () async {
                    bool isSuccess =
                        await adminUserController.editAdminDetails();
                    if (isSuccess) {
                      Navigator.of(Get.context!).pop();
                      CustomSnackBar.showInfo(
                        'Success',
                        'Profile updated successfully.',
                      );

                      adminUserController.getAdminDetails();
                    } else {
                      CustomSnackBar.showError(
                        'Error',
                        'Unable to update the profile.',
                      );
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildProfileImage(StateSetter setState) {
    return Stack(
      alignment: Alignment.center,
      children: [
        adminUserController.adminDetailsModel.value.profilePictureUrl.isEmpty
            ? SvgPicture.asset(
                "assets/svg/logo_placeholder.svg",
                height: 100,
              )
            : CircleAvatar(
                minRadius: 50,
                maxRadius: 50,
                backgroundImage: NetworkImage(
                  adminUserController.adminDetailsModel.value.profilePictureUrl,
                ),
              ),
        Positioned(
          bottom: 0,
          right: 0,
          left: 50,
          child: GestureDetector(
            onTap: () async {
              await _uploadProfileImage(setState);
            },
            child: Container(
              height: 30,
              width: 30,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.kprimarycolor,
              ),
              child: const Icon(
                Icons.edit,
                size: 16,
                color: AppColors.kwhite,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageStack(int index) {
    String imageUrl = _newBusinessBannerUrls[index].isNotEmpty
        ? _newBusinessBannerUrls[index]
        : (_businessBannerUrls[index].isNotEmpty
            ? _businessBannerUrls[index]
            : userController.homePageImageModel.length > index
                ? userController.homePageImageModel[index].imagesUrl
                : "https://parenthing.s3.ap-south-1.amazonaws.com/Home_Page/${index + 1}.png");

    return Expanded(
      child: Row(
        children: [
          Expanded(
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    imageUrl,
                    width: Get.width * .3,
                    height: 550,
                    fit: BoxFit.cover,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return _buildDashboardImageShimmerEffect();
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Image.asset(
                        "assets/png/Banner_Placeholder.png",
                        width: 250,
                        height: 400,
                        fit: BoxFit.cover,
                      );
                    },
                  ),
                ),
                Positioned(
                  top: 5,
                  right: 4,
                  child: InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                    onTap: () => _uploadImage(index),
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppColors.kprimarycolor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: const Icon(
                        Icons.edit,
                        size: 20,
                        color: AppColors.kwhite,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Gap(20),
        ],
      ),
    );
  }

  Future<void> _uploadProfileImage(StateSetter setState) async {
    log("Upload Image button pressed");
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      PlatformFile pickedFile = result.files.first;
      Uint8List? fileBytes = result.files.first.bytes;
      log("File selected: ${pickedFile.name}");
      await _uploadFileHelper(pickedFile, null, fileBytes!, 5);
      setState(() {});
    } else {
      log('No file selected.');
    }
  }

  Future<void> _uploadImage(int index) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      PlatformFile pickedFile = result.files.first;
      Uint8List? fileBytes = result.files.first.bytes;
      await _uploadFileHelper(pickedFile, null, fileBytes!, index);
      setState(() {});
    } else {
      log('No file selected.');
    }
  }

  Future<void> _uploadFileHelper(PlatformFile pickedFile, File? file,
      Uint8List fileBytes, int index) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(fileExtension)) {
      CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
      );
      return;
    }
    userController.isImageUploaded.value = true;
    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    if (kDebugMode) {
      print(
          "Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    }

    bool value = await eventController.createFileNameEntry(
      isAdmin: true,
      pickedFile.name,
      contentType,
      filePath,
      fileBytes,
      "banner",
    );

    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        if (index == 5) {
          // Profile picture
          adminUserController.adminDetailsModel.value.profilePictureUrl =
              newImageUrl;
        } else if (index == 4) {
          userController.homePageMobileBannerModel.first.imagesUrl =
              newImageUrl;
        } else {
          setState(() {
            _newBusinessBannerUrls[index] = newImageUrl;
          });
        }
        if (kDebugMode) {
          print("Image URL set to: $newImageUrl");
        }
      });

      CustomSnackBar.showInfo(
        "Success",
        "Image uploaded successfully",
      );
    } else {
      log("unable to upload the image");
      CustomSnackBar.showError(
        "Failed",
        "Unable to upload Image... try again later",
      );
    }
  }

  void _saveChanges() async {
    String userID = storage.read("USER_ID") ?? "0";
    List<Map<String, dynamic>> images = [];

    for (int i = 0; i < 3; i++) {
      String imageUrl = _newBusinessBannerUrls[i].isNotEmpty
          ? _newBusinessBannerUrls[i]
          : (_businessBannerUrls[i].isNotEmpty
              ? _businessBannerUrls[i]
              : userController.homePageImageModel.length > i
                  ? userController.homePageImageModel[i].imagesUrl
                  : "https://parenthing.s3.ap-south-1.amazonaws.com/Home_Page/${i + 1}.png");

      images.add({"id": i + 1, "images_url": imageUrl, "type": "dashboard"});
    }

    Map<String, dynamic> payload = {
      "admin_id": int.parse(userID),
      "type": "dashboard",
      "images": images
    };

    bool success = await adminUserController.adminDashboardImageUploadApi(
        payload: payload);

    if (success) {
      setState(() {
        for (int i = 0; i < 3; i++) {
          if (_newBusinessBannerUrls[i].isNotEmpty) {
            _businessBannerUrls[i] = _newBusinessBannerUrls[i];
            _newBusinessBannerUrls[i] = '';
          }
        }
      });

      CustomSnackBar.showInfo(
        "Success",
        "Changes saved successfully",
      );

      // Refresh the homepage images
      userController.getHomePageImages();
    } else {
      CustomSnackBar.showError(
        "Error",
        "Failed to save changes. Please try again.",
      );
    }
  }

  Widget _buildDetailsShimmerEffect() {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF5E57E1).withOpacity(0.3),
      highlightColor: const Color(0xFF5E57E1).withOpacity(0.1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 100,
            width: 100,
            decoration: const BoxDecoration(
                color: AppColors.kwhite, shape: BoxShape.circle),
          ),
          const Gap(12),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 20,
                  width: 100,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                ),
                const Gap(12),
                Container(
                  height: 20,
                  width: 150,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                ),
                const Gap(12),
                Container(
                  height: 20,
                  width: 150,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                )
              ],
            ),
          ),
          const Spacer(),
          Container(
            height: 48,
            width: 120,
            decoration: BoxDecoration(
                color: AppColors.kwhite,
                borderRadius: BorderRadius.circular(10)),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileImageShimmerEffect() {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF5E57E1).withOpacity(0.3),
      highlightColor: const Color(0xFF5E57E1).withOpacity(0.1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(20),
          Container(
            height: 120,
            width: Get.width * .3,
            decoration: BoxDecoration(
                color: AppColors.kwhite,
                borderRadius: BorderRadius.circular(10)),
          ),
          const Spacer(),
          Container(
            height: 48,
            width: 150,
            decoration: BoxDecoration(
                color: AppColors.kwhite,
                borderRadius: BorderRadius.circular(10)),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardImageShimmerEffect() {
    return Shimmer.fromColors(
      baseColor: const Color(0xFF5E57E1).withOpacity(0.15),
      highlightColor: const Color(0xFFFFFFFF).withOpacity(0.6),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Gap(30),
          Row(
            children: [
              Expanded(
                child: Container(
                  width: Get.width * .3,
                  height: 550,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                ),
              ),
              const Gap(10),
              Expanded(
                child: Container(
                  width: Get.width * .3,
                  height: 550,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                ),
              ),
              const Gap(10),
              Expanded(
                child: Container(
                  width: Get.width * .3,
                  height: 550,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(10)),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
