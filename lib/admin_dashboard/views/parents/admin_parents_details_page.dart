import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_parents_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/model/parents/parent_details_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:shimmer/shimmer.dart';

class AdminParentsDetails extends StatefulWidget {
  const AdminParentsDetails(
      {super.key, required this.arguments, this.isParentProfile = true});
  final Map<String, int> arguments;
  final bool isParentProfile;

  @override
  State<AdminParentsDetails> createState() => _AdminParentsDetailsState();
}

class _AdminParentsDetailsState extends State<AdminParentsDetails> {
  final AdminParentsModelController adminParentsModelController = Get.find<AdminParentsModelController>();
  int id = 0;
  RxBool isApproveClass = true.obs;
  RxBool isRejectClass = false.obs;
  String selectedRejectReason = "";
  List<String> rejectReaons = [
    'Profanity',
    'Inappropriate content and imagery.',
    'Contact details are in-valid.',
    "Contact number should not be in description"
  ];

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['PARENT_ID']!;
      });
      adminParentsModelController.getAdminParentDetailsData(id);
    });
  }

  @override
  void dispose() {
    super.dispose();
    adminParentsModelController.clearAdminParentData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Obx(
            () => adminParentsModelController.isParentDetailsLoading.value
                ? buildShimmerEffect()
                : Container(
                    width: Get.width * .7,
                    decoration: BoxDecoration(
                        border:
                            Border.all(color: AppColors.backcolor, width: 1.0),
                        borderRadius: BorderRadius.circular(11.0)),
                    child: Column(
                      // mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 16),
                              child: Row(
                                children: [
                                  InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                    onTap: () {
                                      locator<NavigationServices>().goBack();
                                    },
                                    child: SvgPicture.asset(
                                        "assets/svg/arrow-left.svg"),
                                  ),
                                  const Gap(20),
                                  Text(
                                    "Profile details",
                                    style: heading2TextRegular,
                                  ),
                                  const Spacer(),
                                  adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .isReported ==
                                          1
                                      ? const AppealContainer(isAppeal: false)
                                      : adminParentsModelController
                                                  .parentsDetailsModel
                                                  .value
                                                  .hasAppealed ==
                                              1
                                          ? const AppealContainer(
                                              isAppeal: true)
                                          : const SizedBox.shrink(),
                                ],
                              ),
                            ),
                            const Divider(
                                color: AppColors.backcolor, thickness: 1.0),
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ClipOval(
                                    child: CachedNetworkImage(
                                        imageUrl: adminParentsModelController
                                            .parentsDetailsModel
                                            .value
                                            .profilePictureUrl,
                                        height: 100.0,
                                        width: 100.0,
                                        fit: BoxFit.cover,
                                        errorWidget: (context, url, error) => SvgPicture.asset(
                                            adminParentsModelController.parentsDetailsModel.value.gender == 'Female'
                                                ? "assets/icons/empty_female.svg"
                                                : "assets/icons/empty_male.svg",
                                            height: 100.0,
                                            width: 100.0,
                                            fit: BoxFit.cover),
                                        placeholder: (context, url) => SvgPicture.asset(
                                            adminParentsModelController.parentsDetailsModel.value.gender == 'Female'
                                                ? "assets/icons/empty_female.svg"
                                                : "assets/icons/empty_male.svg",
                                            height: 100.0,
                                            width: 100.0,
                                            fit: BoxFit.cover)),
                                  ),
                                  const Gap(16),
                                  _buildInfoRow('Name',
                                      '${adminParentsModelController.parentsDetailsModel.value.firstName} ${adminParentsModelController.parentsDetailsModel.value.lastName}'),
                                  _buildInfoRow(
                                      'Gender',
                                      adminParentsModelController
                                          .parentsDetailsModel.value.gender),
                                  _buildInfoRow(
                                      'Date of birth',
                                      adminParentsModelController
                                          .parentsDetailsModel.value.dob),
                                  _buildInfoRow(
                                      'Bio',
                                      adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .aboutMe
                                              .isEmpty
                                          ? "-"
                                          : adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .aboutMe),
                                  _buildInfoRow(
                                      'Email',
                                      adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .email
                                              .isEmpty
                                          ? "-"
                                          : adminParentsModelController
                                              .parentsDetailsModel.value.email),
                                  _buildInfoRow('Phone',
                                      '+91 ${adminParentsModelController.parentsDetailsModel.value.mobile}'),
                                  _buildInfoRow(
                                      'Address',
                                      adminParentsModelController
                                          .parentsDetailsModel.value.address),
                                  _buildInfoRow('No. of kids',
                                      '${adminParentsModelController.parentsDetailsModel.value.kidsCount}'),
                                  _buildInfoRow(
                                      'Joined on',
                                      getDateWithDayMonthName(
                                          adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .joinedOn)),
                                  const Divider(
                                      color: AppColors.backcolor,
                                      thickness: 1.0),
                                  const Gap(10),
                                  Text(
                                    "Kids details",
                                    style: body2TextBold,
                                  ),
                                  const Gap(10),
                                  ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: adminParentsModelController
                                          .parentsDetailsModel
                                          .value
                                          .children
                                          .length,
                                      itemBuilder: (context, index) {
                                        var childObject =
                                            adminParentsModelController
                                                .parentsDetailsModel
                                                .value
                                                .children[index];
                                        return Column(
                                          children: [
                                            KidsCell(
                                              interests: childObject.interests,
                                              childDetails: childObject,
                                            ),
                                            const Gap(20),
                                          ],
                                        );
                                      }),
                                  widget.isParentProfile
                                      ? const SizedBox.shrink()
                                      : ActivityLog(
                                          entries: adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .activityLog
                                              .map((log) {
                                            return ActivityEntry(
                                              title: log.activityType,
                                              dateTime: DateTime.parse(
                                                  log.activityTime),
                                            );
                                          }).toList(),
                                        ),
                                  widget.isParentProfile ||
                                          adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .reportDetails
                                              .isEmpty
                                      ? const SizedBox.shrink()
                                      : Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text("Report summary",
                                                style: title3TextSemiBold),
                                            const Gap(10),
                                            SingleChildScrollView(
                                              scrollDirection: Axis.horizontal,
                                              child: ConstrainedBox(
                                                constraints: BoxConstraints(
                                                    minWidth:
                                                        MediaQuery.of(context)
                                                            .size
                                                            .width),
                                                child: DataTable(
                                                  headingTextStyle:
                                                      body2TextRegular.copyWith(
                                                          color: AppColors
                                                              .txtsecondary),
                                                  dataTextStyle:
                                                      bodyTextRegular,
                                                  border: TableBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                    left: const BorderSide(
                                                        color: AppColors
                                                            .bordergrey),
                                                    right: const BorderSide(
                                                        color: AppColors
                                                            .bordergrey),
                                                    top: const BorderSide(
                                                        color: AppColors
                                                            .bordergrey),
                                                    bottom: const BorderSide(
                                                        color: AppColors
                                                            .bordergrey),
                                                    verticalInside:
                                                        BorderSide.none,
                                                  ),
                                                  headingRowColor:
                                                      MaterialStateColor
                                                          .resolveWith((states) =>
                                                              AppColors
                                                                  .klightwhite),
                                                  columnSpacing: 20,
                                                  dataRowMaxHeight: 70,
                                                  columns: [
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text('Sr No',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text(
                                                              'Reported by',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text(
                                                              'Reported on',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text('Reason',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text(
                                                              'Description',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                    DataColumn(
                                                      label: FittedBox(
                                                          child: Text(
                                                              'Attachments',
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      color: AppColors
                                                                          .txtsecondary))),
                                                    ),
                                                  ],
                                                  rows: List<DataRow>.generate(
                                                    adminParentsModelController
                                                        .parentsDetailsModel
                                                        .value
                                                        .reportDetails
                                                        .length,
                                                    (index) => DataRow(
                                                      cells: [
                                                        DataCell(Text(
                                                            "${index + 1}",
                                                            style:
                                                                bodyTextRegular)),
                                                        DataCell(Text(
                                                            adminParentsModelController
                                                                .parentsDetailsModel
                                                                .value
                                                                .reportDetails[
                                                                    index]
                                                                .name,
                                                            style:
                                                                bodyTextRegular)),
                                                        DataCell(
                                                          Text(
                                                              adminParentsModelController
                                                                  .parentsDetailsModel
                                                                  .value
                                                                  .reportDetails[
                                                                      index]
                                                                  .reportedAt,
                                                              style:
                                                                  bodyTextRegular),
                                                        ),
                                                        DataCell(Text(
                                                            adminParentsModelController
                                                                .parentsDetailsModel
                                                                .value
                                                                .reportDetails[
                                                                    index]
                                                                .reason,
                                                            style:
                                                                bodyTextRegular)),
                                                        DataCell(
                                                          SizedBox(
                                                            width: 240,
                                                            child: Text(
                                                              adminParentsModelController
                                                                      .parentsDetailsModel
                                                                      .value
                                                                      .reportDetails[
                                                                          index]
                                                                      .description
                                                                      .isEmpty
                                                                  ? "-"
                                                                  : adminParentsModelController
                                                                      .parentsDetailsModel
                                                                      .value
                                                                      .reportDetails[
                                                                          index]
                                                                      .description,
                                                              maxLines: 4,
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              style:
                                                                  bodyTextRegular,
                                                            ),
                                                          ),
                                                        ),
                                                        DataCell(Center(
                                                          child: IconButton(
                                                              onPressed: () {
                                                                log("Attachments");
                                                                _showImageDialog(
                                                                    context,
                                                                    adminParentsModelController
                                                                        .parentsDetailsModel
                                                                        .value
                                                                        .reportDetails[
                                                                            index]
                                                                        .imageUrl);
                                                              },
                                                              icon: const Icon(
                                                                  Icons
                                                                      .remove_red_eye_outlined,
                                                                  color: AppColors
                                                                      .kprimarycolor)),
                                                        )),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const Gap(30),
                                            const Divider(),
                                          ],
                                        ),
                                  adminParentsModelController
                                              .parentsDetailsModel
                                              .value
                                              .hasAppealed ==
                                          1
                                      ? Column(
                                          children: [
                                            AppealWidget(
                                              latestAppeal:
                                                  adminParentsModelController
                                                      .parentsDetailsModel
                                                      .value
                                                      .latestAppeal,
                                              appealAttachments: () {
                                                _showImageDialog(
                                                    context,
                                                    adminParentsModelController
                                                        .parentsDetailsModel
                                                        .value
                                                        .latestAppeal
                                                        .imageUrl);
                                              },
                                            ),
                                            const Gap(20),
                                            const Divider(),
                                            const Gap(20),
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                const Gap(10),
                                                Text("Take Action",
                                                    style: title3TextSemiBold),
                                                const Gap(20),
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              "Choose your response",
                                                              style:
                                                                  body2TextRegular),
                                                          const Gap(10),
                                                          Row(
                                                            children: [
                                                              Expanded(
                                                                child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                                  onTap: () {
                                                                    setState(
                                                                        () {
                                                                      isApproveClass
                                                                              .value =
                                                                          true;
                                                                      isRejectClass
                                                                              .value =
                                                                          false;
                                                                    });
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    height: 42,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      borderRadius:
                                                                          const BorderRadius
                                                                              .horizontal(
                                                                        left: Radius.circular(
                                                                            12),
                                                                      ),
                                                                      color: isApproveClass.value
                                                                          ? AppColors
                                                                              .kwhite
                                                                          : AppColors
                                                                              .scaffoldColor,
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: isApproveClass.value
                                                                            ? AppColors.txtprimary
                                                                            : AppColors.bordergrey,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Center(
                                                                      child:
                                                                          Text(
                                                                        "Accept appeal",
                                                                        style: body2TextSemiBold.copyWith(
                                                                            color: isApproveClass.value
                                                                                ? AppColors.txtprimary
                                                                                : AppColors.txtsecondary),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              Expanded(
                                                                child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                                  onTap: () {
                                                                    setState(
                                                                        () {
                                                                      isApproveClass
                                                                              .value =
                                                                          false;
                                                                      isRejectClass
                                                                              .value =
                                                                          true;
                                                                    });
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    height: 42,
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      borderRadius:
                                                                          const BorderRadius
                                                                              .horizontal(
                                                                        right: Radius.circular(
                                                                            12),
                                                                      ),
                                                                      color: isRejectClass.value
                                                                          ? AppColors
                                                                              .kwhite
                                                                          : AppColors
                                                                              .scaffoldColor,
                                                                      border:
                                                                          Border
                                                                              .all(
                                                                        color: isRejectClass.value
                                                                            ? AppColors.txtprimary
                                                                            : AppColors.bordergrey,
                                                                      ),
                                                                    ),
                                                                    child:
                                                                        Center(
                                                                      child:
                                                                          Text(
                                                                        "Reject appeal",
                                                                        style: body2TextSemiBold.copyWith(
                                                                            color: isRejectClass.value
                                                                                ? AppColors.txtprimary
                                                                                : AppColors.txtsecondary),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          const Gap(20),
                                                          isRejectClass.value
                                                              ? Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                        "Reason for rejection*",
                                                                        style:
                                                                            body2TextRegular),
                                                                    const Gap(
                                                                        10),
                                                                    CustomDropdownFormField(
                                                                      items: rejectReaons
                                                                          .map(
                                                                            (item) =>
                                                                                DropdownMenuItem(
                                                                              value: item,
                                                                              child: Text(item),
                                                                            ),
                                                                          )
                                                                          .toList(),
                                                                      hintText:
                                                                          "Select",
                                                                      onChanged:
                                                                          (value) {
                                                                        selectedRejectReason =
                                                                            value!;
                                                                        log("Selected Rejection Reason: $selectedRejectReason");
                                                                      },
                                                                      validator:
                                                                          (value) {
                                                                        if (value ==
                                                                                null ||
                                                                            value.isEmpty) {
                                                                          return 'Please select a reason';
                                                                        }
                                                                        return null;
                                                                      },
                                                                    ),
                                                                  ],
                                                                )
                                                              : Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  children: [
                                                                    Text(
                                                                        "Write a message* (This will be conveyed to the reported user)",
                                                                        style:
                                                                            body2TextRegular),
                                                                    const Gap(
                                                                        10),
                                                                    CustomTextFormField(
                                                                      onChanged:
                                                                          (val) {
                                                                        setState(
                                                                            () {
                                                                          selectedRejectReason =
                                                                              val;
                                                                        });
                                                                      },
                                                                      hintText:
                                                                          "Write here",
                                                                      maxLines:
                                                                          3,
                                                                      validator:
                                                                          (value) {
                                                                        if (value ==
                                                                                null ||
                                                                            value.isEmpty) {
                                                                          return 'Please enter the reason.';
                                                                        }
                                                                        return null;
                                                                      },
                                                                    ),
                                                                  ],
                                                                ),
                                                          const Gap(30),
                                                          PrimaryButton(
                                                            text:
                                                                "Confirm Decision",
                                                            onTap: () {
                                                              adminParentsModelController
                                                                  .appealParentsApi(
                                                                      adminParentsModelController
                                                                          .parentsDetailsModel
                                                                          .value
                                                                          .parentId,
                                                                      isApproveClass
                                                                              .value
                                                                          ? "approve"
                                                                          : "reject",
                                                                      selectedRejectReason)
                                                                  .then(
                                                                      (value) {
                                                                adminParentsModelController
                                                                    .getAllReportedParentList();
                                                                adminParentsModelController
                                                                    .getAllInActiveParentList();
                                                                adminParentsModelController
                                                                    .getAllParentList();
                                                                locator<NavigationServices>()
                                                                    .goBack();
                                                              });
                                                            },
                                                          ),
                                                          const Gap(30),
                                                        ],
                                                      ),
                                                    ),
                                                    const Gap(30),
                                                    Expanded(
                                                      child: Container(
                                                        width: Get.width,
                                                        padding:
                                                            const EdgeInsets
                                                                .all(12),
                                                        decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        8),
                                                            border: Border.all(
                                                                color: AppColors
                                                                    .bordergrey)),
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                                "If you accept this appeal then:",
                                                                style:
                                                                    body2TextSemiBold),
                                                            const Gap(5),
                                                            const BulletText(
                                                                text:
                                                                    "The account will be restored to its original state."),
                                                            const Gap(15),
                                                            Text(
                                                                "If you reject this appeal then:",
                                                                style:
                                                                    body2TextSemiBold),
                                                            const Gap(5),
                                                            const BulletText(
                                                                text:
                                                                    "The account will be permanently suspended or deleted."),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            )
                                          ],
                                        )
                                      : const SizedBox.shrink(),
                                  adminParentsModelController
                                                  .parentsDetailsModel
                                                  .value
                                                  .isReported ==
                                              1 &&
                                          adminParentsModelController
                                                  .parentsDetailsModel
                                                  .value
                                                  .hasAppealed ==
                                              0
                                      ? Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Gap(10),
                                            Text("Take Action",
                                                style: title3TextSemiBold),
                                            const Gap(20),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                          "Choose your response",
                                                          style:
                                                              body2TextRegular),
                                                      const Gap(10),
                                                      Row(
                                                        children: [
                                                          Expanded(
                                                            child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                              onTap: () {
                                                                setState(() {
                                                                  isApproveClass
                                                                          .value =
                                                                      true;
                                                                  isRejectClass
                                                                          .value =
                                                                      false;
                                                                });
                                                              },
                                                              child: Container(
                                                                height: 42,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  borderRadius:
                                                                      const BorderRadius
                                                                          .horizontal(
                                                                    left: Radius
                                                                        .circular(
                                                                            12),
                                                                  ),
                                                                  color: isApproveClass.value
                                                                      ? AppColors
                                                                          .kwhite
                                                                      : AppColors
                                                                          .scaffoldColor,
                                                                  border: Border
                                                                      .all(
                                                                    color: isApproveClass.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .bordergrey,
                                                                  ),
                                                                ),
                                                                child: Center(
                                                                  child: Text(
                                                                    "Suspend profile",
                                                                    style: body2TextSemiBold.copyWith(
                                                                        color: isApproveClass.value
                                                                            ? AppColors.txtprimary
                                                                            : AppColors.txtsecondary),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          Expanded(
                                                            child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                              onTap: () {
                                                                setState(() {
                                                                  isApproveClass
                                                                          .value =
                                                                      false;
                                                                  isRejectClass
                                                                          .value =
                                                                      true;
                                                                });
                                                              },
                                                              child: Container(
                                                                height: 42,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  borderRadius:
                                                                      const BorderRadius
                                                                          .horizontal(
                                                                    right: Radius
                                                                        .circular(
                                                                            12),
                                                                  ),
                                                                  color: isRejectClass.value
                                                                      ? AppColors
                                                                          .kwhite
                                                                      : AppColors
                                                                          .scaffoldColor,
                                                                  border: Border
                                                                      .all(
                                                                    color: isRejectClass.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .bordergrey,
                                                                  ),
                                                                ),
                                                                child: Center(
                                                                  child: Text(
                                                                    "Ignore profile",
                                                                    style: body2TextSemiBold.copyWith(
                                                                        color: isRejectClass.value
                                                                            ? AppColors.txtprimary
                                                                            : AppColors.txtsecondary),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      const Gap(20),
                                                      isRejectClass.value
                                                          ? Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                    "Reason for rejection*",
                                                                    style:
                                                                        body2TextRegular),
                                                                const Gap(10),
                                                                CustomDropdownFormField(
                                                                  items: rejectReaons
                                                                      .map(
                                                                        (item) =>
                                                                            DropdownMenuItem(
                                                                          value:
                                                                              item,
                                                                          child:
                                                                              Text(item),
                                                                        ),
                                                                      )
                                                                      .toList(),
                                                                  hintText:
                                                                      "Select",
                                                                  onChanged:
                                                                      (value) {
                                                                    selectedRejectReason =
                                                                        value!;
                                                                    log("Selected Rejection Reason: $selectedRejectReason");
                                                                  },
                                                                  validator:
                                                                      (value) {
                                                                    if (value ==
                                                                            null ||
                                                                        value
                                                                            .isEmpty) {
                                                                      return 'Please select a Personal ID Type';
                                                                    }
                                                                    return null;
                                                                  },
                                                                ),
                                                              ],
                                                            )
                                                          : Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                    "Write a message* (This will be conveyed to the reported user)",
                                                                    style:
                                                                        body2TextRegular),
                                                                const Gap(10),
                                                                CustomTextFormField(
                                                                  onChanged:
                                                                      (val) {
                                                                    setState(
                                                                        () {
                                                                      selectedRejectReason =
                                                                          val;
                                                                    });
                                                                    // int price = int.tryParse(val) ?? 0;
                                                                    // eventController.eventDetailsModel
                                                                    //     .value.price = price;
                                                                    // _onChanged(val);
                                                                  },
                                                                  hintText:
                                                                      "Write here",
                                                                  maxLines: 3,
                                                                  validator:
                                                                      (value) {
                                                                    if (value ==
                                                                            null ||
                                                                        value
                                                                            .isEmpty) {
                                                                      return 'Please enter the reason.';
                                                                    }
                                                                    return null;
                                                                  },
                                                                ),
                                                              ],
                                                            ),
                                                      const Gap(30),
                                                      PrimaryButton(
                                                        text:
                                                            "Confirm Decision",
                                                        onTap: () {
                                                          adminParentsModelController
                                                              .suspendParents(
                                                                  adminParentsModelController
                                                                      .parentsDetailsModel
                                                                      .value
                                                                      .parentId,
                                                                  selectedRejectReason,
                                                                  isApproveClass
                                                                          .value
                                                                      ? "suspend"
                                                                      : "ignore")
                                                              .then((value) {
                                                            adminParentsModelController
                                                                .getAllReportedParentList();
                                                            adminParentsModelController
                                                                .getAllInActiveParentList();
                                                            adminParentsModelController
                                                                .getAllParentList();
                                                            locator<NavigationServices>()
                                                                .goBack();
                                                          });
                                                        },
                                                      ),
                                                      const Gap(30),
                                                    ],
                                                  ),
                                                ),
                                                const Gap(30),
                                                const Spacer()
                                              ],
                                            ),
                                          ],
                                        )
                                      : const SizedBox(),
                                ],
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(label,
                style: bodyTextRegular.copyWith(color: AppColors.txtsecondary)),
          ),
          Expanded(
            child: Text(value, style: bodyTextMedium),
          ),
        ],
      ),
    );
  }

  void _showImageDialog(BuildContext context, String attachment) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: Stack(
            children: [
              Container(
                width: Get.width / 2,
                height: 600,
                decoration: BoxDecoration(
                    color: AppColors.kwhite,
                    borderRadius: BorderRadius.circular(10)),
                child: (attachment.isEmpty)
                    ? const Center(
                        child: Text(
                          'No image uploaded',
                          style: TextStyle(
                            fontSize: 20,
                            color: Colors.black,
                          ),
                        ),
                      )
                    : Image.network(
                        attachment,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),
              ),
              Positioned(
                top: 5,
                right: 4,
                child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: AppColors.kprimarycolor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.kwhite,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class ActivityLog extends StatelessWidget {
  final List<ActivityEntry> entries;

  const ActivityLog({super.key, required this.entries});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity log',
            style: title3TextSemiBold,
          ),
          const Gap(15),
          for (var entry in entries) _buildTimelineEntry(context, entry),
          Divider(
            thickness: 1.0,
            color: AppColors.ktertiary.withOpacity(.3),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineEntry(BuildContext context, ActivityEntry entry) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.kprimarycolor,
              ),
            ),
            if (entry != entries.last)
              Container(
                width: 2,
                height: 40,
                color: AppColors.kprimarycolor,
              ),
          ],
        ),
        const Gap(15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                entry.title,
                style: body2TextMedium,
              ),
              Text(
                _formatDateTime(entry.dateTime),
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              if (entry.by != null) Text('By ${entry.by}'),
              if (entry.reason != null)
                Text(
                  'Reason: ${entry.reason}',
                  style:
                      body3TextRegular.copyWith(color: AppColors.txtsecondary),
                ),
              const Gap(15),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mma');
    return formatter.format(dateTime);
  }
}

class ActivityEntry {
  final String title;
  final DateTime dateTime;
  final String? by;
  final String? reason;

  ActivityEntry({
    required this.title,
    required this.dateTime,
    this.by,
    this.reason,
  });
}

class BulletText extends StatelessWidget {
  final String text;
  const BulletText({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text("• $text",
        style: body2TextRegular.copyWith(color: AppColors.txtsecondary));
  }
}

class KidsCell extends StatelessWidget {
  const KidsCell(
      {super.key, required this.interests, required this.childDetails});
  final List<Interest> interests;
  final Child childDetails;

  @override
  Widget build(BuildContext context) {
    int calculateAge(String dob) {
      DateTime birthDate = DateTime.parse(dob);
      DateTime today = DateTime.now();
      int age = today.year - birthDate.year;
      if (today.month < birthDate.month ||
          (today.month == birthDate.month && today.day < birthDate.day)) {
        age--;
      }
      return age;
    }

    int age = calculateAge(childDetails.dob);
    log(age.toString());

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.backcolor, width: 1.0),
        borderRadius: BorderRadius.circular(11),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${childDetails.firstName} ${childDetails.lastName}',
            style: bodyTextMedium,
          ),
          Row(
            children: [
              Text(
                "$age year",
                style: body2TextMedium.copyWith(color: AppColors.txtsecondary),
              ),
              Text(" . ",
                  style:
                      body2TextMedium.copyWith(color: AppColors.txtsecondary)),
              Text(childDetails.gender,
                  style:
                      body2TextMedium.copyWith(color: AppColors.txtsecondary)),
            ],
          ),
          const Divider(color: AppColors.backcolor, thickness: 1.0),
          Text("Interests",
              style: body2TextMedium.copyWith(color: AppColors.txtsecondary)),
          interests.isEmpty
              ? Text("No interest available", style: body2TextMedium)
              : Wrap(
                  spacing: 6.0,
                  runSpacing: 2.0,
                  children: interests
                      .map((interest) => InterestsCell(interest: interest))
                      .toList(),
                ),
        ],
      ),
    );
  }
}

class InterestsCell extends StatelessWidget {
  const InterestsCell({super.key, required this.interest});
  final Interest interest;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 12.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.backcolor,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(100),
        ),
        child: Text(interest.subCategory, style: body2TextMedium),
      ),
    );
  }
}

class AppealWidget extends StatelessWidget {
  const AppealWidget(
      {super.key, required this.latestAppeal, required this.appealAttachments});
  final LatestAppeal latestAppeal;
  final void Function()? appealAttachments;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Appeal raised',
            style: title3TextSemiBold,
          ),
          const SizedBox(height: 16.0),
          _buildInfoRow('Raised on', latestAppeal.createdAt),
          const SizedBox(height: 8.0),
          _buildInfoRow('Message', latestAppeal.explanation),
          const SizedBox(height: 8.0),
          _buildInfoRow(
            'Attachment',
            'View attachment',
            isLink: true,
            onPressed: appealAttachments,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value,
      {bool isLink = false, void Function()? onPressed}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 150,
          child: Text(label,
              style: bodyTextRegular.copyWith(color: AppColors.txtsecondary)),
        ),
        Expanded(
          child: isLink
              ? GestureDetector(
                  onTap: onPressed,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.remove_red_eye,
                        color: AppColors.kprimarycolor,
                      ),
                      const Gap(5),
                      Text(
                        value,
                        style: body2TextMedium.copyWith(
                            color: AppColors.kprimarycolor),
                      ),
                    ],
                  ),
                )
              : Text(
                  value.isEmpty ? "-" : value,
                  style: body2TextMedium,
                  overflow: TextOverflow.ellipsis,
                ),
        ),
      ],
    );
  }
}

Widget buildShimmerEffect() {
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CircleAvatar(
            radius: 50,
            backgroundColor: Colors.white,
          ),
          const Gap(10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 80,
                    height: 16,
                  ),
                  const Gap(50),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 120,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 80,
                    height: 16,
                  ),
                  const Gap(50),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 70,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 100,
                    height: 16,
                  ),
                  const Gap(40),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 130,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 70,
                    height: 16,
                  ),
                  const Gap(60),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8)),
                      width: double.infinity,
                      height: 66,
                    ),
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 80,
                    height: 16,
                  ),
                  const Gap(50),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 140,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 100,
                    height: 16,
                  ),
                  const Gap(40),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 130,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 100,
                    height: 16,
                  ),
                  const Gap(40),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 200,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 100,
                    height: 16,
                  ),
                  const Gap(50),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 50,
                    height: 16,
                  ),
                ],
              ),
              const Gap(25),
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 100,
                    height: 16,
                  ),
                  const Gap(50),
                  Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8)),
                    width: 150,
                    height: 16,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ),
  );
}

class AppealContainer extends StatelessWidget {
  final bool isAppeal;
  const AppealContainer({super.key, required this.isAppeal});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 32,
      width: 120,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isAppeal
            ? AppColors.kprimarycolor.withOpacity(0.2)
            : AppColors.errorRed.withOpacity(0.2),
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      child: Row(
        children: [
          isAppeal
              ? SvgPicture.asset("assets/icons/appealed.svg")
              : SvgPicture.asset("assets/icons/reported.svg"),
          const Gap(10),
          Text(
            isAppeal ? "Appealed" : "Reported",
            style: body2TextRegular.copyWith(
                color: isAppeal ? AppColors.kprimarycolor : AppColors.errorRed),
          ),
        ],
      ),
    );
  }
}
