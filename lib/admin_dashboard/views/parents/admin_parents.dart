import 'dart:async';
import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_parents_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/common_filter.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/custom_pagination.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';

class AdminParentsPage extends StatefulWidget {
  final int initialIndex;
  const AdminParentsPage({super.key, this.initialIndex = 0});

  @override
  State<AdminParentsPage> createState() => _AdminParentsPageState();
}

class _AdminParentsPageState extends State<AdminParentsPage> {
  final AdminParentsModelController adminParentsModelController = Get.find<AdminParentsModelController>();
  int sortColumnIndex = 0;
  bool sortAscending = true;
  int pageNo = 0;
  String filterBy = "";
  // late final int pageCount;
  // final int? pages = 1;
  Timer? _debounce;
  int currentAllProfile = 0;
  int currentReportedProfile = 0;
  int currentInactiveProfile = 0;
  int currentTabIndex = 0;
  String searchQuery = "";
  DateTimeRange? selectedDateRange;
  String selectedCity = "";
  String selectedStatus = "";
  String selectedEventType = "";
  String selectedClassType = "";
  String selectedCategoryType = "";
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    currentTabIndex = widget.initialIndex;
    _loadData();
    _searchController = TextEditingController(text: searchQuery);
  }

  @override
  void dispose() {
    adminParentsModelController.dispose();
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void resetFilterAndLoadData(int tabIndex) {
    setState(() {
      currentTabIndex = tabIndex;
      searchQuery = "";
      selectedDateRange = null;
      selectedCity = "";
      selectedStatus = "";
      selectedEventType = "";
      selectedClassType = "";
      selectedCategoryType = "";
      currentAllProfile = 0;
      currentReportedProfile = 0;
      currentInactiveProfile = 0;
    });
    _loadData();
  }

  void clearFilterAndLoadData() {
    setState(() {
      searchQuery = "";
      selectedDateRange = null;
      selectedCity = "";
      selectedStatus = "";
      selectedEventType = "";
      selectedClassType = "";
      selectedCategoryType = "";
      currentAllProfile = 0;
      currentReportedProfile = 0;
      currentInactiveProfile = 0;
    });
    _loadData();
  }

  void _loadData() {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';
    log('Selected Date Range: ${selectedDateRange?.start} to ${selectedDateRange?.end}');
    log('Search Query: $searchQuery');
    log('Selected City: $selectedCity');
    log('Start Date String: $startDateString');
    log('End Date String: $endDateString');
    switch (currentTabIndex) {
      case 0:
        adminParentsModelController.getAllParentList(
            search: searchQuery,
            startDate: startDateString,
            endDate: endDateString,
            city: selectedCity,
            page: currentAllProfile);
        break;
      case 1:
        adminParentsModelController.getAllReportedParentList(
            search: searchQuery,
            startDate: startDateString,
            endDate: endDateString,
            city: selectedCity,
            page: currentReportedProfile);
        break;
      case 2:
        adminParentsModelController.getAllInActiveParentList(
            search: searchQuery,
            startDate: startDateString,
            endDate: endDateString,
            city: selectedCity,
            page: currentInactiveProfile);
        break;
    }
  }

  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        searchQuery = value;
        currentAllProfile = 0;
        currentInactiveProfile = 0;
        currentReportedProfile = 0;
      });
      _loadData();
    });
  }

  void _exportParentData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminParentsModelController.getAllParentList(
      search: searchQuery,
      startDate: startDateString,
      endDate: endDateString,
      city: selectedCity,
      page: currentAllProfile,
      action: "export",
    );
  }

  void _exportReportedParentData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminParentsModelController.getAllReportedParentList(
      search: searchQuery,
      startDate: startDateString,
      endDate: endDateString,
      city: selectedCity,
      page: currentReportedProfile,
      action: "export",
    );
  }

  void _exportInactiveProfileData() async {
    String startDateString = selectedDateRange?.start.toIso8601String() ?? '';
    String endDateString = selectedDateRange?.end.toIso8601String() ?? '';

    adminParentsModelController.getAllInActiveParentList(
      search: searchQuery,
      startDate: startDateString,
      endDate: endDateString,
      city: selectedCity,
      page: currentInactiveProfile,
      action: "export",
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Parents',
              style: heading2TextRegular,
            ),
            const Gap(10),
            DefaultTabController(
              length: 3,
              initialIndex: widget.initialIndex,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TabBar(
                    isScrollable: true,
                    padding: EdgeInsets.zero,
                    indicatorColor: AppColors.kprimarycolor,
                    labelColor: AppColors.kprimarycolor,
                    unselectedLabelColor: AppColors.kblack,
                    dividerColor: Colors.transparent,
                    unselectedLabelStyle: const TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w500,
                        color: AppColors.kblack),
                    labelStyle: const TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w500,
                        color: AppColors.kprimarycolor),
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                    onTap: (index) {
                      setState(() {
                        currentTabIndex = index;
                      });
                      resetFilterAndLoadData(index);
                    },
                    tabs: const [
                      Tab(text: 'All profile'),
                      Tab(text: 'Reported profile'),
                      Tab(text: 'Inactive profile'),
                    ],
                  ),
                  const Divider(
                    color: AppColors.backcolor,
                    height: 1.0,
                  ),
                  const Gap(10),
                  SizedBox(
                    height: Get.height,
                    child: TabBarView(
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        buildParentsDataTable(),
                        reportedParents(),
                        inActiveParents()
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // this is for active parents
  Widget buildParentsDataTable() {
    return Obx(
      () => adminParentsModelController.isParentLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  FilterBar(
                    isTotal: true,
                    onClearFilter: clearFilterAndLoadData,
                    totalText:
                        "Total parents ${adminParentsModelController.parentList.length}",
                    isAllCities: true,
                    onSearch: _onSearchChanged,
                    searchController: _searchController,
                    onStatusChanged: (value) {
                      setState(() {
                        selectedStatus = value ?? "";
                      });
                      _loadData();
                    },
                    onOnlineOfflineChanged: (value) {
                      setState(() {
                        selectedClassType = value ?? "";
                      });
                      _loadData();
                    },
                    onCategoryChanged: (value) {
                      setState(() {
                        selectedCategoryType = value;
                      });
                      _loadData();
                    },
                    onCitiesChanged: (city) {
                      if (kDebugMode) {
                        log('City changed to: $city');
                      }
                      setState(() {
                        selectedCity = city!;
                      });
                      _loadData();
                    },
                    onDateRangeChanged: (range) {
                      log('Date Range changed to: $range');
                      setState(() {
                        selectedDateRange = range;
                      });
                      _loadData();
                    },
                    currentSearchQuery: searchQuery,
                    currentDateRange: selectedDateRange,
                    currentCity: selectedCity,
                    currentStatus: selectedStatus,
                    currentOnlineOffline: selectedClassType,
                    currentCategory: selectedCategoryType,
                    onExportCSV: _exportParentData,
                    statusItems: const ['All status', 'Active', 'Inactive'],
                    onlineOfflineItems: const ['Online', 'Offline'],
                    categoryItems: const [
                      'Category 1',
                      'Category 2',
                      'Category 3'
                    ],
                    citiesItems: const [
                      "All Cities",
                      "Mumbai",
                      "Pune",
                      "Bangalore",
                      "Hyderabad"
                    ],
                  ),
                  const Gap(20),
                  adminParentsModelController.isParentLoading.value
                      ? const Center(
                          child: CircularProgressIndicator.adaptive(),
                        )
                      : adminParentsModelController.parentList.isEmpty
                          ? const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Gap(100),
                                  Icon(
                                    Icons.person,
                                    size: 65,
                                  ),
                                  Gap(10),
                                  Text(
                                    "Parents' data not found.",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16.0,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ScrollConfiguration(
                              behavior: ScrollConfiguration.of(context)
                                  .copyWith(scrollbars: false),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: DataTable(
                                  showCheckboxColumn: false,
                                  columnSpacing: 80,
                                  headingTextStyle: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                  ),
                                  dataTextStyle: const TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  sortColumnIndex: sortColumnIndex,
                                  sortAscending: sortAscending,
                                  dataRowMaxHeight: 48,
                                  border: TableBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    left: const BorderSide(
                                        color: AppColors.bordergrey),
                                    right: const BorderSide(
                                        color: AppColors.bordergrey),
                                    top: const BorderSide(
                                        color: AppColors.bordergrey),
                                    bottom: const BorderSide(
                                        color: AppColors.bordergrey),
                                    verticalInside: BorderSide.none,
                                  ),
                                  headingRowColor:
                                      MaterialStateColor.resolveWith(
                                          (states) => AppColors.klightwhite),
                                  columns: [
                                    DataColumn(
                                      label:
                                          Text('Sr No', style: body2TextBold),
                                    ),
                                    // DataColumn(
                                    //   label: Text('View', style: body2TextBold),
                                    //   tooltip: 'View',
                                    // ),
                                    DataColumn(
                                      label:
                                          Text('Photo', style: body2TextBold),
                                    ),
                                    DataColumn(
                                      label: Text('Name', style: body2TextBold),
                                      tooltip: 'Parent Name',
                                    ),
                                    DataColumn(
                                      label: Text('Joined on',
                                          style: body2TextBold),
                                      tooltip: 'Joined date',
                                    ),
                                    DataColumn(
                                      label:
                                          Text('Gender', style: body2TextBold),
                                      tooltip: 'Gender',
                                    ),
                                    DataColumn(
                                      label: Text('kids', style: body2TextBold),
                                      tooltip: 'No. of kids',
                                    ),
                                    DataColumn(
                                      label: Text('City', style: body2TextBold),
                                      tooltip: 'City',
                                    ),
                                    DataColumn(
                                      label:
                                          Text('Device', style: body2TextBold),
                                      tooltip: 'Device',
                                    ),
                                  ],
                                  rows: List<DataRow>.generate(
                                    adminParentsModelController
                                        .parentList.length,
                                    (index) => DataRow(
                                      onSelectChanged: (value) {
                                        locator<NavigationServices>()
                                            .navigateTo(adminParentsDetails,
                                                arguments: <String, dynamic>{
                                              'PARENT_ID':
                                                  adminParentsModelController
                                                      .parentList[index]
                                                      .parentId,
                                              'IS_PARENT_PROFILE': true
                                            }).then((value) {});
                                      },
                                      cells: [
                                        DataCell(
                                          Text(
                                            "${index + 1}",
                                          ),
                                        ),
                                        // DataCell(
                                        //   IconButton(
                                        //     icon: Icon(
                                        //       CupertinoIcons.eye_fill,
                                        //       color: AppColors.kprimarycolor
                                        //           .withOpacity(0.9),
                                        //     ),
                                        //     onPressed: () {
                                        //       locator<NavigationServices>()
                                        //           .navigateTo(
                                        //               adminParentsDetails,
                                        //               arguments: <String,
                                        //                   dynamic>{
                                        //             'PARENT_ID':
                                        //                 adminParentsModelController
                                        //                     .parentList[index]
                                        //                     .parentId,
                                        //             'IS_PARENT_PROFILE': true
                                        //           }).then((value) {});
                                        //     },
                                        //   ),
                                        // ),
                                        DataCell(ClipOval(
                                          child: CachedNetworkImage(
                                              imageUrl: adminParentsModelController
                                                  .parentList[index]
                                                  .profilePictureUrl,
                                              height: 40.0,
                                              width: 40.0,
                                              fit: BoxFit.cover,
                                              errorWidget: (context, url, error) =>
                                                  SvgPicture.asset(
                                                      adminParentsModelController.parentList[index].gender == 'Female'
                                                          ? "assets/icons/empty_female.svg"
                                                          : "assets/icons/empty_male.svg",
                                                      height: 40.0,
                                                      width: 40.0,
                                                      fit: BoxFit.cover),
                                              placeholder: (context, url) => SvgPicture.asset(
                                                  adminParentsModelController
                                                              .parentList[index]
                                                              .gender ==
                                                          'Female'
                                                      ? "assets/icons/empty_female.svg"
                                                      : "assets/icons/empty_male.svg",
                                                  height: 40.0,
                                                  width: 40.0,
                                                  fit: BoxFit.cover)),
                                        )),
                                        DataCell(Row(
                                          children: [
                                            Text(
                                              adminParentsModelController
                                                      .parentList[index]
                                                      .fullName
                                                      .isEmpty
                                                  ? '-'
                                                  : adminParentsModelController
                                                      .parentList[index]
                                                      .fullName
                                                      .split(' ')
                                                      .map((name) =>
                                                          name.capitalizeFirst)
                                                      .join(' '),
                                              style: body2TextRegular.copyWith(
                                                  fontWeight: FontWeight.w400),
                                            ),
                                            const Gap(4),
                                            adminParentsModelController
                                                        .parentList[index]
                                                        .isReported ==
                                                    1
                                                ? SvgPicture.asset(
                                                    "assets/svg/red_Flag.svg")
                                                : const SizedBox.shrink()
                                          ],
                                        )),
                                        DataCell(Text(
                                          getDateWithDayMonthName(
                                              adminParentsModelController
                                                  .parentList[index].joinedOn),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        )),
                                        DataCell(Text(
                                          adminParentsModelController
                                              .parentList[index]
                                              .gender
                                              .capitalizeFirst
                                              .toString(),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        )),
                                        DataCell(Text(
                                          adminParentsModelController
                                              .parentList[index].kidsCount
                                              .toString(),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        )),
                                        DataCell(Text(
                                          adminParentsModelController
                                                  .parentList[index]
                                                  .city
                                                  .isEmpty
                                              ? '-'
                                              : adminParentsModelController
                                                  .parentList[index]
                                                  .city
                                                  .capitalizeFirst
                                                  .toString(),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        )),
                                        DataCell(Text(
                                          adminParentsModelController
                                                      .parentList[index]
                                                      .deviceType ==
                                                  "ios"
                                              ? "iOS"
                                              : adminParentsModelController
                                                  .parentList[index]
                                                  .deviceType
                                                  .capitalizeFirst
                                                  .toString(),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        )),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                  const Gap(15),
                  if (adminParentsModelController
                          .parentListTotalModel.first.total >
                      0)
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                          child: CustomPagination(
                            currentPage: currentAllProfile,
                            totalItems: adminParentsModelController
                                .parentListTotalModel.first.total,
                            itemsPerPage: 10,
                            onPageChanged: (int index) {
                              setState(() {
                                currentAllProfile = index;
                              });
                              _loadData();
                            },
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
    );
  }

  Widget inActiveParents() {
    return Obx(
      () => adminParentsModelController.isInActiveParentLoading.value
          ? const Center(child: CircularProgressIndicator.adaptive())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  FilterBar(
                    onClearFilter: clearFilterAndLoadData,
                    searchController: _searchController,
                    isTotal: true,
                    totalText:
                        "Repoted parents ${adminParentsModelController.inActiveParentModel.length}",
                    isAllCities: true,
                    onSearch: _onSearchChanged,
                    onStatusChanged: (value) {
                      setState(() {
                        selectedStatus = value ?? "";
                      });
                      _loadData();
                    },
                    onOnlineOfflineChanged: (value) {
                      setState(() {
                        selectedClassType = value ?? "";
                      });
                      _loadData();
                    },
                    onCategoryChanged: (value) {
                      setState(() {
                        selectedCategoryType = value;
                      });
                      _loadData();
                    },
                    onCitiesChanged: (city) {
                      if (kDebugMode) {
                        log('City changed to: $city');
                      }
                      setState(() {
                        selectedCity = city!;
                      });
                      _loadData();
                    },
                    onDateRangeChanged: (range) {
                      log('Date Range changed to: $range');
                      setState(() {
                        selectedDateRange = range;
                      });
                      _loadData();
                    },
                    currentSearchQuery: searchQuery,
                    currentDateRange: selectedDateRange,
                    currentCity: selectedCity,
                    currentStatus: selectedStatus,
                    currentOnlineOffline: selectedClassType,
                    currentCategory: selectedCategoryType,
                    onExportCSV: _exportInactiveProfileData,
                    statusItems: const ['All status', 'Active', 'Inactive'],
                    onlineOfflineItems: const ['Online', 'Offline'],
                    categoryItems: const [
                      'Category 1',
                      'Category 2',
                      'Category 3'
                    ],
                    citiesItems: const [
                      "All Cities",
                      "Mumbai",
                      "Pune",
                      "Bangalore",
                      "Hyderabad"
                    ],
                  ),
                  const Gap(20),
                  adminParentsModelController.isInActiveParentLoading.value
                      ? const Center(child: CircularProgressIndicator.adaptive())
                      : adminParentsModelController
                              .inActiveParentModel.isNotEmpty
                          ? SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                showCheckboxColumn: false,
                                headingTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                                dataTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                sortColumnIndex: sortColumnIndex,
                                sortAscending: sortAscending,
                                dataRowMaxHeight: 48,
                                border: TableBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  left: const BorderSide(
                                      color: AppColors.bordergrey),
                                  right: const BorderSide(
                                      color: AppColors.bordergrey),
                                  top: const BorderSide(
                                      color: AppColors.bordergrey),
                                  bottom: const BorderSide(
                                      color: AppColors.bordergrey),
                                  verticalInside: BorderSide.none,
                                ),
                                headingRowColor: MaterialStateColor.resolveWith(
                                    (states) => AppColors.klightwhite),
                                columns: [
                                  DataColumn(
                                    label: Text('Sr No', style: body2TextBold),
                                  ),
                                  // DataColumn(
                                  //   label: Text('View', style: body2TextBold),
                                  //   tooltip: 'View',
                                  // ),
                                  DataColumn(
                                    label: Text('Photo', style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label: Text('Name', style: body2TextBold),
                                    tooltip: 'Parent Name',
                                  ),
                                  DataColumn(
                                    label: Text('Date', style: body2TextBold),
                                    tooltip: 'Date',
                                  ),
                                  DataColumn(
                                    label: Text('Status', style: body2TextBold),
                                    tooltip: 'Status',
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Done by', style: body2TextBold),
                                    tooltip: 'Done by',
                                  ),
                                  DataColumn(
                                    label: Text('', style: body2TextBold),
                                    tooltip: '',
                                  ),
                                  DataColumn(
                                    label: Text('', style: body2TextBold),
                                    tooltip: '',
                                  ),
                                  DataColumn(
                                    label: Text('', style: body2TextBold),
                                    tooltip: '',
                                  ),
                                  DataColumn(
                                    label: Text('', style: body2TextBold),
                                    tooltip: '',
                                  ),
                                  DataColumn(
                                    label: Text('', style: body2TextBold),
                                    tooltip: '',
                                  ),
                                ],
                                rows: List<DataRow>.generate(
                                  adminParentsModelController
                                      .inActiveParentModel.length,
                                  (index) => DataRow(
                                    onSelectChanged: (value) {
                                      locator<NavigationServices>().navigateTo(
                                          adminParentsDetails,
                                          arguments: <String, dynamic>{
                                            'PARENT_ID':
                                                adminParentsModelController
                                                    .inActiveParentModel[index]
                                                    .parentId,
                                            'IS_PARENT_PROFILE': false
                                          }).then((value) {});
                                    },
                                    cells: [
                                      DataCell(
                                        Text(
                                          "${index + 1}",
                                        ),
                                      ),
                                      // DataCell(IconButton(
                                      //   icon: Icon(
                                      //     CupertinoIcons.eye_fill,
                                      //     color: AppColors.kprimarycolor
                                      //         .withOpacity(0.9),
                                      //   ),
                                      //   onPressed: () {
                                      //     locator<NavigationServices>()
                                      //         .navigateTo(adminParentsDetails,
                                      //             arguments: <String, dynamic>{
                                      //           'PARENT_ID':
                                      //               adminParentsModelController
                                      //                   .inActiveParentModel[
                                      //                       index]
                                      //                   .parentId,
                                      //           'IS_PARENT_PROFILE': false
                                      //         }).then((value) {});
                                      //   },
                                      // )),
                                      DataCell(
                                        ClipOval(
                                          child: CachedNetworkImage(
                                              imageUrl: adminParentsModelController
                                                  .inActiveParentModel[index]
                                                  .profilePictureUrl,
                                              height: 40.0,
                                              width: 40.0,
                                              fit: BoxFit.cover,
                                              errorWidget: (context, url, error) =>
                                                  SvgPicture.asset(
                                                      adminParentsModelController.inActiveParentModel[index].gender ==
                                                              'Female'
                                                          ? "assets/icons/empty_female.svg"
                                                          : "assets/icons/empty_male.svg",
                                                      height: 40.0,
                                                      width: 40.0,
                                                      fit: BoxFit.cover),
                                              placeholder: (context, url) =>
                                                  SvgPicture.asset(
                                                      adminParentsModelController
                                                                  .inActiveParentModel[index]
                                                                  .gender ==
                                                              'Female'
                                                          ? "assets/icons/empty_female.svg"
                                                          : "assets/icons/empty_male.svg",
                                                      height: 40.0,
                                                      width: 40.0,
                                                      fit: BoxFit.cover)),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          adminParentsModelController
                                              .inActiveParentModel[index]
                                              .parentName,
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(Text(
                                        getDateWithDayMonthName(
                                            adminParentsModelController
                                                .inActiveParentModel[index]
                                                .updatedAt),
                                        style: body2TextRegular.copyWith(
                                            fontWeight: FontWeight.w400),
                                      )),
                                      DataCell(Text(
                                        adminParentsModelController
                                                    .inActiveParentModel[index]
                                                    .isDeleted ==
                                                0
                                            ? adminParentsModelController
                                                        .inActiveParentModel[
                                                            index]
                                                        .isDisabled ==
                                                    0
                                                ? "Active"
                                                : "Disabled"
                                            : "Suspended",
                                        style: body2TextRegular.copyWith(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: "Rubic",
                                            color: Colors.red),
                                      )),
                                      DataCell(Text(
                                        adminParentsModelController
                                                    .inActiveParentModel[index]
                                                    .adminName ==
                                                ""
                                            ? "-"
                                            : adminParentsModelController
                                                .inActiveParentModel[index]
                                                .adminName,
                                        style: body2TextRegular.copyWith(
                                            fontWeight: FontWeight.w400),
                                      )),
                                      DataCell(
                                        Text(
                                          "",
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          "",
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          "",
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          "",
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(
                                        Text(
                                          "",
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Gap(100),
                                  Icon(
                                    Icons.person,
                                    size: 65,
                                  ),
                                  Gap(10),
                                  Text(
                                    "Inactive parents' data not found.",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                  const Gap(15),
                  if (adminParentsModelController
                          .parentListTotalModel.first.total >
                      0)
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                          child: CustomPagination(
                            currentPage: currentInactiveProfile,
                            totalItems: adminParentsModelController
                                .parentListTotalModel.first.total,
                            itemsPerPage: 10,
                            onPageChanged: (int index) {
                              setState(() {
                                currentInactiveProfile = index;
                              });
                              _loadData();
                            },
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
    );
  }

  Widget reportedParents() {
    return Obx(() => adminParentsModelController.isRepotedParentLoading.value
        ? const Center(child: CircularProgressIndicator.adaptive())
        : ScrollConfiguration(
            behavior:
                ScrollConfiguration.of(context).copyWith(scrollbars: false),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  FilterBar(
                    isTotal: true,
                    onClearFilter: clearFilterAndLoadData,
                    searchController: _searchController,
                    totalText:
                        "Total parents ${adminParentsModelController.reportParentList.length}",
                    isAllCities: true,
                    onSearch: _onSearchChanged,
                    onStatusChanged: (value) {
                      setState(() {
                        selectedStatus = value ?? "";
                      });
                      _loadData();
                    },
                    onOnlineOfflineChanged: (value) {
                      setState(() {
                        selectedClassType = value ?? "";
                      });
                      _loadData();
                    },
                    onCategoryChanged: (value) {
                      setState(() {
                        selectedCategoryType = value;
                      });
                      _loadData();
                    },
                    onCitiesChanged: (city) {
                      if (kDebugMode) {
                        log('City changed to: $city');
                      }
                      setState(() {
                        selectedCity = city!;
                      });
                      _loadData();
                    },
                    onDateRangeChanged: (range) {
                      log('Date Range changed to: $range');
                      setState(() {
                        selectedDateRange = range;
                      });
                      _loadData();
                    },
                    currentSearchQuery: searchQuery,
                    currentDateRange: selectedDateRange,
                    currentCity: selectedCity,
                    currentStatus: selectedStatus,
                    currentOnlineOffline: selectedClassType,
                    currentCategory: selectedCategoryType,
                    onExportCSV: _exportReportedParentData,
                    statusItems: const ['All status', 'Active', 'Inactive'],
                    onlineOfflineItems: const ['Online', 'Offline'],
                    categoryItems: const [
                      'Category 1',
                      'Category 2',
                      'Category 3'
                    ],
                    citiesItems: const [
                      "All Cities",
                      "Mumbai",
                      "Pune",
                      "Bangalore",
                      "Hyderabad"
                    ],
                  ),
                  const Gap(20),
                  adminParentsModelController.isRepotedParentLoading.value
                      ? const Center(child: CircularProgressIndicator.adaptive())
                      : adminParentsModelController.reportParentList.isNotEmpty
                          ? SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: DataTable(
                                showCheckboxColumn: false,
                                headingTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                                dataTextStyle: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                sortColumnIndex: sortColumnIndex,
                                sortAscending: sortAscending,
                                dataRowMaxHeight: 48,
                                border: TableBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  left: const BorderSide(
                                      color: AppColors.bordergrey),
                                  right: const BorderSide(
                                      color: AppColors.bordergrey),
                                  top: const BorderSide(
                                      color: AppColors.bordergrey),
                                  bottom: const BorderSide(
                                      color: AppColors.bordergrey),
                                  verticalInside: BorderSide.none,
                                ),
                                headingRowColor: MaterialStateColor.resolveWith(
                                    (states) => AppColors.klightwhite),
                                columns: [
                                  DataColumn(
                                    label: Text('Sr No', style: body2TextBold),
                                  ),
                                  // DataColumn(
                                  //   label: Text('View', style: body2TextBold),
                                  //   tooltip: 'View',
                                  // ),
                                  DataColumn(
                                    label: Text('Photo', style: body2TextBold),
                                  ),
                                  DataColumn(
                                    label: Text('Name', style: body2TextBold),
                                    tooltip: 'Parent Name',
                                  ),
                                  DataColumn(
                                    label: Text('Reported on',
                                        style: body2TextBold),
                                    tooltip: 'Reported on',
                                  ),
                                  DataColumn(
                                    label: Text('Reported by',
                                        style: body2TextBold),
                                    tooltip: 'Reported by',
                                  ),
                                  DataColumn(
                                    label: Text('Status', style: body2TextBold),
                                    tooltip: 'Status',
                                  ),
                                  DataColumn(
                                    label:
                                        Text('Done by', style: body2TextBold),
                                    tooltip: 'Done by',
                                  ),
                                ],
                                rows: List<DataRow>.generate(
                                  adminParentsModelController
                                      .reportParentList.length,
                                  (index) => DataRow(
                                    onSelectChanged: (value) {
                                      locator<NavigationServices>().navigateTo(
                                          adminParentsDetails,
                                          arguments: <String, dynamic>{
                                            'PARENT_ID':
                                                adminParentsModelController
                                                    .reportParentList[index]
                                                    .reportedParentId,
                                            'IS_PARENT_PROFILE': false
                                          }).then((value) {});
                                    },
                                    cells: [
                                      DataCell(
                                        Text(
                                          "${index + 1}",
                                        ),
                                      ),
                                      // DataCell(IconButton(
                                      //   icon: Icon(
                                      //     CupertinoIcons.eye_fill,
                                      //     color: AppColors.kprimarycolor
                                      //         .withOpacity(0.9),
                                      //   ),
                                      //   onPressed: () {
                                      //     locator<NavigationServices>()
                                      //         .navigateTo(adminParentsDetails,
                                      //             arguments: <String, dynamic>{
                                      //           'PARENT_ID':
                                      //               adminParentsModelController
                                      //                   .reportParentList[index]
                                      //                   .reportedParentId,
                                      //           'IS_PARENT_PROFILE': false
                                      //         }).then((value) {});
                                      //   },
                                      // )),
                                      DataCell(ClipOval(
                                        child: CachedNetworkImage(
                                            imageUrl: adminParentsModelController
                                                .reportParentList[index]
                                                .profilePictureUrl,
                                            height: 40.0,
                                            width: 40.0,
                                            fit: BoxFit.cover,
                                            errorWidget: (context, url, error) => SvgPicture.asset(
                                                adminParentsModelController.reportParentList[index].gender ==
                                                        'Female'
                                                    ? "assets/icons/empty_female.svg"
                                                    : "assets/icons/empty_male.svg",
                                                height: 40.0,
                                                width: 40.0,
                                                fit: BoxFit.cover),
                                            placeholder: (context, url) => SvgPicture.asset(
                                                adminParentsModelController
                                                            .reportParentList[index]
                                                            .gender ==
                                                        'Female'
                                                    ? "assets/icons/empty_female.svg"
                                                    : "assets/icons/empty_male.svg",
                                                height: 40.0,
                                                width: 40.0,
                                                fit: BoxFit.cover)),
                                      )),
                                      DataCell(
                                        Text(
                                          adminParentsModelController
                                              .reportParentList[index]
                                              .parentName
                                              .capitalizeFirst
                                              .toString(),
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                      DataCell(Text(
                                        getDateWithDayMonthName(
                                            adminParentsModelController
                                                .reportParentList[index]
                                                .reportedOn),
                                        style: body2TextRegular.copyWith(
                                            fontWeight: FontWeight.w400),
                                      )),
                                      DataCell(Text(
                                        adminParentsModelController
                                            .reportParentList[index]
                                            .reportedBy
                                            .first
                                            .parentName,
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary),
                                      )),
                                      DataCell(Text(
                                        adminParentsModelController
                                                    .reportParentList[index]
                                                    .status ==
                                                ''
                                            ? "-"
                                            : adminParentsModelController
                                                .reportParentList[index]
                                                .status
                                                .capitalizeFirst
                                                .toString(),
                                        style: bodyTextRegular.copyWith(
                                            color: adminParentsModelController
                                                        .reportParentList[index]
                                                        .status ==
                                                    'pending'
                                                ? AppColors.kwarningbold
                                                : adminParentsModelController
                                                            .reportParentList[
                                                                index]
                                                            .status ==
                                                        'suspended'
                                                    ? AppColors.errorRed
                                                    : AppColors.kprimarycolor),
                                      )),
                                      DataCell(
                                        Text(
                                          adminParentsModelController
                                                      .reportParentList[index]
                                                      .adminName ==
                                                  ''
                                              ? "-"
                                              : adminParentsModelController
                                                  .reportParentList[index]
                                                  .adminName,
                                          style: body2TextRegular.copyWith(
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Gap(100),
                                  Icon(
                                    Icons.person,
                                    size: 65,
                                  ),
                                  Gap(10),
                                  Text(
                                    "Reported parents' data not found.",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                  const Gap(15),
                  if (adminParentsModelController
                          .parentListTotalModel.first.total >
                      0)
                    Row(
                      children: [
                        const Spacer(),
                        Expanded(
                          child: CustomPagination(
                            currentPage: currentReportedProfile,
                            totalItems: adminParentsModelController
                                .parentListTotalModel.first.total,
                            itemsPerPage: 10,
                            onPageChanged: (int index) {
                              setState(() {
                                currentReportedProfile = index;
                              });
                              _loadData();
                            },
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ));
  }
}
