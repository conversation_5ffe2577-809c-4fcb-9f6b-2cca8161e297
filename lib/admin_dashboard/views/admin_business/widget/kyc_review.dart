import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:photo_view/photo_view.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:url_launcher/url_launcher.dart';

class KycReviewPage extends StatefulWidget {
  final int requestID;
  const KycReviewPage({super.key, required this.requestID});

  @override
  State<KycReviewPage> createState() => _KycReviewPageState();
}

class _KycReviewPageState extends State<KycReviewPage> {
  final AdminBusinessController adminBusinessController = Get.find<AdminBusinessController>();
  RxBool isNameMatched = false.obs;
  RxBool isApprove = true.obs;
  RxBool isDocumentNumberMatched = false.obs;
  RxBool isReject = false.obs;
  String selectedRejectReason = "";
  List<String> rejectReaons = [
    'Document image is not clear',
    'Name does not match',
    'Document number does not match',
  ];
  @override
  void initState() {
    super.initState();
    adminBusinessController.getKycRequestByID(widget.requestID);
  }

@override
  void dispose() {
   adminBusinessController.clearAdminKYCData();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      appBar: AppBar(
        shadowColor: AppColors.kwhite,
        backgroundColor: AppColors.kwhite,
        surfaceTintColor: AppColors.kwhite,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                height: 32,
                width: 32,
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, color: AppColors.scaffoldColor),
                child: const Center(
                  child: Icon(Icons.close),
                ),
              ),
            ),
            const Spacer(),
            Text("KYC review", style: titleTextSemiBold),
            const Spacer(),
          ],
        ),
      ),
      body: Obx(
        () => adminBusinessController.iskycByIDLoading.value
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Column(
                children: [
                  const Divider(
                    color: AppColors.backcolor,
                    height: 1.0,
                  ),
                  Expanded(
                    child: ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context)
                          .copyWith(scrollbars: false),
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Business name",
                              style: bodyTextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                            ),
                            const Gap(4),
                            Text(
                              // "EduSpace pvt Ltd",
                              adminBusinessController
                                  .kycRequestByIDModel.value.businessName,
                              style: bodyTextSemiBold,
                            ),
                            const Gap(25),
                            Text(
                              "Status",
                              style: bodyTextRegular,
                            ),
                            const Gap(4),
                            Text(
                              adminBusinessController.kycRequestByIDModel.value
                                  .status.capitalizeFirst
                                  .toString(),
                              style: bodyTextSemiBold.copyWith(
                                  color: adminBusinessController
                                              .kycRequestByIDModel
                                              .value
                                              .status ==
                                          "approved"
                                      ? AppColors.kprimarycolor
                                      : adminBusinessController
                                                  .kycRequestByIDModel
                                                  .value
                                                  .status ==
                                              "pending"
                                          ? AppColors.kwarningbold
                                          : adminBusinessController
                                                      .kycRequestByIDModel
                                                      .value
                                                      .status ==
                                                  "rejected"
                                              ? AppColors.errorRed
                                              : AppColors.kprimarycolor),
                            ),
                            const Gap(25),
                            Text(
                              "KYC submitted on",
                              style: bodyTextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                            ),
                            const Gap(4),
                            Text(
                              dateFormat(adminBusinessController
                                  .kycRequestByIDModel.value.date),
                              style: bodyTextSemiBold,
                            ),
                            const Gap(25),
                            Text(
                              "Document Number",
                              style: bodyTextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                            ),
                            const Gap(4),
                            Text(
                              adminBusinessController
                                  .kycRequestByIDModel.value.documentNumber,
                              style: bodyTextSemiBold,
                            ),
                            const Gap(25),
                            Text(
                              "Registered address",
                              style: bodyTextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                            ),
                            const Gap(4),
                            Text(
                              adminBusinessController
                                  .kycRequestByIDModel.value.businessAddress,
                              maxLines: 3,
                              textAlign: TextAlign.start,
                              style: bodyTextSemiBold,
                            ),
                            const Gap(25),
                            Text(
                              "Attachment",
                              style: bodyTextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                            ),
                            const Gap(4),
                            Row(
                              children: [
                                SvgPicture.asset("assets/icons/Eye.svg"),
                                const Gap(5),
                                InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                  onTap: () {
                                    _showDocumentDialog(
                                      adminBusinessController
                                          .kycRequestByIDModel
                                          .value
                                          .documentUrl,
                                    );
                                    // adminBusinessController.kycRequestByIDModel.value.documentUrl
                                  },
                                  child: Text(
                                    "View document",
                                    style: bodyTextSemiBold.copyWith(
                                        color: AppColors.kprimarycolor),
                                  ),
                                ),
                              ],
                            ),
                            const Gap(25),
                            const Divider(
                                color: AppColors.scaffoldColor, height: 1.0),
                            const Gap(25),
                            adminBusinessController
                                            .kycRequestByIDModel.value.status ==
                                        "approved" ||
                                    adminBusinessController
                                            .kycRequestByIDModel.value.status ==
                                        "rejected"
                                ? const SizedBox.shrink()
                                : Column(
                                    children: [
                                      Visibility(
                                        visible: !isReject.value,
                                        child: Column(
                                          children: [
                                            CheckboxListTile(
                                              // checkColor: AppColors.kprimarycolor,

                                              title: Text(
                                                  "Name on document matches the business name.",
                                                  style: bodyTextRegular),
                                              value: isNameMatched.value,
                                              onChanged: (newValue) {
                                                isNameMatched.value = newValue!;
                                              },
                                              controlAffinity:
                                                  ListTileControlAffinity
                                                      .leading,
                                            ),
                                            CheckboxListTile(
                                              // checkColor: AppColors.kprimarycolor,

                                              title: Text(
                                                  "The document number is the same as the one on the document.",
                                                  style: bodyTextRegular),
                                              value:
                                                  isDocumentNumberMatched.value,
                                              onChanged: (newValue) {
                                                isDocumentNumberMatched.value =
                                                    newValue!;
                                              },
                                              controlAffinity:
                                                  ListTileControlAffinity
                                                      .leading,
                                            ),
                                            const Gap(25),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        "Take actions",
                                        style: bodyTextRegular.copyWith(
                                            color: AppColors.txtsecondary),
                                      ),
                                      const Gap(4),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                              onTap: () {
                                                isApprove.value = true;
                                                isReject.value = false;
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    left: Radius.circular(12),
                                                  ),
                                                  color: isApprove.value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                    color: isApprove.value
                                                        ? AppColors.txtprimary
                                                        : AppColors.bordergrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Approve",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isApprove.value
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                              onTap: () {
                                                isApprove.value = false;
                                                isReject.value = true;
                                              },
                                              child: Container(
                                                height: 42,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius
                                                          .horizontal(
                                                    right: Radius.circular(12),
                                                  ),
                                                  color: isReject.value
                                                      ? AppColors.kwhite
                                                      : AppColors.scaffoldColor,
                                                  border: Border.all(
                                                    color: isReject.value
                                                        ? AppColors.txtprimary
                                                        : AppColors.bordergrey,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    "Reject",
                                                    style: body2TextSemiBold
                                                        .copyWith(
                                                      color: isReject.value
                                                          ? AppColors.txtprimary
                                                          : AppColors
                                                              .txtsecondary,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Gap(25),
                                      isReject.value == true
                                          ? Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "Reason for rejection*",
                                                  style:
                                                      bodyTextRegular.copyWith(
                                                          color: AppColors
                                                              .txtsecondary),
                                                ),
                                                const Gap(4),
                                                CustomDropdownFormField(
                                                  items: rejectReaons
                                                      .map((item) =>
                                                          DropdownMenuItem(
                                                            value: item,
                                                            child: Text(item),
                                                          ))
                                                      .toList(),
                                                  hintText: 'Select',
                                                  onChanged: (value) {
                                                    selectedRejectReason =
                                                        value!;
                                                    log("Selected Rejection Reason: $selectedRejectReason");
                                                  },
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return 'Please select a Personal ID Type';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ],
                                            )
                                          : const SizedBox.shrink(),
                                      const Gap(25),
                                      PrimaryButton(
                                        text: "Confirm verification",
                                        onTap: () {
                                          if (isApprove.value &&
                                              (!isNameMatched.value ||
                                                  !isDocumentNumberMatched
                                                      .value)) {
                                           CustomSnackBar.showError(
                                              'Error',
                                              'Both checkboxes must be selected to approve.'
                                            );
                                          } else if (isReject.value &&
                                              selectedRejectReason.isNotEmpty) {
                                            log("Rejecting KYC with ID: ${widget.requestID}");
                                            log("Selected Rejection Reason: $selectedRejectReason");

                                            adminBusinessController
                                                .verifyAdminKyc(
                                              businesID: adminBusinessController
                                                  .kycRequestByIDModel
                                                  .value
                                                  .businessId,
                                              reason: selectedRejectReason,
                                              status: 'rejected',
                                            )
                                                .then((value) {
                                              adminBusinessController
                                                  .getAdminEventList();

                                              Navigator.pop(context);
                                              CustomSnackBar.showInfo(
                                                'Success',
                                                'KYC has been rejected successfully.',
                                              
                                              );
                                            });
                                          } else if (isApprove.value) {
                                            log("Approving KYC with ID: ${widget.requestID}");

                                            adminBusinessController
                                                .verifyAdminKyc(
                                              businesID: adminBusinessController
                                                  .kycRequestByIDModel
                                                  .value
                                                  .businessId,
                                              reason: '',
                                              status: 'approved',
                                            )
                                                .then((value) {
                                              adminBusinessController
                                                  .getAdminKycList();

                                              Navigator.pop(context);
                                            CustomSnackBar.showInfo(
                                                'Success',
                                                'KYC has been approved successfully.'
                                              );
                                            });
                                          } else {
                                          CustomSnackBar.showError(
                                              'Error',
                                              'Please select a rejection reason if rejecting.'
                                            );
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                            const Gap(20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class KycReviewPanel extends StatelessWidget {
  final Widget content;

  const KycReviewPanel({super.key, required this.content});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Container(
            color: Colors.black.withOpacity(0.5),
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Container(
            color: Colors.white,
            width: Get.width * 0.35,

            // height: Get.height,
            child: content,
          ),
        ),
      ],
    );
  }
}

Future<void> _launchUrl(String url) async {
  final Uri uri = Uri.parse(url);
  if (!await launchUrl(uri)) {
    throw Exception('Could not launch $url');
  }
}

void _showDocumentDialog(String documentUrl) async {
  final isPdf = documentUrl.endsWith('.pdf');

  if (kIsWeb && isPdf) {
    await _launchUrl(documentUrl);
  } else {
    showDialog(
      context: Get.context!,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(child: ImageViewPage(url: documentUrl));
      },
    );
  }
}

class ImageViewPage extends StatelessWidget {
  final String url;

  const ImageViewPage({super.key, required this.url});

  @override
  Widget build(BuildContext context) {
    final imageProvider = NetworkImage(url);
    final controller = PhotoViewController();

    return SizedBox(
   width:  Get.width * 0.7,height:  Get.height * 0.7,
      child:
          Padding(
            padding: const EdgeInsets.all(16),
            child: Stack(
              children: [
                PhotoView(
                  
                  imageProvider: imageProvider,
                  controller: controller,
                  minScale: PhotoViewComputedScale.contained,
                  maxScale: PhotoViewComputedScale.contained * 2,
                  enableRotation: false,
                                  customSize: Size(Get.width * 0.7, Get.height * 0.7),
                    initialScale: PhotoViewComputedScale.contained,
                      basePosition: Alignment.center,
                    heroAttributes: const PhotoViewHeroAttributes(tag: "image"),
                        
                  backgroundDecoration: const BoxDecoration(color: Colors.transparent),
                ),
                Positioned(
            top: 10,
            right: 10,
            child: IconButton(
              icon: const Icon(Icons.close, color: AppColors.kprimarycolor),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
                ),
              ],
            ),
            
          ),
          
       
    );
  }
}
