import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/utils.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_cell.dart';
import 'package:parenthing_dashboard/view/common_widgets/animation.dart';
import 'package:parenthing_dashboard/view/common_widgets/empty_case.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_cell.dart';
import 'package:parenthing_dashboard/view/event/widgets/option_container.dart';
import 'package:parenthing_dashboard/view/profile/widgets/social_medial_container.dart';

class AdminBusinessDetailsPage extends StatefulWidget {
  final Map<String, int> arguments;

  const AdminBusinessDetailsPage({super.key, required this.arguments});

  @override
  State<AdminBusinessDetailsPage> createState() =>
      _AdminBusinessDetailsPageState();
}

class _AdminBusinessDetailsPageState extends State<AdminBusinessDetailsPage> {
  int id = 0;
  bool isAbout = true;
  bool isEvent = false;
  bool isClasses = false;
  String type = "about";
  final EventController eventController = Get.find<EventController>();
  final ClassController classVM = Get.find<ClassController>();
  final AdminBusinessController adminBusinessController =
      Get.find<AdminBusinessController>();

  @override
  void initState() {
    super.initState();
    // eventController.getAllEventList("published");
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['businessByID']!;
      });
      adminBusinessController.getAdminBusinesstByID(businessID: id, type: type);
    });
  }

  @override
  void dispose() {
    adminBusinessController.clearAdminBusinessData();
    super.dispose();
  }

  // Future<void> _launchURL(String urlString) async {
  //   final Uri url = Uri.parse(urlString);
  //   if (!await canLaunchUrl(url)) {
  //     throw Exception('Could not launch $url');
  //   }
  //   await launchUrl(url, mode: LaunchMode.externalApplication);
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              width: Get.width * .7,
              // padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color: AppColors.ktertiary.withOpacity(.3), width: 1.0),
              ),
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            InkWell(
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              overlayColor:
                                  MaterialStateProperty.all(Colors.transparent),
                              onTap: () {
                                locator<NavigationServices>().goBack();
                              },
                              child:
                                  SvgPicture.asset("assets/svg/arrow-left.svg"),
                            ),
                            const Spacer(),
                            Text(
                              "Business details",
                              style: heading2TextRegular,
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                      Divider(
                        thickness: 1.0,
                        color: AppColors.ktertiary.withOpacity(.3),
                      ),
                      Obx(
                        () => Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  adminBusinessController.adminBusinessByIDModel
                                          .value.profilePictureUrl.isEmpty
                                      ? SvgPicture.asset(
                                          "assets/svg/logo_placeholder.svg",
                                          height: 100,
                                        )
                                      : CircleAvatar(
                                          maxRadius: 100,
                                          child: Image.network(
                                              adminBusinessController
                                                  .adminBusinessByIDModel
                                                  .value
                                                  .profilePictureUrl),
                                        ),
                                  const Gap(12),
                                  Expanded(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            adminBusinessController
                                                .adminBusinessByIDModel
                                                .value
                                                .businessName,
                                            // "Business name",
                                            style: heading2TextMedium),
                                        Row(
                                          children: [
                                            SvgPicture.asset(
                                                "assets/icons/Envelope.svg",
                                                fit: BoxFit.contain),
                                            const Gap(5),
                                            Text(
                                              adminBusinessController
                                                  .adminBusinessByIDModel
                                                  .value
                                                  .email,
                                              style: bodyTextRegular.copyWith(
                                                  color: AppColors.secondary),
                                            ),
                                          ],
                                        ),
                                        const Gap(5),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgPicture.asset(
                                              "assets/icons/MapPin.svg",
                                              fit: BoxFit.contain,
                                            ),
                                            const Gap(5),
                                            Expanded(
                                              child: Text(
                                                adminBusinessController
                                                    .adminBusinessByIDModel
                                                    .value
                                                    .officeAddress,
                                                style: bodyTextRegular.copyWith(
                                                    color: AppColors.secondary),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const Gap(5),
                                        Row(
                                          children: [
                                            SvgPicture.asset(
                                              "assets/icons/Phone.svg",
                                              fit: BoxFit.contain,
                                            ),
                                            const Gap(5),
                                            Text(
                                              adminBusinessController
                                                      .adminBusinessByIDModel
                                                      .value
                                                      .businessPhone
                                                      .isEmpty
                                                  ? "N/A"
                                                  : '+91-${adminBusinessController.adminBusinessByIDModel.value.businessPhone}',
                                              style: bodyTextRegular.copyWith(
                                                  color: AppColors.secondary),
                                            ),
                                          ],
                                        ),
                                        const Gap(5),
                                        Row(
                                          children: [
                                            SvgPicture.asset(
                                              "assets/svg/Calendar.svg",
                                              fit: BoxFit.contain,
                                            ),
                                            const Gap(5),
                                            Text(
                                              'Joined on ${dateFormat(adminBusinessController.adminBusinessByIDModel.value.createdAt)}',
                                              style: bodyTextRegular.copyWith(
                                                  color: AppColors.secondary),
                                            ),
                                          ],
                                        ),
                                        const Gap(20),
                                        Row(
                                          children: [
                                            SocialMediaContainer(
                                              imagePath:
                                                  "assets/icons/Link.svg",
                                              title: "Website",
                                              onTap: () {
                                                Utils.launchURL(
                                                    adminBusinessController
                                                        .adminBusinessByIDModel
                                                        .value
                                                        .websiteLink);
                                              },
                                            ),
                                            const Gap(10),
                                            SocialMediaContainer(
                                              imagePath:
                                                  "assets/icons/InstagramLogo.svg",
                                              title: "Instagram",
                                              onTap: () {
                                                Utils.launchURL(
                                                    adminBusinessController
                                                        .adminBusinessByIDModel
                                                        .value
                                                        .instagramLink);
                                              },
                                            ),
                                            const Gap(10),
                                            SocialMediaContainer(
                                              imagePath:
                                                  "assets/icons/FacebookLogo.svg",
                                              title: "Facebook",
                                              onTap: () {
                                                Utils.launchURL(
                                                    adminBusinessController
                                                        .adminBusinessByIDModel
                                                        .value
                                                        .facebookLink);
                                              },
                                            ),
                                            const Gap(10),
                                            SocialMediaContainer(
                                              imagePath:
                                                  "assets/icons/YoutubeLogo.svg",
                                              title: "YouTube",
                                              onTap: () {
                                                Utils.launchURL(
                                                    adminBusinessController
                                                        .adminBusinessByIDModel
                                                        .value
                                                        .youtubeLink);
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Divider(
                    thickness: 1.0,
                    color: AppColors.ktertiary.withOpacity(.3),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Container(
                      height: 46,
                      width: Get.width * .25,
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 4),
                      decoration: BoxDecoration(
                          color: AppColors.scaffoldColor,
                          borderRadius: BorderRadius.circular(50)),
                      child: Row(
                        children: [
                          OptionContainer(
                            text: 'About',
                            isSelected: isAbout,
                            onTap: () {
                              setState(() {
                                isAbout = true;
                                isEvent = false;
                                isClasses = false;
                                type = "about";
                              });
                              adminBusinessController.getAdminBusinesstByID(
                                  businessID: id, type: type);
                            },
                          ),
                          OptionContainer(
                            text: 'Events',
                            isSelected: isEvent,
                            onTap: () {
                              setState(() {
                                isAbout = false;
                                isEvent = true;
                                isClasses = false;
                                type = "events";
                              });
                              adminBusinessController.getAdminBusinesstByID(
                                  businessID: id, type: type);
                            },
                          ),
                          OptionContainer(
                            text: 'Classes',
                            isSelected: isClasses,
                            onTap: () {
                              setState(
                                () {
                                  isAbout = false;
                                  isEvent = false;
                                  isClasses = true;
                                  type = "classes";
                                },
                              );
                              adminBusinessController.getAdminBusinesstByID(
                                  businessID: id, type: type);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  isAbout
                      ? buildisAbout(adminBusinessController)
                      : isEvent
                          ? buildEvents(
                              eventController, adminBusinessController)
                          : buildClasses(classVM, adminBusinessController),
                  const Gap(20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

buildisAbout(AdminBusinessController adminBusinessController) {
  return Obx(
    () => adminBusinessController.isBusinessByIDLoading.value
        ? const Center(
            child: CircularProgressIndicator(),
          )
        : Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "KYC verification",
                  style: title2TextMedium,
                ),
                const Gap(15),
                SizedBox(
                  width: Get.width * .3,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Status",
                        style: bodyTextRegular.copyWith(
                            color: AppColors.txtsecondary),
                      ),
                      const Spacer(),
                      Text(
                        adminBusinessController
                                .adminBusinessByIDModel.value.kycStatus.isEmpty
                            ? "N/A"
                            : adminBusinessController.adminBusinessByIDModel
                                .value.kycStatus.capitalizeFirst
                                .toString(),
                        style: bodyTextRegular.copyWith(
                            color: adminBusinessController
                                        .adminBusinessByIDModel
                                        .value
                                        .kycStatus ==
                                    "approved"
                                ? AppColors.kprimarycolor
                                : adminBusinessController.adminBusinessByIDModel
                                            .value.kycStatus ==
                                        "pending"
                                    ? AppColors.kwarningbold
                                    : adminBusinessController
                                                .adminBusinessByIDModel
                                                .value
                                                .kycStatus ==
                                            "rejected"
                                        ? AppColors.errorRed
                                        : AppColors.txtprimary),
                      ),
                    ],
                  ),
                ),
                const Gap(15),
                Divider(
                  thickness: 1.0,
                  color: AppColors.ktertiary.withOpacity(.3),
                ),
                const Gap(15),
                Text(
                  "Point of contact",
                  style: title2TextMedium,
                ),
                const Gap(15),
                SizedBox(
                  width: Get.width * .3,
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Person name",
                            style: bodyTextRegular.copyWith(
                                color: AppColors.txtsecondary),
                          ),
                          const Spacer(),
                          Text(
                            adminBusinessController
                                .adminBusinessByIDModel.value.pocName,
                            style: bodyTextSemiBold,
                          ),
                        ],
                      ),
                      const Gap(15),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Phone number of PoC",
                            style: bodyTextRegular.copyWith(
                                color: AppColors.txtsecondary),
                          ),
                          const Spacer(),
                          Text(
                              adminBusinessController
                                  .adminBusinessByIDModel.value.pocNumber,
                              style: bodyTextSemiBold),
                        ],
                      ),
                      const Gap(15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Alternate Phone number of PoC",
                            style: bodyTextRegular,
                          ),
                          const Spacer(),
                          Text(
                              adminBusinessController.adminBusinessByIDModel
                                      .value.pocAltNumber.isEmpty
                                  ? "N/A"
                                  : adminBusinessController
                                      .adminBusinessByIDModel
                                      .value
                                      .pocAltNumber,
                              style: bodyTextSemiBold),
                        ],
                      ),
                    ],
                  ),
                ),
                const Gap(15),
                Divider(
                  thickness: 1.0,
                  color: AppColors.ktertiary.withOpacity(.3),
                ),
                const Gap(15),
                Text(
                  "Business details",
                  style: title2TextMedium,
                ),
                const Gap(20),
                Text(
                  "Type of business",
                  style:
                      bodyTextRegular.copyWith(color: AppColors.txtsecondary),
                ),
                const Gap(10),
                Text(
                  adminBusinessController
                          .adminBusinessByIDModel.value.businessType.isEmpty
                      ? "N/A"
                      : adminBusinessController
                          .adminBusinessByIDModel.value.businessType,
                  style: bodyTextSemiBold,
                ),
                const Gap(20),
                Text(
                  "About you business",
                  style:
                      bodyTextRegular.copyWith(color: AppColors.txtsecondary),
                ),
                const Gap(10),
                Text(
                  adminBusinessController.adminBusinessByIDModel.value
                          .businessDescription.isEmpty
                      ? "N/A"
                      : adminBusinessController
                          .adminBusinessByIDModel.value.businessDescription,
                  style: bodyTextSemiBold,
                ),
              ],
            ),
          ),
  );
}

buildEvents(EventController eventController,
    AdminBusinessController adminBusinessController) {
  return Padding(
    padding: const EdgeInsets.all(16),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Events created", style: title2TextRegular),
        const Gap(20),
        Obx(
          () => adminBusinessController.isEventsLoading.value
              ? const Center(child: CircularProgressIndicator())
              : adminBusinessController.adminBusinessByIDModel.value.events !=
                          null &&
                      adminBusinessController
                          .adminBusinessByIDModel.value.events!.isNotEmpty
                  ? GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        mainAxisExtent: 133,
                        crossAxisSpacing: 10.0,
                        mainAxisSpacing: 10.0,
                      ),
                      itemCount: adminBusinessController
                          .adminBusinessByIDModel.value.events!.length,
                      itemBuilder: (context, index) {
                        return GridAnimationWidget(
                          columnCount: 2,
                          index: index,
                          child: EventCell(
                            isAdmin: true,
                            eventModelObject: adminBusinessController
                                .adminBusinessByIDModel.value.events![index],
                            eventController: eventController,
                          ),
                        );
                      },
                    )
                  : const Center(
                      child: EmptyCaseContainer(type: "admin_event_published"),
                    ),
        ),
      ],
    ),
  );
}

buildClasses(
    ClassController classVM, AdminBusinessController adminBusinessController) {
  return Padding(
    padding: const EdgeInsets.all(16),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Classes created", style: title2TextRegular),
        const Gap(20),
        Obx(
          () => adminBusinessController.isClassesLoading.value
              ? const Center(child: CircularProgressIndicator())
              : adminBusinessController.adminBusinessByIDModel.value.classes !=
                          null &&
                      adminBusinessController
                          .adminBusinessByIDModel.value.classes!.isNotEmpty
                  ? GridView.builder(
                      shrinkWrap: true,
                      physics: const ScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        mainAxisExtent: 200,
                        crossAxisSpacing: 10.0,
                        mainAxisSpacing: 10.0,
                      ),
                      itemCount: adminBusinessController
                          .adminBusinessByIDModel.value.classes!.length,
                      itemBuilder: (context, index) {
                        return GridAnimationWidget(
                          columnCount: 2,
                          index: index,
                          child: MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: ClassCell(
                              isAdmin: true,
                              classModelObject: adminBusinessController
                                  .adminBusinessByIDModel.value.classes![index],
                              currentTab: "published",
                              classVM: classVM,
                            ),
                          ),
                        );
                      },
                    )
                  : const Center(
                      child: EmptyCaseContainer(type: "admin_class"),
                    ),
        ),
      ],
    ),
  );
}
