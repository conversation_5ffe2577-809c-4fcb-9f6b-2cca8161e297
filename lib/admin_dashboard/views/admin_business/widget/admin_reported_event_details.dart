import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_cell.dart';
import 'package:url_launcher/url_launcher.dart';

class AdminReportEventDetailsPage extends StatefulWidget {
  const AdminReportEventDetailsPage({super.key, required this.arguments});
  final Map<String, int> arguments;

  @override
  State<AdminReportEventDetailsPage> createState() =>
      _AdminReportEventDetailsPageState();
}

class _AdminReportEventDetailsPageState
    extends State<AdminReportEventDetailsPage> {
  int id = 0;
  int sortColumnIndex = 0;
  bool sortAscending = false;
  RxBool isApproveReject = true.obs;
  RxBool isRejectReport = false.obs;
  String selectedRejectReason = "";
  final _reasonController = TextEditingController().obs;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final AdminBusinessController adminBusinessController = Get.find<AdminBusinessController>();

  List<String> rejectReaons = [
    'Profanity',
    'Inappropriate content and imagery.',
    'Contact details are valid.',
    "Contact number should not be in description"
  ];

  @override
  void initState() {
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['eventID']!;
      });

      adminBusinessController.getAdminReportEventDetailsData(id);
    });
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    adminBusinessController.clearAdminReportEventData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: _buildPage(),
    );
  }

  _buildPage() {
    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
            width: Get.width * .7,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.ktertiary.withOpacity(.3), width: 1.0),
            ),
            child: Obx(
              () => adminBusinessController.isAdminReportEventByIDLoadiong.value
                  ? const ClassDetailsShimmer(isClass: false)
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 0.0),
                          child: Row(
                            children: [
                              InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                onTap: () {
                                  locator<NavigationServices>().goBack();
                                },
                                child: SvgPicture.asset(
                                    "assets/svg/arrow-left.svg"),
                              ),
                              const Gap(16),
                              Text(
                                "Event details",
                                style: title3TextSemiBold,
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12.0, vertical: 4.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: adminBusinessController
                                              .adminEventReportByID
                                              .value
                                              .status ==
                                          "inreview"
                                      ? AppColors.kwarning
                                      : adminBusinessController
                                                  .adminEventReportByID
                                                  .value
                                                  .status ==
                                              "rejected"
                                          ? AppColors.errorRed.withOpacity(.2)
                                          : Colors.green.withOpacity(.2),
                                ),
                                child: Text(
                                  adminBusinessController.adminEventReportByID
                                      .value.status.capitalizeFirst
                                      .toString(),
                                  style: body2TextBold.copyWith(
                                      color: adminBusinessController
                                                  .adminEventReportByID
                                                  .value
                                                  .status ==
                                              "inreview"
                                          ? Colors.yellow[800]
                                          : adminBusinessController
                                                      .adminEventReportByID
                                                      .value
                                                      .status ==
                                                  "rejected"
                                              ? AppColors.errorRed
                                              : Colors.green),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary.withOpacity(.3),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8.0, horizontal: 10.0),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12.0),
                                          child: CachedNetworkImage(
                                            imageUrl: adminBusinessController
                                                .adminEventReportByID
                                                .value
                                                .bannerUrl,
                                            width: Get.width,
                                            height: Get.height * .6,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) =>
                                                Image.asset(
                                                    "assets/png/Banner_Placeholder.png",
                                                    height: 450,
                                                    width: Get.width,
                                                    fit: BoxFit.cover),
                                            errorWidget: (context, url,
                                                    error) =>
                                                Image.asset(
                                                    "assets/png/Banner_Placeholder.png",
                                                    height: 450,
                                                    width: Get.width,
                                                    fit: BoxFit.cover),
                                          ),
                                        ),
                                        const Gap(10),
                                        Text(
                                          adminBusinessController
                                              .adminEventReportByID.value.title,
                                          style: title3TextBold,
                                        ),
                                        const Gap(10),
                                        Row(
                                          children: [
                                            adminBusinessController
                                                        .adminEventReportByID
                                                        .value
                                                        .eventType ==
                                                    "online"
                                                ? const EventRowChild(
                                                    iconPath:
                                                        "assets/icons/VideoConference.svg",
                                                    textTitle: "Online")
                                                : EventRowChild(
                                                    iconPath:
                                                        "assets/svg/MapPin.svg",
                                                    textTitle:
                                                        adminBusinessController
                                                            .adminEventReportByID
                                                            .value
                                                            .eventType,
                                                  ),
                                            const Gap(10),
                                            EventRowChild(
                                              iconPath: "assets/svg/Baby.svg",
                                              textTitle:
                                                  "${adminBusinessController.adminEventReportByID.value.minAge}-${adminBusinessController.adminEventReportByID.value.maxAge} years",
                                            ),
                                            const Gap(10),
                                            EventRowChild(
                                              iconPath:
                                                  "assets/svg/ClockClockwise.svg",
                                              textTitle:
                                                  "${adminBusinessController.adminEventReportByID.value.duration} minutes",
                                            ),
                                          ],
                                        ),
                                        const Gap(10),
                                        ListTile(
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 0, vertical: 0),
                                          title: Text(
                                            "About event",
                                            style: bodyTextBold,
                                          ),
                                          subtitle: Text(
                                            adminBusinessController
                                                .adminEventReportByID
                                                .value
                                                .description,
                                            style: body2TextMedium,
                                          ),
                                        ),
                                        const Gap(10),
                                        ListTile(
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 0, vertical: 0),
                                          title: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "Organiser",
                                                style: title3TextSemiBold,
                                              ),
                                              const Gap(8),
                                            ],
                                          ),
                                          subtitle: Row(
                                            children: [
                                              adminBusinessController
                                                      .adminEventReportByID
                                                      .value
                                                      .businesslogo!
                                                      .isEmpty
                                                  ? SvgPicture.asset(
                                                      "assets/icons/empty_logo.svg",
                                                      height: 34,
                                                      width: 34,
                                                      fit: BoxFit.cover)
                                                  : CircleAvatar(
                                                      maxRadius: 14,
                                                      backgroundImage: NetworkImage(
                                                          adminBusinessController
                                                              .adminEventReportByID
                                                              .value
                                                              .businesslogo!),
                                                    ),
                                              const Gap(10),
                                              Text(
                                                adminBusinessController
                                                    .adminEventReportByID
                                                    .value
                                                    .businessName,
                                                style: bodyTextRegular,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Gap(20),
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                            color: AppColors.ktertiary
                                                .withOpacity(.3),
                                            width: 1.0),
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          EventColumChild(
                                            subTitle: _formatDateTime(
                                                DateTime.parse(
                                                    adminBusinessController
                                                        .adminEventReportByID
                                                        .value
                                                        .startDate)),
                                            title: "Start date",
                                          ),
                                          Divider(
                                            thickness: 1.0,
                                            color: AppColors.ktertiary
                                                .withOpacity(.3),
                                          ),
                                          EventColumChild(
                                            subTitle: _formatDateTime(
                                                DateTime.parse(
                                                    adminBusinessController
                                                        .adminEventReportByID
                                                        .value
                                                        .endDate)),
                                            title: "End date",
                                          ),
                                          Divider(
                                            thickness: 1.0,
                                            color: AppColors.ktertiary
                                                .withOpacity(.3),
                                          ),
                                          EventColumChild(
                                            subTitle: adminBusinessController
                                                        .adminEventReportByID
                                                        .value
                                                        .price ==
                                                    0
                                                ? "Free"
                                                : "₹${adminBusinessController.adminEventReportByID.value.price} ${adminBusinessController.adminEventReportByID.value.price == 1 ? '' : 'onwards'}",
                                            title: "Price",
                                          ),
                                          Divider(
                                            thickness: 1.0,
                                            color: AppColors.ktertiary
                                                .withOpacity(.3),
                                          ),
                                          adminBusinessController
                                                  .adminEventReportByID
                                                  .value
                                                  .ctaUrl
                                                  .isNotEmpty
                                              ? Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                      onTap: () {
                                                        _launchURL(
                                                            adminBusinessController
                                                                .adminEventReportByID
                                                                .value
                                                                .ctaUrl);
                                                      },
                                                      child: EventColumChild(
                                                        subTitle:
                                                            adminBusinessController
                                                                .adminEventReportByID
                                                                .value
                                                                .ctaUrl,
                                                        title: "Booking Url",
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    EventColumChild(
                                                      subTitle:
                                                          "+91-${adminBusinessController.adminEventReportByID.value.ctaMobile}",
                                                      title: "Contact",
                                                    ),
                                                  ],
                                                ),
                                          adminBusinessController
                                                      .adminEventReportByID
                                                      .value
                                                      .eventType ==
                                                  "Online"
                                              ? const SizedBox.shrink()
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    const Gap(5),
                                                    Divider(
                                                      thickness: 1.0,
                                                      color: AppColors.ktertiary
                                                          .withOpacity(.3),
                                                    ),
                                                    EventColumChild(
                                                      subTitle: adminBusinessController
                                                              .adminEventReportByID
                                                              .value
                                                              .city
                                                              .isEmpty
                                                          ? "-"
                                                          : adminBusinessController
                                                              .adminEventReportByID
                                                              .value
                                                              .city,
                                                      title: "City",
                                                    ),
                                                    Divider(
                                                      thickness: 1.0,
                                                      color: AppColors.ktertiary
                                                          .withOpacity(.3),
                                                    ),
                                                    EventColumChild(
                                                      subTitle: adminBusinessController
                                                              .adminEventReportByID
                                                              .value
                                                              .address
                                                              .isEmpty
                                                          ? "-"
                                                          : adminBusinessController
                                                              .adminEventReportByID
                                                              .value
                                                              .address,
                                                      title: "Address",
                                                    ),
                                                  ],
                                                ),
                                        ],
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Gap(20),
                        ActivityLog(
                          entries: adminBusinessController
                              .adminEventReportByID.value.activityLogs
                              .map((log) {
                            return ActivityEntry(
                              title: log.message,
                              dateTime: DateTime.parse(log.time),
                            );
                          }).toList(),
                        ),
                        const Gap(20),
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary.withOpacity(.3),
                        ),
                        const Gap(20),
                        Text("Report summary", style: title3TextSemiBold),
                        const Gap(10),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: ConstrainedBox(
                            constraints: BoxConstraints(
                                minWidth: MediaQuery.of(context).size.width),
                            child: DataTable(
                              headingTextStyle: body2TextRegular.copyWith(
                                  color: AppColors.txtsecondary),
                              dataTextStyle: bodyTextRegular,
                              sortColumnIndex: sortColumnIndex,
                              sortAscending: sortAscending,
                              border: TableBorder(
                                borderRadius: BorderRadius.circular(12),
                                left: const BorderSide(
                                    color: AppColors.bordergrey),
                                right: const BorderSide(
                                    color: AppColors.bordergrey),
                                top: const BorderSide(
                                    color: AppColors.bordergrey),
                                bottom: const BorderSide(
                                    color: AppColors.bordergrey),
                                verticalInside: BorderSide.none,
                              ),
                              headingRowColor: MaterialStateColor.resolveWith(
                                  (states) => AppColors.klightwhite),
                              columnSpacing: 20,
                              dataRowMaxHeight: 70,
                              columns: [
                                DataColumn(
                                    label: Text('Sr No',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                                DataColumn(
                                    label: Text('Reported by',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                                DataColumn(
                                    label: Text('Reported on',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                                DataColumn(
                                    label: Text('Reason',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                                DataColumn(
                                    label: Text('Event name',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                                DataColumn(
                                    label: Text('Attachments',
                                        style: body2TextRegular.copyWith(
                                            color: AppColors.txtsecondary))),
                              ],
                              rows: List<DataRow>.generate(
                                adminBusinessController.adminEventReportByID
                                    .value.reportSummary.length,
                                (index) {
                                  final report = adminBusinessController
                                      .adminEventReportByID
                                      .value
                                      .reportSummary[index];
                                  return DataRow(
                                    cells: [
                                      DataCell(Text("${index + 1}",
                                          style: bodyTextRegular)),
                                      DataCell(Text(report.reportedBy,
                                          style: bodyTextRegular)),
                                      DataCell(Text(
                                          formatDateTimeStamp(
                                              report.reportedOn),
                                          style: bodyTextRegular)),
                                      DataCell(Text(report.reason,
                                          style: bodyTextRegular)),
                                      DataCell(
                                        SizedBox(
                                          width: 240,
                                          child: Text(
                                            report.eventName,
                                            maxLines: 4,
                                            overflow: TextOverflow.ellipsis,
                                            style: bodyTextRegular,
                                          ),
                                        ),
                                      ),
                                      DataCell(Center(
                                        child: IconButton(
                                            onPressed: () {
                                              log("Attachments");
                                              _showImageDialog(
                                                  context, report.attachment);
                                            },
                                            icon: const Icon(
                                                Icons.remove_red_eye_outlined,
                                                color:
                                                    AppColors.kprimarycolor)),
                                      )),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                        const Gap(30),
                        Obx(() => adminBusinessController
                                    .adminEventReportByID.value.status ==
                                "inreview"
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Divider(),
                                  const Gap(30),
                                  Text("Take Action",
                                      style: title3TextSemiBold),
                                  const Gap(20),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text("Choose your response",
                                                style: body2TextRegular),
                                            const Gap(10),
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                    onTap: () {
                                                      setState(() {
                                                        isApproveReject.value =
                                                            true;
                                                        isRejectReport.value =
                                                            false;
                                                      });
                                                    },
                                                    child: Container(
                                                      height: 42,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            const BorderRadius
                                                                .horizontal(
                                                          left: Radius.circular(
                                                              12),
                                                        ),
                                                        color: isApproveReject
                                                                .value
                                                            ? AppColors.kwhite
                                                            : AppColors
                                                                .scaffoldColor,
                                                        border: Border.all(
                                                          color: isApproveReject
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .bordergrey,
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          "Approve report",
                                                          style: body2TextSemiBold.copyWith(
                                                              color: isApproveReject
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .txtsecondary),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                                    onTap: () {
                                                      setState(() {
                                                        isApproveReject.value =
                                                            false;
                                                        isRejectReport.value =
                                                            true;
                                                      });
                                                    },
                                                    child: Container(
                                                      height: 42,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            const BorderRadius
                                                                .horizontal(
                                                          right:
                                                              Radius.circular(
                                                                  12),
                                                        ),
                                                        color: isRejectReport
                                                                .value
                                                            ? AppColors.kwhite
                                                            : AppColors
                                                                .scaffoldColor,
                                                        border: Border.all(
                                                          color: isRejectReport
                                                                  .value
                                                              ? AppColors
                                                                  .txtprimary
                                                              : AppColors
                                                                  .bordergrey,
                                                        ),
                                                      ),
                                                      child: Center(
                                                        child: Text(
                                                          "Reject report",
                                                          style: body2TextSemiBold.copyWith(
                                                              color: isRejectReport
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .txtsecondary),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            isRejectReport.value
                                                ? Form(
                                                    key: _formKey,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Gap(10),
                                                        Text(
                                                            "Describe the reason* (This will be conveyed to the organiser)",
                                                            style:
                                                                body2TextRegular),
                                                        const Gap(10),
                                                        CustomTextFormField(
                                                          controller:
                                                              _reasonController
                                                                  .value,
                                                          onChanged: (val) {},
                                                          hintText:
                                                              "Write here",
                                                          maxLines: 4,
                                                          maxLength: 150,
                                                          validator: (value) {
                                                            if (value == null ||
                                                                value.isEmpty) {
                                                              return 'Please enter the reason.';
                                                            }
                                                            return null;
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                : const SizedBox.shrink(),
                                            //     ? Column(
                                            //         crossAxisAlignment:
                                            //             CrossAxisAlignment.start,
                                            //         children: [
                                            //           Text("Reason for rejection*",
                                            //               style: body2TextRegular),
                                            //           const Gap(10),
                                            //           CustomDropdownFormField(
                                            //             items: rejectReaons
                                            //                 .map(
                                            //                   (item) =>
                                            //                       DropdownMenuItem(
                                            //                     value: item,
                                            //                     child: Text(item),
                                            //                   ),
                                            //                 )
                                            //                 .toList(),
                                            //             hintText: "Select",
                                            //             onChanged: (value) {
                                            //               selectedRejectReason =
                                            //                   value!;
                                            //               log("Selected Rejection Reason: $selectedRejectReason");
                                            //             },
                                            //             validator: (value) {
                                            //               if (value == null ||
                                            //                   value.isEmpty) {
                                            //                 return 'Please select a Personal ID Type';
                                            //               }
                                            //               return null;
                                            //             },
                                            //           ),
                                            //         ],
                                            //       )
                                            //     : Column(
                                            //         crossAxisAlignment:
                                            //             CrossAxisAlignment.start,
                                            //         children: [
                                            //           Text(
                                            //               "Describe the reason* (This will be conveyed to the organiser)",
                                            //               style: body2TextRegular),
                                            //           const Gap(10),
                                            //           CustomTextFormField(
                                            //             controller:
                                            //                 _reasonController.value,
                                            //             onChanged: (val) {},
                                            //             hintText: "Write here",
                                            //             maxLines: 4,
                                            //             maxLength: 150,
                                            //             validator: (value) {
                                            //               if (value == null ||
                                            //                   value.isEmpty) {
                                            //                 return 'Please enter the reason.';
                                            //               }
                                            //               return null;
                                            //             },
                                            //           ),
                                            //         ],
                                            //       ),
                                            const Gap(30),
                                            PrimaryButton(
                                              text: "Confirm Decision",
                                              onTap: () {
                                                log("${isApproveReject.value}");
                                                if (isApproveReject.value ||
                                                    (_formKey.currentState
                                                            ?.validate() ??
                                                        false)) {
                                                  Future<void> action() async {
                                                    bool result;
                                                    if (isApproveReject.value) {
                                                      result =
                                                          await adminBusinessController
                                                              .actionAdminReportData(
                                                        id: adminBusinessController
                                                            .adminEventReportByID
                                                            .value
                                                            .eventId,
                                                        reason: "",
                                                        type: "event",
                                                        action: "accept",
                                                      );
                                                    } else {
                                                      result =
                                                          await adminBusinessController
                                                              .actionAdminReportData(
                                                        id: adminBusinessController
                                                            .adminEventReportByID
                                                            .value
                                                            .eventId,
                                                        reason:
                                                            _reasonController
                                                                .value.text,
                                                        type: "event",
                                                        action: "reject",
                                                      );
                                                    }
                                                    if (result) {
                                                      adminBusinessController
                                                          .getAdminBusinessReportsData(
                                                              type: "events");
                                                      locator<NavigationServices>()
                                                          .goBack();
                                                    }
                                                  }

                                                  action();
                                                }
                                              },
                                            ),
                                            const Gap(30),
                                          ],
                                        ),
                                      ),
                                      const Gap(30),
                                      Expanded(
                                        child: Container(
                                          width: Get.width,
                                          padding: const EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              border: Border.all(
                                                  color: AppColors.bordergrey)),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                  "If you approved this report as Invalid:",
                                                  style: body2TextSemiBold),
                                              const Gap(5),
                                              const BulletText(
                                                  text:
                                                      "The event/ class will be unpublished immediately."),
                                              const Gap(5),
                                              const BulletText(
                                                  text:
                                                      "The business account will be suspended if reports warnings are ignored"),
                                              const Gap(15),
                                              Text(
                                                  "If you approve this report as valid:",
                                                  style: body2TextSemiBold),
                                              const Gap(5),
                                              const BulletText(
                                                  text:
                                                      "The event/ class will remain live to users"),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            : const SizedBox.shrink()),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }

  void _showImageDialog(BuildContext context, String attachment) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: AppColors.kwhite,
          child: Stack(
            children: [
              SizedBox(
                width: Get.width / 2,
                height: 600,
                child: attachment.isNotEmpty
                    ? Image.network(
                        attachment,
                        fit: BoxFit.cover,
                        loadingBuilder: (BuildContext context, Widget child,
                            ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                              child: Text('No image available',
                                  style: bodyTextRegular));
                        },
                      )
                    : Center(
                        child:
                            Text('No image available', style: bodyTextRegular)),
              ),
              Positioned(
                top: 6,
                right: 10,
                child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      // shape: BoxShape.circle,
                      color: AppColors.kprimarycolor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.kwhite,
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mma');
    return formatter.format(dateTime);
  }

  Future<void> _launchURL(String urlString, {bool isPhone = false}) async {
    // Ensure URL has proper scheme for web URLs
    String processedUrl = urlString;
    if (!isPhone &&
        !urlString.startsWith('http://') &&
        !urlString.startsWith('https://')) {
      processedUrl = 'https://$urlString';
    }

    final Uri url = Uri.parse(processedUrl);
    final Uri launchPhone = Uri(
      scheme: 'tel',
      path: urlString,
    );
    final Uri targetUri = isPhone ? launchPhone : url;

    if (!await canLaunchUrl(targetUri)) {
      if (kIsWeb && !isPhone) {
        try {
          await launchUrl(
            targetUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return;
        } catch (e) {
          throw Exception('Could not launch $targetUri: $e');
        }
      }
      throw Exception('Could not launch $targetUri');
    }

    // Use different launch modes based on platform
    if (kIsWeb) {
      // For web, force external browser
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
        webOnlyWindowName: '_blank',
      );
    } else {
      // For mobile platforms
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
      );
    }
  }
}

class ActivityLog extends StatelessWidget {
  final List<ActivityEntry> entries;

  const ActivityLog({super.key, required this.entries});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Activity log',
          style: title3TextSemiBold,
        ),
        const Gap(15),
        for (var entry in entries) _buildTimelineEntry(context, entry),
      ],
    );
  }

  Widget _buildTimelineEntry(BuildContext context, ActivityEntry entry) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.kprimarycolor,
              ),
            ),
            if (entry != entries.last)
              Container(
                width: 2,
                height: 40,
                color: AppColors.kprimarycolor,
              ),
          ],
        ),
        const Gap(15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                entry.title,
                style: body2TextMedium,
              ),
              Text(
                _formatDateTime(entry.dateTime),
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              if (entry.by != null) Text('By ${entry.by}'),
              if (entry.reason != null)
                Text(
                  'Reason: ${entry.reason}',
                  style:
                      body3TextRegular.copyWith(color: AppColors.txtsecondary),
                ),
              const Gap(15),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mma');
    return formatter.format(dateTime);
  }
}

class ActivityEntry {
  final String title;
  final DateTime dateTime;
  final String? by;
  final String? reason;

  ActivityEntry({
    required this.title,
    required this.dateTime,
    this.by,
    this.reason,
  });
}

class BulletText extends StatelessWidget {
  final String text;
  const BulletText({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Text("• $text",
        style: body2TextRegular.copyWith(color: AppColors.txtsecondary));
  }
}
