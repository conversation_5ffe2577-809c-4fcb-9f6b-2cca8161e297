import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/admin_business_event_details.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';

class AdminClassDetailsPage extends StatefulWidget {
  const AdminClassDetailsPage({super.key, required this.arguments});
  final Map<String, int> arguments;

  @override
  State<AdminClassDetailsPage> createState() => _AdminClassDetailsPageState();
}

class _AdminClassDetailsPageState extends State<AdminClassDetailsPage> {
  final ClassController classVM = Get.find<ClassController>();
  final AdminBusinessController adminBusinessController =
      Get.find<AdminBusinessController>();
  int id = 0;
  int sortColumnIndex = 0;
  bool sortAscending = false;
  RxBool isApproveClass = true.obs;
  RxBool isRejectClass = false.obs;
  String selectedRejectReason = "";
  bool isReasonSelected = false;
  final _reasonController = TextEditingController().obs;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // bool isButtonActive() {
  //   return !isRejectClass.value || (isRejectClass.value && isReasonSelected);
  // }

  List<String> rejectReaons = [
    'Profanity',
    'Inappropriate content and imagery.',
    'Contact details are Invalid.',
    "Contact number should not be in description"
  ];

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['requestID']!;
      });
      adminBusinessController.getAdminClassDetailsData(id);
    });
  }

  @override
  void dispose() {
    super.dispose();
    adminBusinessController.clearAdminClassData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Container(
              width: Get.width * .7,
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
                border: Border.all(
                  color: AppColors.kgrey,
                ),
              ),
              child: Obx(
                () => adminBusinessController.isClassByIDLoading.value
                    ? const ClassDetailsShimmer()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16.0, vertical: 0.0),
                            child: Row(
                              children: [
                                InkWell(
                                  hoverColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  overlayColor: MaterialStateProperty.all(
                                      Colors.transparent),
                                  onTap: () {
                                    locator<NavigationServices>().goBack();
                                  },
                                  child: SvgPicture.asset(
                                      "assets/svg/arrow-left.svg"),
                                ),
                                const Gap(16),
                                Text(
                                  "Classes details",
                                  style: title3TextSemiBold,
                                ),
                                const Spacer(),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0, vertical: 4.0),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: adminBusinessController
                                                  .adminClassByIDModel
                                                  .value
                                                  .status ==
                                              "inreview"
                                          ? AppColors.kwarning
                                          : adminBusinessController
                                                      .adminClassByIDModel
                                                      .value
                                                      .status ==
                                                  'rejected'
                                              ? AppColors.errorRed
                                                  .withOpacity(.2)
                                              : adminBusinessController
                                                          .adminClassByIDModel
                                                          .value
                                                          .status ==
                                                      'draft'
                                                  ? Colors.deepPurpleAccent
                                                      .withOpacity(.2)
                                                  : Colors.green
                                                      .withOpacity(.2)),
                                  child: Text(
                                    adminBusinessController.adminClassByIDModel
                                        .value.status.capitalizeFirst
                                        .toString(),
                                    style: body2TextBold.copyWith(
                                        color: adminBusinessController
                                                    .adminClassByIDModel
                                                    .value
                                                    .status ==
                                                "inreview"
                                            ? Colors.yellow[800]
                                            : adminBusinessController
                                                        .adminClassByIDModel
                                                        .value
                                                        .status ==
                                                    'rejected'
                                                ? AppColors.errorRed
                                                : adminBusinessController
                                                            .adminClassByIDModel
                                                            .value
                                                            .status ==
                                                        'draft'
                                                    ? Colors.deepPurpleAccent
                                                    : Colors.green),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20.0, vertical: 10),
                            child: Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            adminBusinessController
                                                .adminClassByIDModel
                                                .value
                                                .title,
                                            style: heading2TextMedium,
                                            maxLines: 2,
                                          ),
                                          kMinHeight,
                                          Row(
                                            children: [
                                              adminBusinessController
                                                          .adminClassByIDModel
                                                          .value
                                                          .classType ==
                                                      "Online"
                                                  ? const ClassRowChild(
                                                      iconPath:
                                                          "assets/icons/VideoConference.svg",
                                                      textTitle: "Online")
                                                  : ClassRowChild(
                                                      iconPath:
                                                          "assets/svg/MapPin.svg",
                                                      textTitle:
                                                          adminBusinessController
                                                              .adminClassByIDModel
                                                              .value
                                                              .classType
                                                              .capitalizeFirst
                                                              .toString(),
                                                    ),
                                              const Gap(15),
                                              ClassRowChild(
                                                iconPath: "assets/svg/Baby.svg",
                                                textTitle:
                                                    "${adminBusinessController.adminClassByIDModel.value.minAge} - ${adminBusinessController.adminClassByIDModel.value.maxAge} years",
                                              ),
                                              const Gap(15),
                                              ClassRowChild(
                                                  iconPath:
                                                      "assets/svg/group.svg",
                                                  textTitle:
                                                      adminBusinessController
                                                          .adminClassByIDModel
                                                          .value
                                                          .sessionType),
                                            ],
                                          ),
                                          kMinHeight,
                                          Text(
                                            "About Class",
                                            style: title3TextSemiBold,
                                          ),
                                          kSmHeight,
                                          Text(
                                            adminBusinessController
                                                .adminClassByIDModel
                                                .value
                                                .description,
                                            style: body2TextMedium.copyWith(
                                                color: AppColors.secondary),
                                            maxLines: 5,
                                          ),
                                          const Gap(20),
                                          ListTile(
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 0, vertical: 0),
                                            title: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "Organiser",
                                                  style: title3TextSemiBold,
                                                ),
                                                const Gap(10),
                                              ],
                                            ),
                                            subtitle: Row(
                                              children: [
                                                adminBusinessController
                                                        .adminClassByIDModel
                                                        .value
                                                        .businesslogo
                                                        .isEmpty
                                                    ? SvgPicture.asset(
                                                        "assets/icons/empty_logo.svg",
                                                        height: 34,
                                                        width: 34,
                                                        fit: BoxFit.cover)
                                                    : CircleAvatar(
                                                        maxRadius: 14,
                                                        backgroundImage: NetworkImage(
                                                            adminBusinessController
                                                                .adminClassByIDModel
                                                                .value
                                                                .businesslogo),
                                                      ),
                                                const Gap(10),
                                                Text(
                                                  adminBusinessController
                                                      .adminClassByIDModel
                                                      .value
                                                      .businessName,
                                                  style: bodyTextRegular,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Gap(30),
                                    Expanded(
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: CachedNetworkImage(
                                          imageUrl: adminBusinessController
                                              .adminClassByIDModel
                                              .value
                                              .bannerUrl,
                                          width: Get.width,
                                          height: Get.height * .4,
                                          fit: BoxFit.fitHeight,
                                          errorWidget: (context, url, error) =>
                                              Image.asset(
                                                  "assets/png/class_list_empty.png",
                                                  height: 340,
                                                  width: 340,
                                                  fit: BoxFit.cover),
                                          placeholder: (context, url) =>
                                              Image.asset(
                                                  "assets/png/class_list_empty.png",
                                                  height: 340,
                                                  width: 340,
                                                  fit: BoxFit.cover),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Gap(10),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          ClassCategoryRow(
                              title: 'Category',
                              content: adminBusinessController
                                  .adminClassByIDModel.value.category),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          ClassCategoryRow(
                            title: 'Sub-Category',
                            content: adminBusinessController
                                .adminClassByIDModel.value.subCategory,
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          ClassCategoryRow(
                              title: 'Class fee',
                              content: adminBusinessController
                                          .adminClassByIDModel.value.price ==
                                      0
                                  ? "Free"
                                  : "₹${adminBusinessController.adminClassByIDModel.value.price} ${adminBusinessController.adminClassByIDModel.value.price == 1 ? '' : 'onwards'}"),
                          classVM.classDetailsModel.value.ctamobile.isEmpty
                              ? const SizedBox.shrink()
                              : Column(
                                  children: [
                                    const Divider(
                                      thickness: 1.0,
                                      color: AppColors.kgrey,
                                    ),
                                    ClassCategoryRow(
                                      title: 'Phone',
                                      content: adminBusinessController
                                              .adminClassByIDModel
                                              .value
                                              .ctaMobile
                                              .isEmpty
                                          ? "-"
                                          : adminBusinessController
                                              .adminClassByIDModel
                                              .value
                                              .ctaMobile,
                                    ),
                                  ],
                                ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          ClassCategoryRow(
                            title: 'City',
                            content: adminBusinessController
                                    .adminClassByIDModel.value.city.isEmpty
                                ? "-"
                                : adminBusinessController
                                    .adminClassByIDModel.value.city,
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          ClassCategoryRow(
                            title: 'Address',
                            content: adminBusinessController
                                    .adminClassByIDModel.value.address.isEmpty
                                ? "-"
                                : adminBusinessController
                                    .adminClassByIDModel.value.address,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Divider(
                                  thickness: 1.0,
                                  color: AppColors.ktertiary.withOpacity(.3),
                                ),
                                const Gap(20),
                                ActivityLog(
                                    entries: adminBusinessController
                                        .adminClassByIDModel.value.activityLogs
                                        .map((log) {
                                  return ActivityEntry(
                                      title: log.message,
                                      submissionDate:
                                          formatDateTimeStamp(log.time));
                                }).toList()),
                                if (adminBusinessController.adminClassByIDModel
                                        .value.reportSummary.isNotEmpty &&
                                    adminBusinessController.adminClassByIDModel
                                            .value.reportSummary.first.id >
                                        0)
                                  Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Gap(10),
                                      Divider(
                                        thickness: 1.0,
                                        color:
                                            AppColors.ktertiary.withOpacity(.3),
                                      ),
                                      const Gap(10),
                                      Text("Report summary",
                                          style: title3TextSemiBold),
                                      const Gap(10),
                                      SingleChildScrollView(
                                        scrollDirection: Axis.horizontal,
                                        child: ConstrainedBox(
                                          constraints: BoxConstraints(
                                              minWidth: MediaQuery.of(context)
                                                  .size
                                                  .width),
                                          child: DataTable(
                                            headingTextStyle:
                                                body2TextRegular.copyWith(
                                                    color:
                                                        AppColors.txtsecondary),
                                            dataTextStyle: bodyTextRegular,
                                            sortColumnIndex: sortColumnIndex,
                                            sortAscending: sortAscending,
                                            border: TableBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              left: const BorderSide(
                                                  color: AppColors.bordergrey),
                                              right: const BorderSide(
                                                  color: AppColors.bordergrey),
                                              top: const BorderSide(
                                                  color: AppColors.bordergrey),
                                              bottom: const BorderSide(
                                                  color: AppColors.bordergrey),
                                              verticalInside: BorderSide.none,
                                            ),
                                            headingRowColor:
                                                MaterialStateColor.resolveWith(
                                                    (states) =>
                                                        AppColors.klightwhite),
                                            dataRowMaxHeight: 56,
                                            columns: [
                                              DataColumn(
                                                  label: Text('Sr No',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                              DataColumn(
                                                  label: Text('Reported by',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                              DataColumn(
                                                  label: Text('Reported on',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                              DataColumn(
                                                  label: Text('Reason',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                              DataColumn(
                                                  label: Text('Class name',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                              DataColumn(
                                                  label: Text('Attachments',
                                                      style: body2TextRegular
                                                          .copyWith(
                                                              color: AppColors
                                                                  .txtsecondary))),
                                            ],
                                            rows: List<DataRow>.generate(
                                              adminBusinessController
                                                  .adminClassByIDModel
                                                  .value
                                                  .reportSummary
                                                  .length,
                                              (index) {
                                                final report =
                                                    adminBusinessController
                                                        .adminClassByIDModel
                                                        .value
                                                        .reportSummary[index];
                                                return DataRow(
                                                  cells: [
                                                    DataCell(Text(
                                                        "${index + 1}",
                                                        style:
                                                            bodyTextRegular)),
                                                    DataCell(Text(
                                                        report.reportedBy,
                                                        style:
                                                            bodyTextRegular)),
                                                    DataCell(Text(
                                                        formatDateTimeStamp(
                                                            report.reportedOn),
                                                        style:
                                                            bodyTextRegular)),
                                                    DataCell(Text(report.reason,
                                                        style:
                                                            bodyTextRegular)),
                                                    DataCell(
                                                      SizedBox(
                                                        width: 240,
                                                        child: Text(
                                                          report.eventName,
                                                          maxLines: 4,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          style:
                                                              bodyTextRegular,
                                                        ),
                                                      ),
                                                    ),
                                                    DataCell(Center(
                                                      child: IconButton(
                                                          onPressed: () {
                                                            log("Attachments");
                                                            _showImageDialog(
                                                                context,
                                                                report
                                                                    .attachment);
                                                          },
                                                          icon: const Icon(
                                                              Icons
                                                                  .remove_red_eye_outlined,
                                                              color: AppColors
                                                                  .kprimarycolor)),
                                                    )),
                                                  ],
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                      const Gap(30),
                                    ],
                                  )
                              ],
                            ),
                          ),
                          Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: adminBusinessController
                                          .adminClassByIDModel.value.status ==
                                      "inreview"
                                  ? Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Divider(),
                                        const Gap(10),
                                        Text("Take Action",
                                            style: title3TextSemiBold),
                                        const Gap(10),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text("Choose your response",
                                                      style: body2TextRegular),
                                                  const Gap(10),
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: InkWell(
                                                          hoverColor: Colors
                                                              .transparent,
                                                          splashColor: Colors
                                                              .transparent,
                                                          highlightColor: Colors
                                                              .transparent,
                                                          overlayColor:
                                                              MaterialStateProperty
                                                                  .all(Colors
                                                                      .transparent),
                                                          onTap: () {
                                                            setState(() {
                                                              isApproveClass
                                                                  .value = true;
                                                              isRejectClass
                                                                      .value =
                                                                  false;
                                                            });
                                                          },
                                                          child: Container(
                                                            height: 42,
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  const BorderRadius
                                                                      .horizontal(
                                                                left: Radius
                                                                    .circular(
                                                                        12),
                                                              ),
                                                              color: isApproveClass
                                                                      .value
                                                                  ? AppColors
                                                                      .kwhite
                                                                  : AppColors
                                                                      .scaffoldColor,
                                                              border:
                                                                  Border.all(
                                                                color: isApproveClass
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .bordergrey,
                                                              ),
                                                            ),
                                                            child: Center(
                                                              child: Text(
                                                                "Approve",
                                                                style: body2TextSemiBold.copyWith(
                                                                    color: isApproveClass.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .txtsecondary),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: InkWell(
                                                          hoverColor: Colors
                                                              .transparent,
                                                          splashColor: Colors
                                                              .transparent,
                                                          highlightColor: Colors
                                                              .transparent,
                                                          overlayColor:
                                                              MaterialStateProperty
                                                                  .all(Colors
                                                                      .transparent),
                                                          onTap: () {
                                                            setState(() {
                                                              isApproveClass
                                                                      .value =
                                                                  false;
                                                              isRejectClass
                                                                  .value = true;
                                                            });
                                                          },
                                                          child: Container(
                                                            height: 42,
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  const BorderRadius
                                                                      .horizontal(
                                                                right: Radius
                                                                    .circular(
                                                                        12),
                                                              ),
                                                              color: isRejectClass
                                                                      .value
                                                                  ? AppColors
                                                                      .kwhite
                                                                  : AppColors
                                                                      .scaffoldColor,
                                                              border:
                                                                  Border.all(
                                                                color: isRejectClass
                                                                        .value
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .bordergrey,
                                                              ),
                                                            ),
                                                            child: Center(
                                                              child: Text(
                                                                "Reject",
                                                                style: body2TextSemiBold.copyWith(
                                                                    color: isRejectClass.value
                                                                        ? AppColors
                                                                            .txtprimary
                                                                        : AppColors
                                                                            .txtsecondary),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const Gap(20),
                                                  isRejectClass.value
                                                      ? Form(
                                                          key: _formKey,
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              const Gap(10),
                                                              Text(
                                                                  "Reason for rejection*",
                                                                  style:
                                                                      body2TextRegular),
                                                              const Gap(10),

                                                              CustomTextFormField(
                                                                controller:
                                                                    _reasonController
                                                                        .value,
                                                                onChanged:
                                                                    (val) {},
                                                                hintText:
                                                                    "Write here",
                                                                maxLines: 4,
                                                                maxLength: 150,
                                                                validator:
                                                                    (value) {
                                                                  if (value ==
                                                                          null ||
                                                                      value
                                                                          .isEmpty) {
                                                                    return 'Please enter the reason.';
                                                                  }
                                                                  return null;
                                                                },
                                                              ),
                                                              // CustomDropdownFormField(
                                                              //   items: rejectReaons
                                                              //       .map(
                                                              //         (item) =>
                                                              //             DropdownMenuItem(
                                                              //           value: item,
                                                              //           child: Text(
                                                              //               item),
                                                              //         ),
                                                              //       )
                                                              //       .toList(),
                                                              //   hintText: "Select",
                                                              //   onChanged: (value) {
                                                              //     selectedRejectReason =
                                                              //         value!;
                                                              //     isReasonSelected =
                                                              //         true;
                                                              //     log("Selected Rejection Reason: $selectedRejectReason");
                                                              //   },
                                                              //   validator: (value) {
                                                              //     if (value ==
                                                              //             null ||
                                                              //         value
                                                              //             .isEmpty) {
                                                              //       return 'Please select a rejection type';
                                                              //     }
                                                              //     return null;
                                                              //   },
                                                              // ),
                                                            ],
                                                          ),
                                                        )
                                                      : const SizedBox.shrink(),
                                                  const Gap(30),
                                                  PrimaryButton(
                                                      text: "Confirm Decision",
                                                      onTap:
                                                          handleConfirmDecision),
                                                  const Gap(30),
                                                ],
                                              ),
                                            ),
                                            const Gap(30),
                                            const Spacer(),
                                            // isRejectClass.value
                                            //     ? Expanded(
                                            //         child: Container(
                                            //           width: Get.width,
                                            //           padding:
                                            //               const EdgeInsets.all(
                                            //                   12),
                                            //           decoration: BoxDecoration(
                                            //               borderRadius:
                                            //                   BorderRadius
                                            //                       .circular(8),
                                            //               border: Border.all(
                                            //                   color: AppColors
                                            //                       .bordergrey)),
                                            //           child: Column(
                                            //             crossAxisAlignment:
                                            //                 CrossAxisAlignment
                                            //                     .start,
                                            //             children: [
                                            //               Text(
                                            //                   "If you approve this report as Invalid:",
                                            //                   style:
                                            //                       body2TextSemiBold),
                                            //               const Gap(5),
                                            //               const BulletText(
                                            //                   text:
                                            //                       "The event/ class will be unpublished immediately."),
                                            //               const Gap(5),
                                            //               const BulletText(
                                            //                   text:
                                            //                       "The business account will be suspended if reports warnings are ignored"),
                                            //               const Gap(15),
                                            //               Text(
                                            //                   "If you approve this report as Invalid:",
                                            //                   style:
                                            //                       body2TextSemiBold),
                                            //               const Gap(5),
                                            //               const BulletText(
                                            //                   text:
                                            //                       "The event/ class will remain live to users"),
                                            //             ],
                                            //           ),
                                            //         ),
                                            //       )
                                            //     : const Spacer(),
                                          ],
                                        ),
                                      ],
                                    )
                                  : const SizedBox())
                        ],
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void handleConfirmDecision() {
    if (isApproveClass.value == true) {
      adminBusinessController
          .approveAdminClassData(
              classID:
                  adminBusinessController.adminClassByIDModel.value.classId,
              reason: isApproveClass.value ? "" : _reasonController.value.text,
              type: isApproveClass.value ? "approve" : "reject")
          .then((value) {
        if (value) {
          adminBusinessController.getAdminClassList();
          locator<NavigationServices>().goBack();
        } else {
          // do nothing
        }
      });
    } else {
      if (_formKey.currentState!.validate()) {
        adminBusinessController
            .approveAdminClassData(
                classID:
                    adminBusinessController.adminClassByIDModel.value.classId,
                reason:
                    isApproveClass.value ? "" : _reasonController.value.text,
                type: isApproveClass.value ? "approve" : "reject")
            .then((value) {
          if (value) {
            adminBusinessController.getAdminClassList();
            locator<NavigationServices>().goBack();
          } else {
            // do nothing
          }
        });
      }
    }
  }

  void _showImageDialog(BuildContext context, String attachment) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: Stack(
            children: [
              SizedBox(
                width: Get.width / 2,
                height: 600,
                child: attachment.isNotEmpty
                    ? Image.network(
                        attachment,
                        fit: BoxFit.cover,
                        loadingBuilder: (BuildContext context, Widget child,
                            ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                              child: Text('No image available',
                                  style: bodyTextRegular));
                        },
                      )
                    : Center(
                        child:
                            Text('No image available', style: bodyTextRegular)),
              ),
              Positioned(
                top: 6,
                right: 10,
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      // shape: BoxShape.circle,
                      color: AppColors.kprimarycolor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.kwhite,
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
