import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';

class FilterBar extends StatelessWidget {
  final void Function(String) onSearch;
  final void Function(String?) onStatusChanged;
  final void Function(String?) onOnlineOfflineChanged;
  final void Function(String) onCategoryChanged;
  final dynamic Function(DateTimeRange) onDateRangeChanged;
  final void Function(String?) onCitiesChanged;
  final TextEditingController? searchController;
  final void Function() onClearFilter;
  final VoidCallback onExportCSV;
  final bool isTotal;
  final bool isAllStatus;
  final bool isAllCategory;
  final bool isAllCities;
  final bool isClassType;
  final String? totalText;
  final List<String> statusItems;
  final List<String> onlineOfflineItems;
  final List<String> categoryItems;
  final List<String> citiesItems;
  final String currentSearchQuery;
  final DateTimeRange? currentDateRange;
  final String currentCity;
  final String currentStatus;
  final String currentOnlineOffline;
  final String currentCategory;

  const FilterBar({
    super.key,
    required this.onSearch,
    required this.onStatusChanged,
    required this.onOnlineOfflineChanged,
    required this.onCategoryChanged,
    required this.onDateRangeChanged,
    required this.onExportCSV,
    required this.statusItems,
    required this.onlineOfflineItems,
    required this.categoryItems,
    required this.onClearFilter,
    this.totalText,
    this.isTotal = false,
    this.isAllStatus = false,
    this.isAllCategory = false,
    this.isAllCities = false,
    this.isClassType = false,
    required this.citiesItems,
    required this.onCitiesChanged,
    required this.currentSearchQuery,
    required this.currentDateRange,
    required this.currentCity,
    this.searchController,
    required this.currentStatus,
    required this.currentOnlineOffline,
    required this.currentCategory,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (isTotal && totalText != null)
          Flexible(
            flex: 2,
            child: Container(
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: AppColors.bordergrey),
              ),
              child: Center(
                child: Text(
                  totalText!,
                  style: GoogleFonts.rubik(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: AppColors.txtprimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        const SizedBox(width: 8),
        Flexible(
          flex: 3,
          child: CommonSearchField(
            onChanged: onSearch,
            controller: searchController,
            hintText: "Search",
            initialValue: currentSearchQuery,
            onClear: onClearFilter,
            decoration: BoxDecoration(
              color: AppColors.scaffoldColor,
              borderRadius: BorderRadius.circular(6),
            ),
            hintStyle: body2TextSemiBold.copyWith(color: AppColors.placeHolder),
            textStyle: body2TextSemiBold,
          ),
        ),
        const SizedBox(width: 8),
        if (isAllStatus)
          Flexible(
            flex: 2,
            child: CustomDropdownFormField(
              items: statusItems
                  .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(item, overflow: TextOverflow.ellipsis)))
                  .toList(),
              hintText: 'Status',
              onChanged: onStatusChanged,
              textStyle: bodyTextRegular,
              value: currentStatus.isEmpty ? null : currentStatus,
            ),
          ),
        if (isAllStatus) const SizedBox(width: 8),
        if (isAllCities)
          Flexible(
            flex: 2,
            child: CustomDropdownFormField(
              items: citiesItems
                  .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(item, overflow: TextOverflow.ellipsis)))
                  .toList(),
              hintText: 'Cities',
              onChanged: onCitiesChanged,
              value: currentCity.isEmpty ? null : currentCity,
              textStyle: bodyTextRegular,
            ),
          ),
        if (isAllCities) const SizedBox(width: 8),
        if (isClassType)
          Flexible(
            flex: 2,
            child: CustomDropdownFormField(
              items: onlineOfflineItems
                  .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(item, overflow: TextOverflow.ellipsis)))
                  .toList(),
              hintText: 'Type',
              onChanged: onOnlineOfflineChanged,
              value: currentOnlineOffline.isEmpty ? null : currentOnlineOffline,
              textStyle: bodyTextRegular,
            ),
          ),
        if (isClassType) const SizedBox(width: 8),
        if (isAllCategory)
          Flexible(
            flex: 2,
            child: CustomDropdownFormField(
              items: categoryItems
                  .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(item, overflow: TextOverflow.ellipsis)))
                  .toList(),
              hintText: 'All Category',
              onChanged: (value) => onCategoryChanged(value ?? ''),
              textStyle: bodyTextRegular,
              value: currentCategory.isEmpty ? null : currentCategory,
            ),
          ),
        if (isAllCategory) const SizedBox(width: 8),
        Flexible(
          flex: 3,
          child: DateRangeSelector(
            onDateRangeSelected: onDateRangeChanged,
            initialDateRange: currentDateRange,
          ),
        ),
        const SizedBox(width: 5),
        Flexible(
          flex: 2,
          child: PrimaryButton(text: "Export Excel", onTap: onExportCSV),
        ),
        const SizedBox(width: 8),
        Flexible(
          flex: 2,
          child: SecondaryButton(
            onTap: onClearFilter,
            text: "Clear Filter",
          ),
        ),
      ],
    );
  }
}

class DateRangeSelector extends StatefulWidget {
  final Function(DateTimeRange) onDateRangeSelected;
  final DateTimeRange? initialDateRange;
  const DateRangeSelector(
      {super.key, required this.onDateRangeSelected, this.initialDateRange});

  @override
  State<DateRangeSelector> createState() => _DateRangeSelectorState();
}

class _DateRangeSelectorState extends State<DateRangeSelector> {
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
  }

  @override
  void didUpdateWidget(DateRangeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialDateRange != oldWidget.initialDateRange) {
      setState(() {
        _selectedDateRange = widget.initialDateRange;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showCustomDateRangePicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: AppColors.scaffoldColor,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset("assets/icons/CalendarBlank.svg"),
            const SizedBox(width: 5),
            Expanded(
              child: Text(
                _selectedDateRange == null
                    ? "Start date"
                    : DateFormat('MMM d, y').format(_selectedDateRange!.start),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: body2TextSemiBold.copyWith(color: AppColors.placeHolder),
              ),
            ),
            SvgPicture.asset("assets/icons/ArrowsHorizontal.svg"),
            const SizedBox(width: 5),
            Expanded(
              child: Text(
                _selectedDateRange == null
                    ? 'End Date'
                    : DateFormat('MMM d, y').format(_selectedDateRange!.end),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: body2TextSemiBold.copyWith(color: AppColors.placeHolder),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCustomDateRangePicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: AppColors.kwhite,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: 400,
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            child: DateRangePickerDialog(
              firstDate: DateTime(2000),
              lastDate: DateTime(2101),
              initialDateRange: _selectedDateRange,
              saveText: 'Select',
              onDateRangeSelected: (DateTimeRange? newRange) {
                if (newRange != null && newRange != _selectedDateRange) {
                  setState(() {
                    _selectedDateRange = newRange;
                  });
                  widget.onDateRangeSelected(newRange);
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        );
      },
    );
  }
}

class DateRangePickerDialog extends StatefulWidget {
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTimeRange? initialDateRange;
  final String saveText;
  final Function(DateTimeRange?) onDateRangeSelected;

  const DateRangePickerDialog({
    super.key,
    required this.firstDate,
    required this.lastDate,
    this.initialDateRange,
    required this.saveText,
    required this.onDateRangeSelected,
  });

  @override
  State<DateRangePickerDialog> createState() => _DateRangePickerDialogState();
}

class _DateRangePickerDialogState extends State<DateRangePickerDialog> {
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CalendarDatePicker2(
          config: CalendarDatePicker2Config(
            calendarType: CalendarDatePicker2Type.range,
            selectedDayHighlightColor: Colors.blue,
          ),
          value: _selectedDateRange != null
              ? [_selectedDateRange!.start, _selectedDateRange!.end]
              : [],
          onValueChanged: (dates) {
            if (dates.length == 2) {
              setState(() {
                _selectedDateRange = DateTimeRange(
                  start: dates[0],
                  end: dates[1],
                );
              });
            }
          },
        ),
        ButtonBar(
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => widget.onDateRangeSelected(_selectedDateRange),
              child: Text(widget.saveText),
            ),
          ],
        ),
      ],
    );
  }
}

class CommonSearchField extends StatefulWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String> onChanged;
  final VoidCallback? onClear;
  final String? initialValue;
  final EdgeInsetsGeometry? contentPadding;
  final BoxDecoration? decoration;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;

  const CommonSearchField({
    super.key,
    this.controller,
    required this.hintText,
    required this.onChanged,
    this.onClear,
    this.initialValue,
    this.contentPadding,
    this.decoration,
    this.hintStyle,
    this.textStyle,
  });

  @override
  _CommonSearchFieldState createState() => _CommonSearchFieldState();
}

class _CommonSearchFieldState extends State<CommonSearchField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        widget.controller ?? TextEditingController(text: widget.initialValue);
  }

  @override
  void didUpdateWidget(CommonSearchField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      _controller = widget.controller ?? _controller;
    }
    if (widget.initialValue != oldWidget.initialValue &&
        widget.controller == null) {
      _controller.text = widget.initialValue ?? '';
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: widget.decoration ??
          BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
      child: TextField(
        controller: _controller,
        onChanged: widget.onChanged,
        style: widget.textStyle ?? const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: widget.hintStyle ?? const TextStyle(color: Colors.grey),
          prefixIcon: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Icon(
                CupertinoIcons.search,
                color: AppColors.kblack.withOpacity(0.5),
              )),
          suffixIcon: _controller.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    _controller.clear();
                    widget.onChanged('');
                    widget.onClear?.call();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: widget.contentPadding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }
}
