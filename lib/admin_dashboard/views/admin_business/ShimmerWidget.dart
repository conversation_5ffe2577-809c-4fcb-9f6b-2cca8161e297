import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerWidget extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final bool changeColor;
  const ShimmerWidget({
    super.key,
    required this.child,
    required this.isLoading,
    this.changeColor = true,
  });
  @override
  Widget build(BuildContext context) {
    return isLoading
        ? Shimmer.fromColors(
            baseColor: changeColor
                ? const Color.fromARGB(255, 247, 246, 246)
                : const Color.fromARGB(255, 238, 236, 236),
            highlightColor: changeColor
                ? const Color.fromARGB(255, 117, 114, 114)
                : const Color.fromARGB(255, 125, 122, 122),
            child: child,
          )
        : child;
  }
}
