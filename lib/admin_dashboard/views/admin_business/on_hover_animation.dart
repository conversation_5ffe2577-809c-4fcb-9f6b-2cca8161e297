import 'package:flutter/material.dart';

class OnHoverButton extends StatefulWidget {
  final Widget child;
 const OnHoverButton({super.key, required this.child});

  @override
  State<OnHoverButton> createState() => _OnHoverButtonState();
}

class _OnHoverButtonState extends State<OnHoverButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final hoveredTransform = Matrix4.identity()..scale(1.1);
    final transform = isHovered ? hoveredTransform : Matrix4.identity();

    return MouseRegion(
        onHover: (event) => onEntered(true),
        onExit: (event) => onEntered(false),
        child: AnimatedContainer(
            transform: transform,
            duration: const Duration(milliseconds: 50),
            child: widget.child));
  }

  void onEntered(bool isHovered) {
    setState(() {
      this.isHovered = isHovered;
    });
  }
}
