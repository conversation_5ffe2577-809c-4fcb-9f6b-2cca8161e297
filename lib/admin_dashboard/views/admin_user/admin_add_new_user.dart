import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';

class AdminAddNewUser extends StatefulWidget {
  const AdminAddNewUser({super.key});

  @override
  State<AdminAddNewUser> createState() => _AdminAddNewUserState();
}

class _AdminAddNewUserState extends State<AdminAddNewUser> {
  String selectedPosition = '', fullName = '', mobileNumber = '';
  final GlobalKey<FormState> addRoleForm = GlobalKey<FormState>();
  final AdminUserController adminUserController = Get.find<AdminUserController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      appBar: AppBar(
        shadowColor: AppColors.kwhite,
        backgroundColor: AppColors.kwhite,
        surfaceTintColor: AppColors.kwhite,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                height: 32,
                width: 32,
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, color: AppColors.scaffoldColor),
                child: const Center(
                  child: Icon(Icons.close),
                ),
              ),
            ),
            const Spacer(),
            Text("Add new user", style: titleTextSemiBold),
            const Spacer(),
          ],
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: addRoleForm,
              child: Column(
                children: [
                  CustomTextFormField(
                    onChanged: (String value) {
                      setState(() {
                        fullName = value;
                      });
                    },
                    hintText: "Full name",
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Enter full name.';
                      }
                      return null;
                    },
                  ),
                  const Gap(20),
                  CustomTextFormField(
                    onChanged: (String value) {
                      setState(() {
                        mobileNumber = value;
                      });
                    },
                    maxLines: 1,
                    keyboardType: TextInputType.phone,
                    maxLength: 10,
                    hintText: "Mobile number",
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Enter mobile number.';
                      }
                      return null;
                    },
                  ),
                  const Gap(20),
                  Obx(
                    () => CustomDropdownUI(
                      isValidatorReq: true,
                      title: "Select role",
                      items: adminUserController.adminPositionList.map((post) {
                        return DropdownMenuItem(
                          onTap: () {},
                          value: post.position,
                          child: Text(
                            post.position.toString(),
                            style: bodyTextMedium,
                          ),
                        );
                      }).toList(),
                      value: selectedPosition == '' ? null : selectedPosition,
                      onChanged: (value) {
                        setState(() {
                          selectedPosition = value.toString();
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: PrimaryButton(
                text: "Submit",
                onTap: () {
                  if (addRoleForm.currentState?.validate() ?? false) {
                    Map<String, dynamic> payload = {
                      "admin_id": int.parse(storage.read("USER_ID") ?? "0"),
                      "name": fullName,
                      "mobile": mobileNumber,
                      "is_newuser": 0,
                      "role_id": 2,
                      "position": selectedPosition.toLowerCase()
                    };
                    adminUserController.createAdminUser(payload).then((value) {
                      if (value) {
                        adminUserController.getAllAdminUsers();
                        Navigator.pop(context);
                      } else {
                        CustomSnackBar.showError(
                          "Error",
                            "Error while creating user, try after sometime.",
                        );
                      }
                    });
                  }
                }),
          )
        ],
      ),
    );
  }
}
