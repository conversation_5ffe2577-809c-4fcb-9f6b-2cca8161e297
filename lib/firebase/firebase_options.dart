import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
      apiKey: "AIzaSyBo9-kM0Rc0L0m6ThQiMLovdn1ooM0jPYw",
      appId: "1:272995004120:android:dd0c225d5d046ac01e31ad",
      messagingSenderId: "272995004120",
      projectId: "parenthing-app");

  static const FirebaseOptions ios = FirebaseOptions(
      apiKey: "AIzaSyCA1td61QRD4nALKI7ki3-naVLH14TSOi4",
      appId: "1:272995004120:ios:e7e414df2bc3df2e1e31ad",
      messagingSenderId: "272995004120",
      projectId: "parenthing-app");

   static const FirebaseOptions web = FirebaseOptions(
    apiKey: "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs",
  authDomain: "parenthing-app.firebaseapp.com",
  projectId: "parenthing-app",
  storageBucket: "parenthing-app.appspot.com",
  messagingSenderId: "272995004120",
  appId: "1:272995004120:web:d1f1c64c8ba5c5c61e31ad",
  measurementId: "G-4WP7XZZJZ3"
  );
}
