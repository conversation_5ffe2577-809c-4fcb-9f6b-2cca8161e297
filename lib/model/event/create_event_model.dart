import 'dart:convert';

import 'package:parenthing_dashboard/model/location_model.dart';

CreateEventModel createEventModelFromJson(String str) =>
    CreateEventModel.fromJson(json.decode(str));

String createEventModelToJson(CreateEventModel data) =>
    json.encode(data.toJson());

class CreateEventModel {
  String businessId;
  String title;
  String bannerUrl;
  String category;
  String ageGroup;
  String classType;
  int price;
  String description;
  int duration;
  String ctaUrl;
  String eventType;
  String latitude;
  String longitude;
  String profilePictureUrl;
  LocationDetails locationDetails;
  String city;
  String address;
  String ticketType;
  String ctaMobile;
  String multiplePricing;
  int publish;
  int minAge;
  int maxAge;
  String startDate;
  String endDate;
  String status;

  

  CreateEventModel({
    this.businessId = "",
    this.title = "",
    this.bannerUrl = "",
    this.category = "",
    this.ageGroup = "",
    this.classType = "",
    this.price = 0,
    this.description = "",
    this.duration = 0,
    this.ctaUrl = "",
    this.eventType = "",
    this.latitude = "",
    this.longitude = "",
    this.profilePictureUrl = "",
    required this.locationDetails,
    this.city = "",
    this.address = "",
    this.ticketType = "",
    this.ctaMobile = "",
    this.multiplePricing = "",
    this.publish = 0,
    this.minAge = 0,
    this.maxAge = 0,
    this.startDate = "",
    this.endDate = "",
    this.status = "",

  });

  factory CreateEventModel.fromJson(Map<String, dynamic> json) =>
      CreateEventModel(
        businessId: json["business_id"] ?? "",
        title: json["title"],
        bannerUrl: json["banner_url"],
        category: json["category"],
        ageGroup: json["age_group"] ?? "",
        classType: json["class_type"],
        price: json["price"],
        description: json["description"],
        duration: json["duration"],
        ctaUrl: json["cta_url"],
        eventType: json["event_type"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        profilePictureUrl: json["profile_picture_url"],
        locationDetails: LocationDetails.fromJson(json["location_details"]),
        city: json["city"],
        address: json["address"],
        ticketType: json["ticket_type"],
        ctaMobile: json["cta_mobile"],
        multiplePricing: json["multiple_pricing"],
        publish: json["publish"],
        minAge: json["min_age"],
        maxAge: json["max_age"],
        startDate: json["start_date"],
        status: json["status"],
        endDate: json["end_date"],
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "title": title,
        "banner_url": bannerUrl,
        "category": category,
        "age_group": ageGroup,
        "class_type": classType,
        "price": price,
        "description": description,
        "duration": duration,
        "cta_url": ctaUrl,
        "event_type": eventType,
        "latitude": latitude,
        "longitude": longitude,
        "profile_picture_url": profilePictureUrl,
        "location_details": locationDetails.toJson(),
        "city": city,
        "address": address,
        "ticket_type": ticketType,
        "cta_mobile": ctaMobile,
        "multiple_pricing": multiplePricing,
        "publish": publish,
        "min_age": minAge,
        "max_age": maxAge,
        "start_date": startDate,
        "end_date": endDate,
        "status": status,
      };
}
