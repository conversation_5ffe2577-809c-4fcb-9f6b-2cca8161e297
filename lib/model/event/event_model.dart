// To parse this JSON data, do
//
//     final eventModel = eventModelFromJson(jsonString);

import 'dart:convert';

import 'package:parenthing_dashboard/model/location_model.dart';

List<EventModel> eventModelFromJson(String str) =>
    List<EventModel>.from(json.decode(str).map((x) => EventModel.fromJson(x)));

String eventModelToJson(List<EventModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class EventModel {
  String businessId;
  int? eventId;
  String title;
  String bannerUrl;
  String category;
  String ageGroup;
  String classType;
  int price;
  String description;
  String eventDate;
  int duration;
  String ctaUrl;
  String eventType;
  String latitude;
  String longitude;
  String profilePictureUrl;
  LocationDetails locationDetails;
  String businessName;
  String city;
  String address;
  String ticketType;
  String ctaMobile;
  String multiplePricing;
  int publish;
  int minAge;
  int maxAge;
  String startDate;
  String endDate;
  String status;
  String reason;
  int? isApproved;
  int? requestID;

  EventModel({
    this.businessId = "",
    this.eventId,
    this.title = "",
    this.bannerUrl = "",
    this.category = "",
    this.ageGroup = "",
    this.classType = "",
    this.price = 0,
    this.description = "",
    this.eventDate = "",
    this.duration = 0,
    this.ctaUrl = "",
    this.eventType = "",
    this.latitude = "",
    this.longitude = "",
    this.profilePictureUrl = "",
    required this.locationDetails,
    this.businessName = "",
    this.city = "",
    this.address = "",
    this.ticketType = "",
    this.ctaMobile = "",
    this.multiplePricing = "",
    this.publish = 0,
    this.minAge = 0,
    this.maxAge = 0,
    this.startDate = "",
    this.endDate = "",
    this.status = "",
    this.reason = "",
    this.isApproved,
    this.requestID,
  });

  factory EventModel.fromJson(Map<String, dynamic> json) => EventModel(
        businessId: json["business_id"] ?? "",
        eventId: json["event_id"] ?? 0,
        title: json["title"] ?? "",
        bannerUrl: json["banner_url"] ?? "",
        category: json["category"] ?? "",
        ageGroup: json["age_group"] ?? "",
        classType: json["class_type"] ?? "",
        price: json["price"] ?? 0,
        description: json["description"] ?? "",
        eventDate: json["event_date"] ?? "",
        duration: json["duration"] ?? 0,
        ctaUrl: json["cta_url"] ?? "",
        eventType: json["event_type"] ?? "",
        latitude: json["latitude"] ?? "",
        longitude: json["longitude"] ?? "",
        profilePictureUrl: json["profile_picture_url"] ?? "",
        locationDetails: json["location_details"] == null
            ? LocationDetails()
            : LocationDetails.fromJson(json["location_details"]),
        businessName: json["business_name"] ?? "",
        city: json["city"] ?? "",
        address: json["address"] ?? "",
        ticketType: json["ticket_type"] ?? "",
        ctaMobile: json["cta_mobile"] ?? "",
        multiplePricing: json["multiple_pricing"] ?? "",
        publish: json["publish"] ?? 0,
        minAge: json["min_age"] ?? 0,
        maxAge: json["max_age"] ?? 0,
        startDate: json["start_date"] ?? "",
        endDate: json["end_date"] ?? "",
        status: json["status"] ?? "",
        reason: json["reason"] ?? "",
        isApproved: json["is_approved"] ?? 0,
        requestID: json["request_id"] ?? 0
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "event_id": eventId,
        "title": title,
        "banner_url": bannerUrl,
        "category": category,
        "age_group": ageGroup,
        "class_type": classType,
        "price": price,
        "description": description,
        "event_date": eventDate,
        "duration": duration,
        "cta_url": ctaUrl,
        "event_type": eventType,
        "latitude": latitude,
        "longitude": longitude,
        "profile_picture_url": profilePictureUrl,
        "location_details": locationDetails.toJson(),
        "business_name": businessName,
        "city": city,
        "address": address,
        "ticket_type": ticketType,
        "cta_mobile": ctaMobile,
        "multiple_pricing": multiplePricing,
        "publish": publish,
        "min_age": minAge,
        "max_age": maxAge,
        "start_date": startDate,
        "end_date": endDate,
        "status": status,
        "reason": reason,
        "is_approved": isApproved,
        "request_id": requestID
      };
}
