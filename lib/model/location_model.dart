class LocationDetails {
  String area;
  String city;
  String state;
  String address;
  String country;
  double latitude;
  int pinCode;
  double longitude;
  int locationId;
  String subLocality;

  LocationDetails({
    this.area = "",
    this.city = "",
    this.state = "",
    this.address = "",
    this.country = "",
    this.latitude = 0.0,
    this.pinCode = 0,
    this.longitude = 0.0,
    this.locationId = 0,
    this.subLocality = "",
  });

  factory LocationDetails.fromJson(Map<String, dynamic> json) =>
      LocationDetails(
        area: json["area"],
        city: json["city"],
        state: json["state"],
        address: json["address"],
        country: json["country"],
        latitude: json["latitude"]?.toDouble(),
        pinCode: json["pin_code"],
        longitude: json["longitude"]?.toDouble(),
        locationId: json["location_id"] ?? 0,
        subLocality: json["sub_locality"],
      );

  Map<String, dynamic> toJson() => {
        "area": area,
        "city": city,
        "state": state,
        "address": address,
        "country": country,
        "latitude": latitude,
        "pin_code": pinCode,
        "longitude": longitude,
        "location_id": locationId,
        "sub_locality": subLocality,
      };
}
