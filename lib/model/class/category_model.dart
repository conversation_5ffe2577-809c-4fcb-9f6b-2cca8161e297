// To parse this JSON data, do
//
//     final classCategoryModel = classCategoryModelFromJson(jsonString);

import 'dart:convert';

List<ClassCategoryModel> classCategoryModelFromJson(String str) =>
    List<ClassCategoryModel>.from(
        json.decode(str).map((x) => ClassCategoryModel.fromJson(x)));

String classCategoryModelToJson(List<ClassCategoryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ClassCategoryModel {
  int id;
  String name;
  String iconUrl;
  List<Subcategory> subcategories;

  ClassCategoryModel({
    this.id = 0,
    this.name = "",
    this.iconUrl = "",
    required this.subcategories,
  });

  factory ClassCategoryModel.fromJson(Map<String, dynamic> json) =>
      ClassCategoryModel(
        id: json["id"],
        name: json["name"],
        iconUrl: json["icons_url"],
        subcategories: List<Subcategory>.from(
            json["subcategories"].map((x) => Subcategory.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icons_url": iconUrl,
        "subcategories":
            List<dynamic>.from(subcategories.map((x) => x.toJson())),
      };
}

class Subcategory {
  String name;
  int sId;

  Subcategory({
    this.name = "",
    this.sId = 0,
  });

  factory Subcategory.fromJson(Map<String, dynamic> json) => Subcategory(
        name: json["name"],
        sId: json["s_id"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "s_id": sId,
      };
}
