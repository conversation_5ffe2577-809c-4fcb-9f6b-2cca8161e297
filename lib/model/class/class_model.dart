// To parse this JSON data, do
//
//     final classModel = classModelFromJson(jsonString);

import 'dart:convert';

import 'package:parenthing_dashboard/model/location_model.dart';

List<ClassModel> classModelFromJson(String str) =>
    List<ClassModel>.from(json.decode(str).map((x) => ClassModel.fromJson(x)));

String classModelToJson(List<ClassModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ClassModel {
  String businessId;
  int classId;
  String title;
  String bannerUrl;
  String category;
  String classType;
  int price;
  String description;
  String subCategory;
  String ctamobile;
  String sessionType;
  String latitude;
  String longitude;
  String profilePictureUrl;
  LocationDetails locationDetails;
  String businessName;
  String city;
  String address;
  String classFee;
  int publish;
  int minAge;
  int maxAge;
  String status;
  String reason;
  int isApproved;
  int? requestID;

  ClassModel(
      {this.businessId = "",
      this.classId = 0,
      this.title = "",
      this.bannerUrl = "",
      this.category = "",
      this.classType = "",
      this.price = 0,
      this.description = "",
      this.subCategory = "",
      this.ctamobile = "",
      this.sessionType = "",
      this.latitude = "",
      this.longitude = "",
      this.profilePictureUrl = "",
      required this.locationDetails,
      this.businessName = "",
      this.city = "",
      this.address = "",
      this.classFee = "",
      this.publish = 0,
      this.minAge = 0,
      this.maxAge = 0,
      this.status = "",
      this.reason = "",
      this.isApproved = 0,
      this.requestID});

  factory ClassModel.fromJson(Map<String, dynamic> json) => ClassModel(
      classId: json["class_id"] ?? 0,
      businessId: json["business_id"] ?? "",
      title: json["title"] ?? "",
      bannerUrl: json["banner_url"] ?? "",
      category: json["category"] ?? "",
      classType: json["class_type"] ?? "",
      price: json["price"] ?? 0,
      description: json["description"] ?? "",
      subCategory: json["sub_category"] ?? "",
      ctamobile: json["ctamobile"] ?? "",
      sessionType: json["session_type"] ?? "",
      latitude: json["latitude"] ?? "",
      longitude: json["longitude"] ?? "",
      profilePictureUrl: json["profile_picture_url"] ?? "",
      locationDetails: json["location"] == null
          ? LocationDetails()
          : LocationDetails.fromJson(json["location"]),
      businessName: json["business_name"] ?? "",
      city: json["city"] ?? "",
      address: json["address"] ?? "",
      classFee: json["class_fee"] ?? "",
      publish: json["publish"] ?? 0,
      minAge: json["min_age"] ?? 0,
      maxAge: json["max_age"] ?? 0,
      status: json["status"] ?? "",
      reason: json["reason"] ?? "",
      isApproved: json["is_approved"] ?? 0,
      requestID: json["request_id"] ?? 0);

  Map<String, dynamic> toJson() => {
        "class_id": classId,
        "business_id": businessId,
        "title": title,
        "banner_url": bannerUrl,
        "category": category,
        "class_type": classType,
        "price": price,
        "description": description,
        "sub_category": subCategory,
        "ctamobile": ctamobile,
        "session_type": sessionType,
        "latitude": latitude,
        "longitude": longitude,
        "profile_picture_url": profilePictureUrl,
        "location": locationDetails.toJson(),
        "business_name": businessName,
        "city": city,
        "address": address,
        "class_fee": classFee,
        "publish": publish,
        "min_age": minAge,
        "max_age": maxAge,
        "status": status,
        "reason": reason,
        "is_approved": isApproved,
        "request_id": requestID
      };
}
