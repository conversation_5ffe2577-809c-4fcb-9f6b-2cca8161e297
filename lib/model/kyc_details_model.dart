// To parse this JSON data, do
//
//     final kycDetailsModel = kycDetailsModelFromJson(jsonString);

import 'dart:convert';

KycDetailsModel kycDetailsModelFromJson(String str) =>
    KycDetailsModel.fromJson(json.decode(str));

String kycDetailsModelToJson(KycDetailsModel data) =>
    json.encode(data.toJson());

class KycDetailsModel {
  int requestId;
  int businessId;
  String businessName;
  String businessAddress;
  String documentType;
  String documentNumber;
  String documentUrl;
  String idType;
  String date;
  String reason;
  String status;
  String doneBy;

  KycDetailsModel({
    this.requestId = 0,
    this.businessId = 0,
    this.businessName = "",
    this.businessAddress = "",
    this.documentType = "",
    this.documentNumber = "",
    this.documentUrl = "",
    this.idType = "",
    this.date = "",
    this.reason = "",
    this.status = "",
    this.doneBy = "",
  });

  factory KycDetailsModel.fromJson(Map<String, dynamic> json) =>
      KycDetailsModel(
        requestId: json["request_id"],
        businessId: json["business_id"],
        businessName: json["business_name"],
        businessAddress: json["business_address"],
        documentType: json["document_type"],
        documentNumber: json["document_number"],
        documentUrl: json["document_url"],
        idType: json["id_type"],
        date: json["date"],
        reason: json["reason"],
        status: json["status"],
        doneBy: json["done_by"],
      );

  Map<String, dynamic> toJson() => {
        "request_id": requestId,
        "business_id": businessId,
        "business_name": businessName,
        "business_address": businessAddress,
        "document_type": documentType,
        "document_number": documentNumber,
        "document_url": documentUrl,
        "id_type": idType,
        "date": date,
        "reason": reason,
        "status": status,
        "done_by": doneBy,
      };
}
