// To parse this JSON data, do
//
//     final loginModel = loginModelFromJson(jsonString);

import 'dart:convert';

LoginModel loginModelFromJson(String str) =>
    LoginModel.fromJson(json.decode(str));

String loginModelToJson(LoginModel data) => json.encode(data.toJson());

class LoginModel {
  int id;
  String name;
  String profilePictureUrl;
  int isNewuser;
  int kycDone;
  int roleId;
  String token;
  int isprofileComplete;

  LoginModel({
    this.id = 0,
    this.name = "",
    this.profilePictureUrl = "",
    this.isNewuser = 0,
    this.kycDone = 0,
    this.roleId = 0,
    this.token = "",
    this.isprofileComplete = 0,
  });

  factory LoginModel.fromJson(Map<String, dynamic> json) => LoginModel(
        id: json["id"],
        name: json["name"],
        profilePictureUrl: json["profile_picture_url"],
        isNewuser: json["is_newuser"],
        kycDone: json["kyc_done"],
        roleId: json["role_id"],
        token: json["token"],
        isprofileComplete: json["isprofileComplete"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "profile_picture_url": profilePictureUrl,
        "is_newuser": isNewuser,
        "kyc_done": kycDone,
        "role_id": roleId,
        "token": token,
        "isprofileComplete": isprofileComplete,
      };
}
