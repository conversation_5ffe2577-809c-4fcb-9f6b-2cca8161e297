// To parse this JSON data, do
//
//     final businessGraphModel = businessGraphModelFromJson(jsonString);

import 'dart:convert';

BusinessGraphModel businessGraphModelFromJson(String str) =>
    BusinessGraphModel.fromJson(json.decode(str));

String businessGraphModelToJson(BusinessGraphModel data) =>
    json.encode(data.toJson());

class BusinessGraphModel {
  int businessId;
  String businessName;
  int draftEvents;
  int inreviewEvents;
  int approvedEvents;
  int totalEvents;
  int draftClasses;
  int inreviewClasses;
  int approvedClasses;
  int totalClasses;

  BusinessGraphModel({
    this.businessId = 0,
    this.businessName = '',
    this.draftEvents = 0,
    this.inreviewEvents = 0,
    this.approvedEvents = 0,
    this.totalEvents = 0,
    this.draftClasses = 0,
    this.inreviewClasses = 0,
    this.approvedClasses = 0,
    this.totalClasses = 0,
  });

  factory BusinessGraphModel.fromJson(Map<String, dynamic> json) =>
      BusinessGraphModel(
        businessId: json["business_id"],
        businessName: json["business_name"],
        draftEvents: json["draft_events"],
        inreviewEvents: json["inreview_events"],
        approvedEvents: json["approved_events"],
        totalEvents: json["total_events"],
        draftClasses: json["draft_classes"],
        inreviewClasses: json["inreview_classes"],
        approvedClasses: json["approved_classes"],
        totalClasses: json["total_classes"],
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "business_name": businessName,
        "draft_events": draftEvents,
        "inreview_events": inreviewEvents,
        "approved_events": approvedEvents,
        "total_events": totalEvents,
        "draft_classes": draftClasses,
        "inreview_classes": inreviewClasses,
        "approved_classes": approvedClasses,
        "total_classes": totalClasses,
      };
}
