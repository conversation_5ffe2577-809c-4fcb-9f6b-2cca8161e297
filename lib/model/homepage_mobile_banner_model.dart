// To parse this JSON data, do
//
//     final homePageMobileBanner = homePageMobileBannerFromJson(jsonString);

import 'dart:convert';

List<HomePageMobileBanner> homePageMobileBannerFromJson(String str) => List<HomePageMobileBanner>.from(json.decode(str).map((x) => HomePageMobileBanner.fromJson(x)));

String homePageMobileBannerToJson(List<HomePageMobileBanner> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class HomePageMobileBanner {
    int id;
    String imagesUrl;
    String type;
    String event;

    HomePageMobileBanner({
        this.id = 0,
        this.imagesUrl = "",
        this.type = "", 
        this.event = "",
    });

    factory HomePageMobileBanner.fromJson(Map<String, dynamic> json) => HomePageMobileBanner(
        id: json["id"] ?? 0,
        imagesUrl: json["images_url"] ?? "",
        type: json["type"] ?? "",
        event: json["event"] ?? "",
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "images_url": imagesUrl,
        "type": type,
        "event": event,
    };
}
