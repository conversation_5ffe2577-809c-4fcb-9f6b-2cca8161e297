// To parse this JSON data, do
//
//     final homePageImageModel = homePageImageModelFromJson(jsonString);

import 'dart:convert';

List<HomePageImageModel> homePageImageModelFromJson(String str) =>
    List<HomePageImageModel>.from(
        json.decode(str).map((x) => HomePageImageModel.fromJson(x)));

String homePageImageModelToJson(List<HomePageImageModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class HomePageImageModel {
  int id;
  String imagesUrl;
  String type;

  HomePageImageModel({this.id = 0, this.imagesUrl = "", this.type = ""});

  factory HomePageImageModel.fromJson(Map<String, dynamic> json) =>
      HomePageImageModel(
          id: json["id"], imagesUrl: json["images_url"], type: json["type"]);

  Map<String, dynamic> toJson() =>
      {"id": id, "image_url": imagesUrl, "type": type};
}
