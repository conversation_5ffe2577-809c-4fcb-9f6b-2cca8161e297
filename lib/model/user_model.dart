// To parse this JSON data, do
//
//     final userModel = userModelFromJson(jsonString);

import 'dart:convert';

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

String userModelToJson(UserModel data) => json.encode(data.toJson());

class UserModel {
  int businessId;
  String businessName;
  String email;
  String officeAddress;
  String pocName;
  String pocNumber;
  String businessPhone;
  String pocAltNumber;
  String latitude;
  String longitude;
  int locationId;
  int roleId;
  int isNewuser;
  String profilePictureUrl;
  String websiteLink;
  String instagramLink;
  String facebookLink;
  String youtubeLink;
  String businessType;
  String eventTypeDescription;
  String businessDescription;
  String additionalInfo;
  String howHeard;
  String sourceOfReferral;
  int kycDone;
  String kycStatus;
  String reason;
  List<Location> location;
  int isprofileComplete;

  UserModel(
      {this.businessId = 0,
      this.businessName = "",
      this.email = "",
      this.officeAddress = "",
      this.pocName = "",
      this.pocNumber = "",
      this.pocAltNumber = "",
      this.latitude = "",
      this.longitude = "",
      this.locationId = 0,
      this.businessPhone = "",
      this.roleId = 0,
      this.isNewuser = 0,
      this.profilePictureUrl = "",
      this.websiteLink = "",
      this.instagramLink = "",
      this.facebookLink = "",
      this.youtubeLink = "",
      this.businessType = "",
      this.eventTypeDescription = "",
      this.businessDescription = "-",
      this.additionalInfo = "",
      this.howHeard = "",
      this.sourceOfReferral = "",
      this.kycDone = 0,
      this.kycStatus = "",
      this.reason = "",
      this.location = const [],
      this.isprofileComplete = 0});

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        businessId: json["business_id"] ?? 0,
        businessName: json["business_name"] ?? "",
        email: json["email"] ?? "",
        officeAddress: json["office_address"] ?? "",
        pocName: json["poc_name"] ?? "",
        pocNumber: json["poc_number"] ?? "",
        pocAltNumber: json["poc_alt_number"] ?? "",
        businessPhone: json["business_phone"] ?? "",
        latitude: json["latitude"] ?? "",
        longitude: json["longitude"] ?? "",
        locationId: json["location_id"] ?? 0,
        roleId: json["role_id"] ?? 0,
        isNewuser: json["is_newuser"] ?? 0,
        profilePictureUrl: json["profile_picture_url"] ?? "",
        websiteLink: json["website_link"] ?? "",
        instagramLink: json["instagram_link"] ?? "",
        facebookLink: json["facebook_link"] ?? "",
        youtubeLink: json["youtube_link"] ?? "",
        businessType: json["business_type"] ?? "",
        eventTypeDescription: json["event_type_description"] ?? "",
        businessDescription: json["business_description"] ?? "-",
        additionalInfo: json["additional_info"] ?? "",
        howHeard: json["how_heard"] ?? "",
        sourceOfReferral: json["source_of_referral"] ?? "",
        kycDone: json["kyc_done"] ?? 0,
        kycStatus: json["kyc_status"] ?? "",
        reason: json["reason"] ?? "",
        location: json["locations"] == null
            ? []
            : List<Location>.from(
                json["locations"].map((x) => Location.fromJson(x))),
        isprofileComplete: json["isprofileComplete"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "business_id": businessId,
        "business_name": businessName,
        "email": email,
        "office_address": officeAddress,
        "business_phone": businessPhone,
        "poc_name": pocName,
        "poc_number": pocNumber,
        "poc_alt_number": pocAltNumber,
        "latitude": latitude,
        "longitude": longitude,
        "location_id": locationId,
        "role_id": roleId,
        "is_newuser": isNewuser,
        "profile_picture_url": profilePictureUrl,
        "website_link": websiteLink,
        "instagram_link": instagramLink,
        "facebook_link": facebookLink,
        "youtube_link": youtubeLink,
        "business_type": businessType,
        "event_type_description": eventTypeDescription,
        "business_description": businessDescription,
        "additional_info": additionalInfo,
        "how_heard": howHeard,
        "source_of_referral": sourceOfReferral,
        "kyc_done": kycDone,
        "kyc_status": kycStatus,
        "reason": reason,
        "locations": List<dynamic>.from(location.map((x) => x.toJson())),
        "isprofileComplete": isprofileComplete
      };
}

class Location {
  int locationId;
  String latitude;
  String longitude;
  String address;
  String area;
  int pinCode;
  String city;
  String country;
  String state;
  String createdAt;
  String updatedAt;
  String subLocality;
  int businessId;
  int parentId;
  String title;

  Location({
    this.locationId = 0,
    this.latitude = "",
    this.longitude = "",
    this.address = "",
    this.area = "",
    this.pinCode = 0,
    this.city = "",
    this.country = "",
    this.state = "",
    this.createdAt = "",
    this.updatedAt = "",
    this.subLocality = "",
    this.businessId = 0,
    this.parentId = 0,
    this.title = "",
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        locationId: json["location_id"] ?? 0,
        latitude: json["latitude"] ?? "",
        longitude: json["longitude"] ?? "",
        address: json["address"] ?? "",
        area: json["area"] ?? "",
        pinCode: json["pin_code"] ?? 0,
        city: json["city"] ?? "",
        country: json["country"] ?? "",
        state: json["state"] ?? "",
        createdAt: json["created_at"] ?? "",
        updatedAt: json["updated_at"] ?? "",
        subLocality: json["sub_locality"] ?? "",
        businessId: json["business_id"] ?? 0,
        parentId: json["parent_id"] ?? 0,
        title: json["title"] ?? "Home",
      );

  Map<String, dynamic> toJson() => {
        "location_id": locationId,
        "latitude": latitude,
        "longitude": longitude,
        "address": address,
        "area": area,
        "pin_code": pinCode,
        "city": city,
        "country": country,
        "state": state,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "sub_locality": subLocality,
        "business_id": businessId,
        "parent_id": parentId,
        "title": title,
      };
}
