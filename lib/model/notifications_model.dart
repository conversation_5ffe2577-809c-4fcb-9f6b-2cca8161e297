// To parse this JSON data, do
//
//     final businessNotificationsModel = businessNotificationsModelFromJson(jsonString);

import 'dart:convert';

BusinessNotificationListModel businessNotificationListModelFromJson(
        String str) =>
    BusinessNotificationListModel.fromJson(json.decode(str));

String businessNotificationListModelToJson(
        BusinessNotificationListModel data) =>
    json.encode(data.toJson());

class BusinessNotificationListModel {
  int isNew;
  List<BusinessNotificationsModel> notifications;

  BusinessNotificationListModel({
    this.isNew = 0,
    this.notifications = const [],
  });

  factory BusinessNotificationListModel.fromJson(Map<String, dynamic> json) =>
      BusinessNotificationListModel(
        isNew: json["is_new"] ?? 0,
        notifications: json["notifications"] == null
            ? []
            : List<BusinessNotificationsModel>.from(json["notifications"]
                .map((x) => BusinessNotificationsModel.from<PERSON>son(x))),
      );

  Map<String, dynamic> toJson() => {
        "is_new": isNew,
        "notifications":
            List<dynamic>.from(notifications.map((x) => x.toJson())),
      };
}

class BusinessNotificationsModel {
  int id;
  int userId;
  String title;
  String msg;
  String notificationType;
  int isClose;
  int isRead;
  Meta? meta;
  String createdAt;

  BusinessNotificationsModel(
      {this.id = 0,
      this.userId = 0,
      this.title = '',
      this.msg = '',
      this.notificationType = '',
      this.meta,
      this.isRead = 0,
      this.isClose = 0,
      this.createdAt = ''});

  factory BusinessNotificationsModel.fromJson(Map<String, dynamic> json) =>
      BusinessNotificationsModel(
          id: json["id"],
          userId: json["user_id"],
          title: json["title"],
          msg: json["msg"],
          notificationType: json["notification_type"],
          isClose: json["is_close"],
          isRead: json["is_read"] ?? 0,
          meta: json["meta"] != null ? Meta.fromJson(json["meta"]) : null,
          createdAt: json["created_at"]);

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "title": title,
        "msg": msg,
        "notification_type": notificationType,
        "is_read": isRead,
        "is_close": isClose,
        "meta": meta?.toJson(),
        "created_at": createdAt
      };
}

class Meta {
  int? eventId;
  int? classId;

  Meta({
    this.eventId,
    this.classId,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => Meta(
        eventId: json["event_id"],
        classId: json["class_id"],
      );

  Map<String, dynamic> toJson() => {
        "event_id": eventId,
        "class_id": classId,
      };
}
