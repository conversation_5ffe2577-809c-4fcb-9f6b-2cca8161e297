import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import "package:http/browser_client.dart" as browserhttp;

class ApiController extends GetxController {
  static var client = browserhttp.BrowserClient()..withCredentials = false;

  Future apiHelperFn({
    required String apiUrl,
    required var payload,
  }) async {
    Map<String, String> header = {};
    final storage = GetStorage();
    String token = storage.read('AUTH_TOKEN') ?? "";

    if (apiUrl.contains('generateotp') || apiUrl.contains('verifyOtp')) {
      header = {'Content-Type': 'application/json'};
    }
    // else if (apiUrl.contains('get_places')) {
    //   header = {
    //     'Content-Type': 'application/json',
    //     "Access-Control-Allow-Origin": "*",
    //     "Access-Control-Allow-Credentials": "true",
    //   };
    // }
    else {
      header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token'
      };
    }
    try {
      final response = await client.post(
        Uri.parse(apiUrl),
        body: payload == {} ? {} : json.encode(payload),
        headers: header,
      );
      if (kDebugMode) {
        print("$apiUrl ::=> header ${json.encode(header)}");
        print("$apiUrl ::=> payload ${json.encode(payload)}");
        print("$apiUrl ::=> statusCode ${response.statusCode}");
        print("$apiUrl ::=> body ${response.body}");
      }
      if (response.statusCode == 200) {
        return response.body;
      } else {
         if (kDebugMode) {
          print("API Error - Status: ${response.statusCode}, Body: ${response.body}");
        }
        return response.body;
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print("error in apiHelperFn ::=> $apiUrl \n$e \n$stackTrace");
      }
    }
  }

  Future<dynamic> getApi(String url) async {
    if (kDebugMode) {
      print(" url: $url");
    }
    Map<String, String> header = {};
    final storage = GetStorage();
    String token = storage.read('AUTH_TOKEN') ?? "";

    if (url.contains('generateotp') || url.contains('verifyotp')) {
      header = {'Content-Type': 'application/json'};
    } else {
      header = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token'
      };
    }
    dynamic responseJson;
    try {
      final response = await client
          .get(Uri.parse(url), headers: header)
          .timeout(const Duration(seconds: 10));
      responseJson = jsonDecode(response.body);
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('e:=>$e, $stackTrace');
      }
    }
    return responseJson;
  }
}
