import 'dart:developer';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final EventController eventController = Get.find<EventController>();
  final BusinessController businessController = Get.find<BusinessController>();
  bool isOfflineSelected = false;
  bool isOnlineSelected = false;
  bool isOfflineOnlineSelected = false;
  bool isOthersSelected = false;
  String userID = "0";
  String? imageUrl;
  final UserController userController = Get.find();

  @override
  void initState() {
    businessController.businessProfileDetails().then((value) {
      if (value == true) {
        setBusinessDetailsValue();
      }
    });

    if (kDebugMode) {
      print("user id:$userID");
    }
    super.initState();
  }

  void setBusinessDetailsValue() {
    if (businessController.userModel.value.businessType ==
        "Conduct events for kids") {
      setState(() {
        isOfflineSelected = true;
        isOnlineSelected = false;
        isOfflineOnlineSelected = false;
        isOthersSelected = false;
      });
    } else if (businessController.userModel.value.businessType ==
        "Classes for kids") {
      setState(() {
        isOfflineSelected = false;
        isOnlineSelected = true;
        isOfflineOnlineSelected = false;
        isOthersSelected = false;
      });
    } else if (businessController.userModel.value.businessType == "both") {
      setState(() {
        isOfflineSelected = false;
        isOnlineSelected = false;
        isOfflineOnlineSelected = true;
        isOthersSelected = false;
      });
    } else if (businessController.userModel.value.businessType == "Others") {
      setState(() {
        isOfflineSelected = false;
        isOnlineSelected = false;
        isOfflineOnlineSelected = false;
        isOthersSelected = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (kDebugMode) {
      log("user id 2:$userID");
    }
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          child: Container(
            margin: EdgeInsets.only(
                right: screenWidth <= 820 ? 12 : Get.width * .1,
                left: 12,
                top: 12,
                bottom: 30),
            width: Get.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: AppColors.kgrey,
              ),
            ),
            child: Form(
              key: _formKey,
              child: Obx(
                () => businessController.isProfileDetailsLoading.value
                    ? const Center(
                        child: CircularProgressIndicator(),
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Bounceable(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return CustomDialog(
                                          onConfirmTxt: "Yes, leave",
                                          onCancelText: "No",
                                          title: 'Leave this page?',
                                          content:
                                              'Are you sure you want to leave this page? All field details will be discarded',
                                          image:
                                              "assets/icons/WarningCircle.svg",
                                          onConfirm: () {
                                            Navigator.of(context).pop();
                                            locator<NavigationServices>()
                                                .goBack();
                                          },
                                          onCancel: () {
                                            Navigator.of(context).pop();
                                          },
                                        );
                                      },
                                    );
                                  },
                                  child: SvgPicture.asset(
                                    'assets/icons/arrow-left.svg',
                                  ),
                                ),
                                const Gap(30),
                                Text("Edit profile", style: title3TextSemiBold),

                                // Padding(
                                //   padding: const EdgeInsets.all(15.0),
                                //   child: PrimaryButton(
                                //     text: 'Save changes',
                                //     onTap: () {
                                //       onSaveChangeButton();
                                //     },
                                //   ),
                                // )
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Column(
                              crossAxisAlignment: screenWidth <= 820
                                  ? CrossAxisAlignment.center
                                  : CrossAxisAlignment.start,
                              children: [
                                Text("About Business", style: title2TextMedium),
                                kMinHeight,
                                screenWidth <= 820
                                    ? Column(
                                        children: [
                                          CircleAvatar(
                                            minRadius: 50,
                                            maxRadius: 50,
                                            backgroundImage: NetworkImage(
                                              businessController.userModel.value
                                                  .profilePictureUrl,
                                            ),
                                          ),
                                          const Gap(30),
                                          buildEditUploadProfile()
                                        ],
                                      )
                                    : Row(
                                        children: [
                                          CircleAvatar(
                                            minRadius: 50,
                                            maxRadius: 50,
                                            backgroundImage: NetworkImage(
                                              businessController.userModel.value
                                                  .profilePictureUrl,
                                            ),
                                          ),
                                          const Gap(30),
                                          buildEditUploadProfile()
                                        ],
                                      ),
                                kMinHeight,
                                screenWidth <= 820
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Business name (As registered)*',
                                            style: body2TextRegular,
                                          ),
                                          kSmHeight,
                                          CustomTextFormField(
                                            initialValue: businessController
                                                .userModel.value.businessName,
                                            onChanged: (val) {
                                              businessController.userModel.value
                                                  .businessName = val;
                                            },
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return "Enter business name";
                                              }
                                              return null;
                                            },
                                            maxLength: 55,
                                          ),
                                          const Gap(15),
                                          Text(
                                            'Email ID (Optional)',
                                            style: body2TextRegular,
                                          ),
                                          kSmHeight,
                                          CustomTextFormField(
                                            initialValue: businessController
                                                .userModel.value.email,
                                            onChanged: (val) {
                                              businessController
                                                  .userModel.value.email = val;
                                            },
                                            hintText: "Enter here",
                                            //                                          validator: (value) {
                                            //   if (value == null || value.isEmpty) {
                                            //     return 'Email is required';
                                            //   }
                                            //   if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                                            //     return 'Please enter a valid email address';
                                            //   }
                                            //   return null;
                                            // },
                                          ),
                                        ],
                                      )
                                    : Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Business name (As registered)*',
                                                  style: body2TextRegular,
                                                ),
                                                kSmHeight,
                                                CustomTextFormField(
                                                  initialValue:
                                                      businessController
                                                          .userModel
                                                          .value
                                                          .businessName,
                                                  onChanged: (val) {
                                                    businessController
                                                        .userModel
                                                        .value
                                                        .businessName = val;
                                                  },
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return "Enter business name";
                                                    }
                                                    return null;
                                                  },
                                                  maxLength: 55,
                                                  maxLines: 5,
                                                ),
                                              ],
                                            ),
                                          ),
                                          mdWidth,
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Email ID (Optional)',
                                                  style: body2TextRegular,
                                                ),
                                                kSmHeight,
                                                CustomTextFormField(
                                                  initialValue:
                                                      businessController
                                                          .userModel
                                                          .value
                                                          .email,
                                                  onChanged: (val) {
                                                    businessController.userModel
                                                        .value.email = val;
                                                  },

                                                  //                                          validator: (value) {
                                                  //   if (value == null || value.isEmpty) {
                                                  //     return 'Email is required';
                                                  //   }
                                                  //   if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                                                  //     return 'Please enter a valid email address';
                                                  //   }
                                                  //   return null;
                                                  // },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                kMinHeight,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Office address*',
                                      style: body2TextRegular,
                                    ),
                                    kSmHeight,
                                    CustomTextFormField(
                                      maxLines: 6,
                                      onChanged: (val) {
                                        businessController.userModel.value
                                            .officeAddress = val;
                                      },
                                      hintText:
                                          "Building, Locality, street, city, pincode",
                                      initialValue: businessController
                                          .userModel.value.officeAddress,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return "Enter valid addres";
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),
                                kMinHeight,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Business phone number',
                                      style: body2TextRegular,
                                    ),
                                    kSmHeight,
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: SizedBox(
                                        width: Get.width * .35,
                                        child: CustomTextFormField(
                                          initialValue: businessController
                                              .userModel.value.businessPhone,
                                          onChanged: (val) {
                                            businessController.userModel.value
                                                .businessPhone = val;
                                          },
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          maxLength: 10,
                                          hintText: "e.g +91 1234567890",
                                          validator: (value) {
                                            if (!RegExp(r'^\d{10}$')
                                                .hasMatch(value!)) {
                                              return "Enter valid phone number";
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Point of contact",
                                    style: title2TextMedium),
                                const Gap(5),
                                Text(
                                  "The person we should reach out to get in touch with your business",
                                  style: body2TextRegular.copyWith(
                                      color: AppColors.txtsecondary),
                                ),
                                const Gap(25),
                                screenWidth <= 820
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text('Person name*',
                                              style: body2TextRegular),
                                          kSmHeight,
                                          CustomTextFormField(
                                            maxLines: 2,
                                            onChanged: (val) {
                                              businessController.userModel.value
                                                  .pocName = val;
                                            },
                                            initialValue: businessController
                                                .userModel.value.pocName,
                                            hintText: "e.g Ravi Nehra",
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Please enter Name';
                                              }
                                              return null;
                                            },
                                          ),
                                          const Gap(15),
                                          Text(
                                            'Phone number of Poc*',
                                            style: body2TextRegular,
                                          ),
                                          kSmHeight,
                                          CustomTextFormField(
                                            hintText: "e.g +91 1234567890",
                                            onChanged: (val) {
                                              businessController.userModel.value
                                                  .pocNumber = val;
                                            },
                                            inputFormatters: [
                                              FilteringTextInputFormatter
                                                  .digitsOnly
                                            ],
                                            maxLength: 10,
                                            initialValue: businessController
                                                .userModel.value.pocNumber,
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return 'Please enter Poc phone number';
                                              }
                                              if (!RegExp(r'^\d{10}$')
                                                  .hasMatch(value)) {
                                                return 'Please enter a valid 10-digit phone number';
                                              }
                                              return null;
                                            },
                                          ),
                                        ],
                                      )
                                    : Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Person name*',
                                                  style: body2TextRegular,
                                                ),
                                                kSmHeight,
                                                CustomTextFormField(
                                                  maxLines: 2,
                                                  onChanged: (val) {
                                                    businessController.userModel
                                                        .value.pocName = val;
                                                  },
                                                  initialValue:
                                                      businessController
                                                          .userModel
                                                          .value
                                                          .pocName,
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return 'Please enter Name';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                          mdWidth,
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Phone number of Poc*',
                                                  style: body2TextRegular,
                                                ),
                                                kSmHeight,
                                                CustomTextFormField(
                                                  onChanged: (val) {
                                                    businessController.userModel
                                                        .value.pocNumber = val;
                                                  },
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  maxLength: 10,
                                                  initialValue:
                                                      businessController
                                                          .userModel
                                                          .value
                                                          .pocNumber,
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return 'Please enter Poc phone number';
                                                    }
                                                    if (!RegExp(r'^\d{10}$')
                                                        .hasMatch(value)) {
                                                      return 'Please enter a valid 10-digit phone number';
                                                    }
                                                    return null;
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                kMinHeight,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Alternate phone number of Poc',
                                      style: body2TextRegular,
                                    ),
                                    kSmHeight,
                                    SizedBox(
                                      width: Get.width * .35,
                                      child: CustomTextFormField(
                                        onChanged: (val) {
                                          businessController.userModel.value
                                              .pocAltNumber = val;
                                        },
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly
                                        ],
                                        maxLength: 10,
                                        initialValue: businessController
                                            .userModel.value.pocAltNumber,
                                        // validator: (value) {
                                        //   if (!RegExp(r'^\d{10}$').hasMatch(value!)) {
                                        //     return 'Please enter a valid 10-digit phone number';
                                        //   }
                                        //   return null;
                                        // },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Business details",
                                    style: title2TextMedium),
                                kMinHeight,
                                Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text('What does your business do?*',
                                              style: body2TextRegular),
                                          kSmHeight,
                                          screenWidth <= 820
                                              ? Row(
                                                  children: [
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                true;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'conduct events for kids';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius
                                                                  .circular(8),
                                                              right:
                                                                  Radius.zero,
                                                            ),
                                                            color: isOfflineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOfflineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Events",
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              maxLines: 2,
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOfflineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOfflineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                true;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'classes for kids';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isOnlineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOnlineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Classes",
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                              maxLines: 2,
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOnlineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOnlineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                true;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'both';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isOfflineOnlineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOfflineOnlineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Both",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOfflineOnlineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOfflineOnlineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                true;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'Others';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius.zero,
                                                              right: Radius
                                                                  .circular(8),
                                                            ),
                                                            color: isOthersSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOthersSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Others",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOthersSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOthersSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              : Row(
                                                  children: [
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                true;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'Conduct events for kids';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius
                                                                  .circular(8),
                                                              right:
                                                                  Radius.zero,
                                                            ),
                                                            color: isOfflineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOfflineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Conduct events for kids",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOfflineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOfflineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                true;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'Classes for kids';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isOnlineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOnlineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Classes for kids",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOnlineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOnlineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                true;
                                                            isOthersSelected =
                                                                false;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'both';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            color: isOfflineOnlineSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOfflineOnlineSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Both",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOfflineOnlineSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOfflineOnlineSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isOfflineSelected =
                                                                false;
                                                            isOnlineSelected =
                                                                false;
                                                            isOfflineOnlineSelected =
                                                                false;
                                                            isOthersSelected =
                                                                true;
                                                            businessController
                                                                    .userModel
                                                                    .value
                                                                    .businessType =
                                                                'Others';
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 40,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius.zero,
                                                              right: Radius
                                                                  .circular(8),
                                                            ),
                                                            color: isOthersSelected
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .backcolor,
                                                            border: Border.all(
                                                              color: isOthersSelected
                                                                  ? AppColors
                                                                      .kblack
                                                                  : AppColors
                                                                      .lgrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Others",
                                                              style:
                                                                  body2TextSemiBold
                                                                      .copyWith(
                                                                color: isOthersSelected
                                                                    ? AppColors
                                                                        .txtprimary
                                                                    : AppColors
                                                                        .txtsecondary,
                                                                fontWeight: isOthersSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .w500,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                          kMinHeight,
                                          // Column(
                                          //   crossAxisAlignment:
                                          //       CrossAxisAlignment.start,
                                          //   children: [
                                          //     Text(
                                          //         'Tell us more about your business (250 max characters)',
                                          //         style: body2TextRegular),
                                          //     kSmHeight,
                                          //     CustomTextFormField(
                                          //       maxLength: 250,
                                          //       maxLines: 30,
                                          //       onChanged: (val) {
                                          //         businessController
                                          //                 .userModel
                                          //                 .value
                                          //                 .businessDescription =
                                          //             val;
                                          //       },
                                          //       initialValue: businessController
                                          //           .userModel
                                          //           .value
                                          //           .businessDescription,
                                          //       hintText:
                                          //           "e.g. Workshops, seminars",
                                          //       // validator: (value) {
                                          //       //   if (value == null || value.isEmpty) {
                                          //       //     return 'Please enter your office address';
                                          //       //   }
                                          //       //   return null;
                                          //       // },
                                          //     ),
                                          //   ],
                                          // ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                kMinHeight,
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          screenWidth <= 820
                              ? Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: MobileWebsiteLinks(
                                    businessVM: businessController,
                                  ),
                                )
                              : Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: WebsiteLinks(
                                      businessVM: businessController),
                                ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Row(
                              children: [
                                const Spacer(),
                                PrimaryButton(
                                  text: 'Save changes',
                                  onTap: () {
                                    onSaveChangeButton();
                                  },
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildEditUploadProfile() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
            "Upload business logo or your photograph here.\nJPEG file format supported in 1:1 ratio, up to 1 MB"),
        const Gap(12),
        SecondaryButton(
          isImage: true,
          imagePath: "assets/icons/PencilSimple.svg",
          width: 100,
          text: "Edit",
          onTap: () async {
            log("Upload Image button pressed");
            FilePickerResult? result = await FilePicker.platform.pickFiles();

            if (result != null) {
              PlatformFile pickedFile = result.files.first;
              Uint8List? fileBytes = result.files.first.bytes;
              log("File selected: ${pickedFile.name}");
              await _uploadFileHelper(pickedFile, null, fileBytes!);
            } else {
              log('No file selected.');
            }
          },
        ),
      ],
    );
  }

  void onSaveChangeButton() async {
    if (_formKey.currentState?.validate() ?? false) {
      bool isSuccess = await businessController.businessEditProfileDetails();
      if (isSuccess) {
        locator<NavigationServices>().goBack();

        // Navigator.pop(Get.context!);
              CustomSnackBar.showInfo(
          "Success",
          "Your profile has been updated.",
         
        );

        // showDialog(
        //   context: Get.context!,
        //   builder: (context) {
        //     return SuccesPopup(
        //       imagePath: "",
        //       onTap: () {
        //         locator<NavigationServices>().goBack();
        //         Navigator.of(context).pop();
        //       },
        //       onTaptitle: "Okay",
        //       subtitle: "Your profile has been updated.",
        //       title: "Success",
        //     );
        //   },
        // );
      } else {
           CustomSnackBar.showError(
          "Failed",
          "Unable to update profile. Please try again.",
        );
      }
    }
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String contentType = 'image/jpeg';
    String filePath = file?.path ?? '';
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(fileExtension)) {
           CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
     
      );
      return;
    }

    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "profile");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        imageUrl = newImageUrl;
        businessController.userModel.value.profilePictureUrl = newImageUrl;
      });
      log("_imageUrl set to: $newImageUrl");
         CustomSnackBar.showInfo(
        "Succes",
        "Profile uploaded successfully",
      );
    } else {
         CustomSnackBar.showError(
        "Failed",
        "Unable to update the profile picture... try again",
       
      );
    }
  }
}

class WebsiteLinks extends StatelessWidget {
  const WebsiteLinks({
    super.key,
    required this.businessVM,
  });

  final BusinessController businessVM;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Website & social media links",
          style:
              rubikStyle.copyWith(fontSize: 20.0, fontWeight: FontWeight.w800),
        ),
        kMinHeight,
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Website link',
                    style: rubikStyle.copyWith(fontSize: 15.0),
                  ),
                  kSmHeight,
                  CustomTextFormField(
                      maxLines: 4,
                      onChanged: (val) {
                        businessVM.userModel.value.websiteLink = val;
                      },
                      initialValue: businessVM.userModel.value.websiteLink,
                      validator: (value) {
                        if (value!.isNotEmpty) {
                          bool isValidURL = RegExp(
                                  r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                              .hasMatch(value);
                          if (!isValidURL) {
                            return 'Please enter a valid website URL';
                          }
                        }
                        return null;
                      }),
                ],
              ),
            ),
            mdWidth,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Instagram profile',
                    style: rubikStyle.copyWith(fontSize: 15.0),
                  ),
                  kSmHeight,
                  CustomTextFormField(
                    maxLines: 4,
                    validator: (value) {
                      if (value!.isNotEmpty) {
                        bool isValidURL = RegExp(
                                r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                            .hasMatch(value);
                        if (!isValidURL) {
                          return 'Please enter a valid URL';
                        }
                      }
                      return null;
                    },
                    onChanged: (val) {
                      businessVM.userModel.value.instagramLink = val;
                    },
                    initialValue: businessVM.userModel.value.instagramLink,
                  ),
                ],
              ),
            ),
          ],
        ),
        kMinHeight,
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Facebook page',
                    style: rubikStyle.copyWith(fontSize: 15.0),
                  ),
                  kSmHeight,
                  CustomTextFormField(
                    maxLines: 4,
                    validator: (value) {
                      if (value!.isNotEmpty) {
                        bool isValidURL = RegExp(
                                r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                            .hasMatch(value);
                        if (!isValidURL) {
                          return 'Please enter a valid URL';
                        }
                      }
                      return null;
                    },
                    onChanged: (val) {
                      businessVM.userModel.value.facebookLink = val;
                    },
                    initialValue: businessVM.userModel.value.facebookLink,
                  ),
                ],
              ),
            ),
            mdWidth,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Youtube channel',
                    style: rubikStyle.copyWith(fontSize: 15.0),
                  ),
                  kSmHeight,
                  CustomTextFormField(
                    maxLines: 4,
                    validator: (value) {
                      if (value!.isNotEmpty) {
                        final trimmedValue = value.trim();
                        final isValidURL = RegExp(
                          r'^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/[\w\-@]+$',
                        ).hasMatch(trimmedValue);
                        final isValidHandle =
                            RegExp(r'^@?[\w\-]+$').hasMatch(trimmedValue);
                        if (!isValidURL && !isValidHandle) {
                          return 'Enter a valid YouTube URL or channel name';
                        }
                      }
                      return null;
                    },
                    onChanged: (val) {
                      businessVM.userModel.value.youtubeLink = val;
                    },
                    initialValue: businessVM.userModel.value.youtubeLink,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class MobileWebsiteLinks extends StatelessWidget {
  const MobileWebsiteLinks({
    super.key,
    required this.businessVM,
  });

  final BusinessController businessVM;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Website & social media links",
          style: title2TextMedium,
        ),
        kMinHeight,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Website link',
              style: body2TextRegular,
            ),
            kSmHeight,
            CustomTextFormField(
              maxLines: 3,
              onChanged: (val) {
                businessVM.userModel.value.websiteLink = val;
              },
              hintText: "https://",
              initialValue: businessVM.userModel.value.websiteLink,
              validator: (value) {
                if (value!.isNotEmpty) {
                  bool isValidURL = RegExp(
                          r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                      .hasMatch(value);
                  if (!isValidURL) {
                    return 'Please enter a valid website URL';
                  }
                }
                return null;
              },
            ),
            kMinHeight,
            Text(
              'Instagram profile',
              style: body2TextRegular,
            ),
            kSmHeight,
            CustomTextFormField(
              maxLines: 3,
              hintText: "https://",
              validator: (value) {
                if (value!.isNotEmpty) {
                  bool isValidURL = RegExp(
                          r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                      .hasMatch(value);
                  if (!isValidURL) {
                    return 'Please enter a valid URL';
                  }
                }
                return null;
              },
              onChanged: (val) {
                businessVM.userModel.value.instagramLink = val;
              },
              initialValue: businessVM.userModel.value.instagramLink,
            ),
            kMinHeight,
            Text(
              'Facebook page',
              style: body2TextRegular,
            ),
            kSmHeight,
            CustomTextFormField(
              maxLines: 3,
              hintText: "https://",
              validator: (value) {
                if (value!.isNotEmpty) {
                  bool isValidURL = RegExp(
                          r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                      .hasMatch(value);
                  if (!isValidURL) {
                    return 'Please enter a valid URL';
                  }
                }
                return null;
              },
              onChanged: (val) {
                businessVM.userModel.value.facebookLink = val;
              },
              initialValue: businessVM.userModel.value.facebookLink,
            ),
            kMinHeight,
            Text(
              'Youtube channel',
              style: body2TextRegular,
            ),
            kSmHeight,
            CustomTextFormField(
              maxLines: 3,
              validator: (value) {
                if (value!.isNotEmpty) {
                  bool isValidURL = RegExp(
                          r'^(https?:\/\/)?([\w\-])+\.{1}([a-zA-Z]{2,63})([\/\w\.-]*)*\/?$')
                      .hasMatch(value);
                  if (!isValidURL) {
                    return 'Please enter a valid URL';
                  }
                }
                return null;
              },
              hintText: "https://",
              onChanged: (val) {
                businessVM.userModel.value.youtubeLink = val;
              },
              initialValue: businessVM.userModel.value.youtubeLink,
            ),
          ],
        ),
      ],
    );
  }
}
