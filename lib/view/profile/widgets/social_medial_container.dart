import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class SocialMediaContainer extends StatelessWidget {
  final void Function()? onTap;
  final String title;
  final String imagePath;
  const SocialMediaContainer(
      {super.key, this.onTap, required this.title, required this.imagePath});

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap,
      child: Container(
        height: 36,
        width: 130,
        padding: const EdgeInsets.symmetric(horizontal: 9),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.lgrey),
        ),
        child: Row(
          children: [
            SvgPicture.asset(imagePath),
            const Gap(10),
            Expanded(
                child: Text(title,
                    style: body2TextRegular, overflow: TextOverflow.ellipsis))
          ],
        ),
      ),
    );
  }
}
