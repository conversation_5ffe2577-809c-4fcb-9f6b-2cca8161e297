import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class AddressCard extends StatelessWidget {
  final String title;
  final String address;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const AddressCard({
    super.key,
    required this.title,
    required this.address,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.midGrey.withOpacity(0.3),
              ),
            ),
            child: const Icon(
              Icons.location_on_outlined,
              color: AppColors.kprimarycolor,
            ),
          ),
          const Gap(20),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: bodyTextSemiBold),
             screenWidth <= 820 ?   Text(
                    address,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 4,
                    style: bodyTextRegular.copyWith(color: AppColors.secondary),
                  ) :  SizedBox(
                  width: Get.width * .4,
                  child: Text(
                    address,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    style: bodyTextRegular.copyWith(color: AppColors.secondary),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Bounceable(
                      onTap: onEdit,
                      child: Text(
                        "Edit",
                        style: bodyTextMedium.copyWith(
                          color: AppColors.kprimarycolor,
                        ),
                      ),
                    ),
                    const Gap(20),
                    Bounceable(
                      onTap: onDelete,
                      child: Text(
                        "Delete",
                        style: bodyTextMedium.copyWith(
                          color: AppColors.kprimarycolor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
