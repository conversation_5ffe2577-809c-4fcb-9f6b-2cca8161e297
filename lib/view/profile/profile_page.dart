import 'dart:async';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_business/widget/kyc_review.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';
import 'package:parenthing_dashboard/view/profile/business_kyc_details_sidebar.dart';
import 'package:parenthing_dashboard/view/profile/profile_incomplete.dart';
import 'package:parenthing_dashboard/view/profile/widgets/address_widget.dart';
import 'package:parenthing_dashboard/view/profile/widgets/social_medial_container.dart';
import 'package:url_launcher/url_launcher.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final BusinessController businessController = Get.find<BusinessController>();

  @override
  void initState() {
    super.initState();
    businessController.businessProfileDetails();
    Future.microtask(() {
      businessController.updateLastActive();
    });
  }

  @override
  Widget build(BuildContext context) {
    double scrrenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 8,
                  child: Container(
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: AppColors.lgrey,
                      ),
                    ),
                    child: Obx(
                      () => businessController.isProfileDetailsLoading.value
                          ? const Center(child: CircularProgressIndicator())
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                businessController.userModel.value
                                            .isprofileComplete ==
                                        100
                                    ? const SizedBox()
                                    : const ProfileInComplete(),
                                const Gap(20),
                                _buildProfileDetails(),
                                const Gap(10),
                                businessController.userModel.value.kycStatus ==
                                        ""
                                    ? const SizedBox()
                                    : Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Divider(),
                                          const Gap(10),
                                          Text("KYC details",
                                              style: title2TextSemiBold),
                                          const Gap(10),
                                          _buildKYCDetails(),
                                          // const Gap(10),
                                        ],
                                      ),
                                const Divider(),
                                const Gap(10),
                                _builPointofContact(),
                                const Gap(10),
                                const Divider(),
                                const Gap(10),
                                Text("Business details",
                                    style: title2TextMedium),
                                const Gap(20),
                                Text("Type of business",
                                    style: bodyTextRegular.copyWith(
                                        color: AppColors.secondary)),
                                const Gap(5),
                                Text(
                                    businessController
                                                .userModel.value.businessType ==
                                            'both'
                                        ? 'Conduct Classes & Events'
                                        : businessController
                                            .userModel.value.businessType,
                                    style: bodyTextSemiBold),
                                const Gap(10),
                                // Text(
                                //   "About your business",
                                //   style: bodyTextRegular.copyWith(
                                //       color: AppColors.secondary),
                                // ),
                                // const Gap(5),
                                // Text(
                                //     businessController.userModel.value
                                //             .businessDescription.isEmpty
                                //         ? "-"
                                //         : businessController.userModel.value
                                //             .businessDescription,
                                //     style: bodyTextSemiBold),
                                // const Gap(20),
                                const Divider(),
                                const Gap(10),
                                Text("Address", style: title2TextMedium),
                                const Gap(20),
                                businessController
                                        .userModel.value.location.isEmpty
                                    ? Text(
                                        "No Address available",
                                        style: bodyTextSemiBold,
                                      )
                                    : ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: businessController
                                            .userModel.value.location.length,
                                        itemBuilder: (context, index) {
                                          return AddressCard(
                                            address: businessController
                                                .userModel
                                                .value
                                                .location[index]
                                                .address,
                                            title: businessController.userModel
                                                .value.location[index].title,
                                            onDelete: () {
                                              showDialog(
                                                context: context,
                                                barrierDismissible: false,
                                                builder:
                                                    (BuildContext context) {
                                                  return CustomDialog(
                                                    onConfirmTxt: "Delete",
                                                    onCancelText: "Cancel",
                                                    title: 'Remove address',
                                                    content:
                                                        'Are you sure you want to delete this address.',
                                                    image:
                                                        "assets/icons/Trash_red.svg",
                                                    onConfirm: () {
                                                      Navigator.pop(context);
                                                      businessController
                                                          .deleteBusinessAddress(
                                                              businessController
                                                                  .userModel
                                                                  .value
                                                                  .location[
                                                                      index]
                                                                  .locationId)
                                                          .then((value) {
                                                        if (value == true) {
                                                          businessController
                                                              .businessProfileDetails();
                                                        } else {
                                                          CustomSnackBar
                                                              .showError(
                                                            "Please try again later.",
                                                            "Address in use; can’t delete now.",
                                                          );
                                                        }
                                                      });
                                                    },
                                                    onCancel: () {
                                                      Navigator.of(context)
                                                          .pop();
                                                    },
                                                  );
                                                },
                                              );
                                            },
                                            onEdit: () {
                                              locator<NavigationServices>()
                                                  .navigateTo(
                                                addAddressPage,
                                                arguments: ProfileAddAddress(
                                                    isEditing: true,
                                                    addressIndex: index),
                                              );
                                            },
                                          );
                                        },
                                      ),
                                const Gap(10),
                                SecondaryButton(
                                  width: 200,
                                  text: "+ New address",
                                  onTap: () {
                                    locator<NavigationServices>().navigateTo(
                                      addAddressPage,
                                      arguments: const ProfileAddAddress(
                                          isEditing: false),
                                    );
                                  },
                                )
                              ],
                            ),
                    ),
                  ),
                ),
                scrrenWidth <= 1024 ? const SizedBox() : const Gap(10),
                scrrenWidth <= 1024
                    ? const SizedBox()
                    : Expanded(
                        flex: 3,
                        child: Column(
                          children: [
                            parentsInviteWidget(),
                            const Gap(10),
                            businessInviteWidget(),
                          ],
                        ),
                      )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileDetails() {
    double screenWidth = MediaQuery.of(context).size.width;
    return screenWidth <= 820
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: SecondaryButton(
                  isImage: true,
                  imagePath: "assets/icons/PencilSimple.svg",
                  width: 100,
                  text: "Edit",
                  onTap: () {
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext context) {
                        return CustomDialog(
                          onConfirmTxt: "Yes",
                          onCancelText: "No",
                          title: 'Are you sure?',
                          content:
                              'You want to change details in your profile?',
                          image: "assets/icons/WarningCircle.svg",
                          onConfirm: () {
                            Navigator.pop(context);
                            locator<NavigationServices>()
                                .navigateTo(businessEditPage)
                                .then((value) {
                              businessController.businessProfileDetails();
                            });
                          },
                          onCancel: () {
                            Navigator.of(context).pop();
                          },
                        );
                      },
                    );
                  },
                ),
              ),
              Center(
                  child: businessController
                          .userModel.value.profilePictureUrl.isNotEmpty
                      ? CircleAvatar(
                          radius: 50,
                          backgroundImage: NetworkImage(
                            businessController
                                .userModel.value.profilePictureUrl,
                          ))
                      : SvgPicture.asset(
                          "assets/icons/logo.svg",
                          height: 100,
                          width: 100,
                          fit: BoxFit.cover,
                        )),
              const Gap(12),
              Center(
                child: Text(
                  businessController.userModel.value.businessName,
                  style: heading2TextRegular,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
              ),
              const Gap(10),
              _buildContactInfo(),
              const Gap(10),
              _buildSocialMediaLinks(),
            ],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              businessController.userModel.value.profilePictureUrl.isEmpty
                  ? SvgPicture.asset(
                      "assets/icons/logo.svg",
                      height: 100,
                      width: 100,
                      fit: BoxFit.cover,
                    )
                  : CircleAvatar(
                      minRadius: 80,
                      maxRadius: 80,
                      backgroundImage: NetworkImage(
                          businessController.userModel.value.profilePictureUrl),
                    ),
              const Gap(12),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                              businessController.userModel.value.businessName,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: heading2TextRegular),
                        ),
                        // const Spacer(),
                        const Gap(20),
                        SecondaryButton(
                          isImage: true,
                          imagePath: "assets/icons/PencilSimple.svg",
                          width: 100,
                          text: "Edit",
                          onTap: () {
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return CustomDialog(
                                  onConfirmTxt: "Yes",
                                  onCancelText: "No",
                                  title: 'Are you sure?',
                                  content:
                                      'You want to change details in your profile?',
                                  image: "assets/icons/WarningCircle.svg",
                                  onConfirm: () {
                                    Navigator.pop(context);
                                    locator<NavigationServices>()
                                        .navigateTo(businessEditPage)
                                        .then((value) {
                                      businessController
                                          .businessProfileDetails();
                                    });
                                  },
                                  onCancel: () {
                                    Navigator.of(context).pop();
                                  },
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        SvgPicture.asset("assets/icons/Envelope.svg",
                            fit: BoxFit.contain),
                        const Gap(5),
                        Text(
                          businessController.userModel.value.email,
                          style: bodyTextRegular.copyWith(
                              color: AppColors.txtsecondary),
                        ),
                      ],
                    ),
                    const Gap(5),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/MapPin.svg",
                          fit: BoxFit.contain,
                        ),
                        const Gap(5),
                        Expanded(
                          child: Text(
                            businessController.userModel.value.officeAddress,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: bodyTextRegular.copyWith(
                                color: AppColors.txtsecondary),
                          ),
                        ),
                      ],
                    ),
                    const Gap(5),
                    businessController.userModel.value.businessPhone.isEmpty
                        ? const SizedBox()
                        : Row(
                            children: [
                              SvgPicture.asset(
                                "assets/icons/Phone.svg",
                                fit: BoxFit.contain,
                              ),
                              const Gap(5),
                              Text(
                                '+91-${businessController.userModel.value.businessPhone}',
                                style: bodyTextRegular.copyWith(
                                    color: AppColors.txtsecondary),
                              ),
                            ],
                          ),
                    const Gap(20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        businessController.userModel.value.websiteLink.isEmpty
                            ? const SizedBox()
                            : SocialMediaContainer(
                                imagePath: "assets/icons/Link.svg",
                                title: "Website",
                                onTap: () {
                                  _launchURL(businessController
                                      .userModel.value.websiteLink);
                                },
                              ),
                        const Gap(10),
                        businessController.userModel.value.instagramLink.isEmpty
                            ? const SizedBox()
                            : SocialMediaContainer(
                                imagePath: "assets/icons/InstagramLogo.svg",
                                title: "Instagram",
                                onTap: () {
                                  _launchURL(businessController
                                      .userModel.value.instagramLink);
                                },
                              ),
                        const Gap(10),
                        businessController.userModel.value.facebookLink.isEmpty
                            ? const SizedBox()
                            : SocialMediaContainer(
                                imagePath: "assets/icons/FacebookLogo.svg",
                                title: "Facebook",
                                onTap: () {
                                  _launchURL(businessController
                                      .userModel.value.facebookLink);
                                },
                              ),
                        const Gap(10),
                        businessController.userModel.value.youtubeLink.isEmpty
                            ? const SizedBox()
                            : SocialMediaContainer(
                                imagePath: "assets/icons/YoutubeLogo.svg",
                                title: 'YouTube',
                                onTap: () {
                                  _launchURL(businessController
                                      .userModel.value.youtubeLink);
                                },
                              ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          );
  }

  Widget _buildContactInfo() {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset("assets/icons/Envelope.svg", fit: BoxFit.contain),
            const Gap(5),
            Expanded(
              child: Text(
                businessController.userModel.value.email,
                style: bodyTextRegular.copyWith(color: AppColors.txtsecondary),
              ),
            ),
          ],
        ),
        const Gap(5),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgPicture.asset("assets/icons/MapPin.svg", fit: BoxFit.contain),
            const Gap(5),
            Expanded(
              child: Text(
                businessController.userModel.value.officeAddress,
                style: bodyTextRegular.copyWith(color: AppColors.txtsecondary),
              ),
            ),
          ],
        ),
        // Add phone number if available
      ],
    );
  }

  Widget _buildSocialMediaLinks() {
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: [
        businessController.userModel.value.websiteLink.isEmpty
            ? const SizedBox()
            : SocialMediaContainer(
                imagePath: "assets/icons/Link.svg",
                title: "Website",
                onTap: () {
                  _launchURL(businessController.userModel.value.websiteLink);
                },
              ),
        const Gap(10),
        businessController.userModel.value.instagramLink.isEmpty
            ? const SizedBox()
            : SocialMediaContainer(
                imagePath: "assets/icons/InstagramLogo.svg",
                title: "Instagram",
                onTap: () {
                  _launchURL(businessController.userModel.value.instagramLink);
                },
              ),
        const Gap(10),
        businessController.userModel.value.facebookLink.isEmpty
            ? const SizedBox()
            : SocialMediaContainer(
                imagePath: "assets/icons/FacebookLogo.svg",
                title: "Facebook",
                onTap: () {
                  _launchURL(businessController.userModel.value.facebookLink);
                },
              ),
        const Gap(10),
        businessController.userModel.value.youtubeLink.isEmpty
            ? const SizedBox()
            : SocialMediaContainer(
                imagePath: "assets/icons/YoutubeLogo.svg",
                title: 'YouTube',
                onTap: () {
                  _launchURL(businessController.userModel.value.youtubeLink);
                },
              ),
      ],
    );
  }

  Widget _buildKYCDetails() {
    double screenWidth = MediaQuery.of(context).size.width;
    return SizedBox(
      width: screenWidth <= 820 ? Get.width : Get.width * .35,
      child: Row(
        children: [
          Text("KYC status",
              style: bodyTextRegular.copyWith(color: AppColors.secondary)),
          const Spacer(),
          Text(
            businessController.userModel.value.kycStatus.capitalizeFirst
                .toString(),
            textAlign: TextAlign.start,
            style: bodyTextSemiBold.copyWith(
                color:
                    businessController.userModel.value.kycStatus == 'approved'
                        ? Colors.green
                        : businessController.userModel.value.kycStatus ==
                                'requested'
                            ? Colors.orange
                            : Colors.red),
          ),
          screenWidth <= 820
              ? IconButton(
                  onPressed: () {
                    Get.to(
                      () => const BusinessKycDetails(),
                    );
                  },
                  icon: const Icon(
                    Icons.visibility,
                    color: AppColors.kprimarycolor,
                  ),
                )
              : IconButton(
                  onPressed: () {
                    Get.dialog(
                      const KycReviewPanel(
                        content: BusinessKycDetails(),
                      ),
                      barrierDismissible: true,
                    );
                  },
                  icon: const Icon(
                    Icons.visibility,
                    color: AppColors.kprimarycolor,
                  ),
                ),
        ],
      ),
    );
  }

  Widget _builPointofContact() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text("Point of contact", style: title2TextMedium),
        const Gap(20),
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text("Person Name",
                      style: bodyTextRegular.copyWith(
                          color: AppColors.txtsecondary)),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(businessController.userModel.value.pocName,
                        textAlign: TextAlign.start, style: bodyTextSemiBold),
                  ),
                ),
                const Spacer(),
              ],
            ),
            const Gap(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text("Phone number of PoC",
                      style: bodyTextRegular.copyWith(
                          color: AppColors.txtsecondary)),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(businessController.userModel.value.pocNumber,
                        textAlign: TextAlign.start, style: bodyTextSemiBold),
                  ),
                ),
                const Spacer(),
              ],
            ),
            const Gap(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    "Alternate Phone number of PoC",
                    style:
                        bodyTextRegular.copyWith(color: AppColors.txtsecondary),
                  ),
                ),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      businessController.userModel.value.pocAltNumber.isEmpty
                          ? "N/A"
                          : businessController.userModel.value.pocAltNumber,
                      textAlign: TextAlign.start,
                      style: bodyTextSemiBold,
                    ),
                  ),
                ),
                const Spacer(),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _launchURL(String urlString, {bool isPhone = false}) async {
    // Ensure URL has proper scheme for web URLs
    String processedUrl = urlString;
    if (!isPhone &&
        !urlString.startsWith('http://') &&
        !urlString.startsWith('https://')) {
      processedUrl = 'https://$urlString';
    }

    final Uri url = Uri.parse(processedUrl);
    final Uri launchPhone = Uri(
      scheme: 'tel',
      path: urlString,
    );
    final Uri targetUri = isPhone ? launchPhone : url;

    if (!await canLaunchUrl(targetUri)) {
      if (kIsWeb && !isPhone) {
        try {
          await launchUrl(
            targetUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return;
        } catch (e) {
          throw Exception('Could not launch $targetUri: $e');
        }
      }
      throw Exception('Could not launch $targetUri');
    }

    // Use different launch modes based on platform
    if (kIsWeb) {
      // For web, force external browser
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
        webOnlyWindowName: '_blank',
      );
    } else {
      // For mobile platforms
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
      );
    }
  }

  bool showParentsCopied = false;
  bool showBusinessCopied = false;

  void showCopyFeedback(bool isParents) {
    setState(() {
      if (isParents) {
        showParentsCopied = true;
      } else {
        showBusinessCopied = true;
      }
    });

    Timer(const Duration(milliseconds: 600), () {
      if (mounted) {
        setState(() {
          if (isParents) {
            showParentsCopied = false;
          } else {
            showBusinessCopied = false;
          }
        });
      }
    });
  }

  parentsInviteWidget() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(11),
        border: Border.all(color: AppColors.lgrey, width: 1.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SvgPicture.asset(
            'assets/svg/UserCirclePlus.svg',
            height: 65,
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Invite Parents',
                  style: body2TextBold,
                ),
                Text(
                  "Invite parents in your network to discover classes and events you create on Parenthing.",
                  textAlign: TextAlign.start,
                  maxLines: 3,
                  style: body2TextRegular,
                ),
                const Gap(20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hi! You can now see my upcoming classes and events for your kids on Parenthing.\n\nVisit: https://www.parenthingapp.com";
                        FlutterClipboard.copy(shareContent).then((value) {
                          showCopyFeedback(true);
                        });
                      },
                      child: showParentsCopied == true
                          ? Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.kprimarycolor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'Copied',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            )
                          : const FaIcon(
                              FontAwesomeIcons.copy,
                              size: 24.0,
                              color: AppColors.kprimarycolor,
                            ),
                    ),
                    const Gap(20),
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hi! You can now see my upcoming classes and events for your kids on Parenthing.\n\nVisit: https://www.parenthingapp.com";
                        _launchWhatsApp(shareContent);
                      },
                      child: const FaIcon(
                        FontAwesomeIcons.whatsapp,
                        size: 24.0,
                        color: AppColors.kprimarycolor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  businessInviteWidget() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(11),
        border: Border.all(color: AppColors.lgrey, width: 1.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/svg/Buildings_invite.svg',
          ),
          const Gap(10),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                'Invite Business',
                style: body2TextBold,
              ),
              Text(
                'Have family or friends who also run classes or events for kids? Share about Parenthing to help them out.',
                textAlign: TextAlign.start,
                overflow: TextOverflow.ellipsis,
                maxLines: 5,
                style: body2TextRegular,
              ),
              const Gap(20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Bounceable(
                    onTap: () {
                      String shareContent =
                          "Hey! I'm using Parenthing to get more bookings for my classes and events. I think it'd be great for you too.\n\nCheck out: https://www.parenthingapp.com/business";
                      FlutterClipboard.copy(shareContent).then((value) {
                        showCopyFeedback(false);
                      });
                    },
                    child: showBusinessCopied == true
                        ? Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.kprimarycolor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Copied',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          )
                        : const FaIcon(
                            FontAwesomeIcons.copy,
                            size: 24.0,
                            color: AppColors.kprimarycolor,
                          ),
                  ),
                  const Gap(20),
                  Bounceable(
                    onTap: () {
                      String shareContent =
                          "Hey! I'm using Parenthing to get more bookings for my classes and events. I think it'd be great for you too.\n\nCheck out: https://www.parenthingapp.com/business";
                      _launchWhatsApp(shareContent);
                    },
                    child: const FaIcon(
                      FontAwesomeIcons.whatsapp,
                      size: 24.0,
                      color: AppColors.kprimarycolor,
                    ),
                  ),
                ],
              ),
            ],
          )),
        ],
      ),
    );
  }

  Future<void> _launchWhatsApp(String message) async {
    //const phoneNumber = '+9185305 72636';
    final uri =
        Uri.parse('https://wa.me/?text=${Uri.encodeComponent(message)}');
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $uri';
      }
    } catch (e) {
      debugPrint('Error launching WhatsApp: $e');
    }
  }
}
