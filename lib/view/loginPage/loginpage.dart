import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'dart:async';

import 'package:parenthing_dashboard/view/loginPage/business_onboard_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final UserController userController = Get.find<UserController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool isOTP = false;
  void goBackToLogin() {
    setState(() {
      isOTP = false;
    });
  }

  void _handleOtpRequest() async {
    if (userController.phoneController.value.text.length == 10) {
      // Loading state is handled inside the API method
      final success = await userController.sendOtpAPI(
          phone: userController.phoneController.value.text);

      setState(() {
        isOTP = success;
      });
    } else {
      CustomSnackBar.showErrorSnackbar(
          "Invalid Input", "Please enter a valid 10-digit mobile number");
    }
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.backcolor,
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/BG.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
            if (!isOTP)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/app_logo.svg',
                    ),
                    const Gap(30),
                    Container(
                      width: screenWidth <= 480
                          ? Get.width * 0.8 // Mobile (screen width up to 430)
                          : screenWidth <= 850
                              ? Get.width *
                                  0.6 // Tablet (screen width up to 768)
                              : screenWidth <= 1080
                                  ? Get.width *
                                      0.4 // Laptop (screen width up to 1080)
                                  : Get.width *
                                      0.3, // Laptop/Desktop (screen width 1024 and above)
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.kwhite,
                        borderRadius: BorderRadius.circular(20.0),
                        border: Border.all(color: AppColors.kgrey, width: 1),
                      ),
                      child: SingleChildScrollView(
                        // padding: const EdgeInsets.only(bottom: 0.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Login to your account',
                                style: headingTextSemiBold.copyWith(
                                    fontSize: screenWidth <= 480
                                        ? 22
                                        : screenWidth <= 850
                                            ? 24
                                            : screenWidth <= 1080
                                                ? 26
                                                : 28),
                                textAlign: TextAlign.center,
                              ),
                              const Gap(12),
                              Text(
                                'Welcome back! Please enter your registered phone number to continue.',
                                style: title3TextRegular.copyWith(
                                    fontSize: screenWidth <= 480
                                        ? 14
                                        : screenWidth <= 850
                                            ? 18
                                            : screenWidth <= 1080
                                                ? 18
                                                : 18,
                                    color: AppColors.txtsecondary),
                                textAlign: TextAlign.center,
                              ),
                              const Gap(30),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text('Phone number',
                                    style: body2TextRegular),
                              ),
                              const Gap(10),
                              CustomTextFormField(
                                fontstyle: bodyTextMedium,
                                controller:
                                    userController.phoneController.value,
                                hintText: "Enter your phone number",
                                keyboardType: TextInputType.phone,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(10),
                                ],
                                maxLength: 10,
                                maxLines: 1,
                                onChanged: (value) {},
                                onSubmit: (value) {
                                  _handleOtpRequest();
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return "Enter your phone number";
                                  }
                                  // else if (value.length != 10) {
                                  //   return "Phone number must be 10 digits";
                                  // } else if (!RegExp(r'^[6-9]\d{9}$')
                                  //     .hasMatch(value)) {
                                  //   return "Enter a valid phone number";
                                  // }
                                  return null;
                                },
                              ),
                              const Gap(30),
                              Obx(
                                () => PrimaryButton(
                                  isLoading:
                                      userController.isSendOTPLoading.value,
                                  text: "Request OTP",
                                  onTap: () async {
                                    if (userController.phoneController.value
                                            .text.length ==
                                        10) {
                                      userController.isSendOTPLoading.value =
                                          true;
                                      await userController
                                          .sendOtpAPI(
                                              phone: userController
                                                  .phoneController.value.text)
                                          .then((value) {
                                        userController.isSendOTPLoading.value =
                                            false;
                                        if (value == true) {
                                          setState(() {
                                            isOTP = true;
                                          });
                                        } else {
                                          setState(() {
                                            isOTP = false;
                                          });
                                        }
                                      });
                                    } else {
                                      CustomSnackBar.showErrorSnackbar(
                                          "Invalid Input",
                                          "Please enter a valid 10-digit mobile number");
                                    }
                                  },
                                ),
                              ),
                              const Gap(16),
                              SecondaryButton(
                                textColor: AppColors.kprimarycolor,
                                text: "Register your business",
                                onTap: () {
                                  Get.to(() => const BusinessOnBoardingPage());
                                },
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            if (isOTP)
              Otp(
                (Function stopTimer) {
                  if (userController.otpController.value.text.length == 4) {
                    userController.isSendOTPLoading.value = true;
                    userController
                        .verifyOtpAPI(
                            otp: userController.otpController.value.text,
                            phone: userController.phoneController.value.text)
                        .then((value) {
                      if (value == true) {
                        stopTimer();
                        userController.isSendOTPLoading.value = false;
                        CustomSnackBar.showInfoSnackbar(
                            "Success", "Login successful! Welcome.");
                      } else {
                        CustomSnackBar.showErrorSnackbar("Invalid OTP",
                            "The OTP you entered is incorrect. Please try again.");
                      }
                    });
                  } else {
                    CustomSnackBar.showErrorSnackbar("Missing OTP",
                        "Please enter the 4-digit OTP sent to your phone.");
                  }
                },
                height,
                screenWidth,
                goBackToLogin,
              ),
          ],
        ),
      ),
    );
  }
}

class Otp extends StatefulWidget {
  final Function(Function stopTimer) onTap;

  final VoidCallback onBackPress;

  final double height;
  final double width;

  const Otp(this.onTap, this.height, this.width, this.onBackPress, {super.key});

  @override
  State<Otp> createState() => _OtpState();
}

class _OtpState extends State<Otp> {
  late Timer timer;
  int resendTimer = 45;
  bool isVerifying = false;
  final UserController userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  void startTimer() {
    const oneSec = Duration(seconds: 1);
    timer = Timer.periodic(oneSec, (Timer t) {
      if (resendTimer == 0) {
        setState(() {
          timer.cancel();
        });
      } else {
        setState(() {
          resendTimer--;
        });
      }
    });
  }

  void stopTimer() {
    timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Align(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/images/app_logo.svg',
          ),
          const Gap(30),
          Container(
            width: screenWidth <= 480
                ? Get.width * 0.8 // Mobile (screen width up to 430)
                : screenWidth <= 850
                    ? Get.width * 0.6 // Tablet (screen width up to 768)
                    : screenWidth <= 1080
                        ? Get.width * 0.4 // Laptop (screen width up to 1080)
                        : Get.width *
                            0.3, // Laptop/Desktop (screen width 1024 and above)
            padding: const EdgeInsets.all(30.0),
            decoration: BoxDecoration(
              color: AppColors.kwhite,
              borderRadius: BorderRadius.circular(20.0),
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(bottom: 0.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: widget.onBackPress,
                      ),
                      Expanded(
                        child: Center(
                          child: Text(
                            'OTP verification',
                            style: rubikStyle.copyWith(
                              fontSize: screenWidth <= 480
                                  ? 18
                                  : screenWidth <= 850
                                      ? 22
                                      : screenWidth <= 1080
                                          ? 22
                                          : 25.0,
                              color: AppColors.kblack,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ),
                      const Gap(20),
                    ],
                  ),
                  kSmHeight,
                  Text(
                    'Please enter the verification code we sent to your phone number',
                    style: rubikStyle.copyWith(
                      fontSize: 16.0,
                      color: AppColors.secondary,
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  kHeight,
                  CustomPinInput(
                    length: 4,
                    onChanged: (value) {
                      userController.otpController.value.text = value;
                    },
                    onCompleted: () {
                      log("OTP completed");
                      widget.onTap(stopTimer);
                    },
                  ),
                  const Gap(30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("Didn't get the OTP? ",
                          style: body2TextRegular.copyWith(
                              color: AppColors.txtsecondary)),
                      resendTimer == 0
                          ? InkWell(
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              overlayColor:
                                  MaterialStateProperty.all(Colors.transparent),
                              onTap: () {
                                if (resendTimer == 0) {
                                  setState(() {
                                    resendTimer = 45;
                                    userController.sendOtpAPI(
                                        phone: userController
                                            .phoneController.value.text);
                                  });
                                  startTimer();
                                }
                              },
                              child: Center(
                                child: Text(
                                  'Resend OTP',
                                  style: body2TextMedium.copyWith(
                                    color: resendTimer == 0
                                        ? AppColors.kprimarycolor
                                        : AppColors.ktertiary,
                                  ),
                                ),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Resend in ',
                                  style: body2TextMedium,
                                ),
                                Text(
                                  "00:${resendTimer < 10 ? '0$resendTimer' : resendTimer}",
                                  style: body2TextMedium,
                                ),
                              ],
                            ),
                    ],
                  ),
                  kMinHeight,
                  Obx(
                    () => PrimaryButton(
                      isLoading: userController.isSendOTPLoading.value,
                      text: "Login",
                      onTap: () => widget.onTap(stopTimer),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomPinInput extends StatefulWidget {
  final int length;
  final ValueChanged<String> onChanged;
  final VoidCallback? onCompleted;

  const CustomPinInput(
      {super.key,
      required this.length,
      required this.onChanged,
      this.onCompleted});

  @override
  // ignore: library_private_types_in_public_api
  _CustomPinInputState createState() => _CustomPinInputState();
}

class _CustomPinInputState extends State<CustomPinInput> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(widget.length, (_) => TextEditingController());
    _focusNodes = List.generate(widget.length, (_) => FocusNode());
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNodes.first.requestFocus();
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focus in _focusNodes) {
      focus.dispose();
    }
    super.dispose();
  }

  void _onChanged() {
    String pin = _controllers.map((c) => c.text).join();
    widget.onChanged(pin);
    if (pin.length == widget.length && widget.onCompleted != null) {
      FocusScope.of(context).unfocus();
      widget.onCompleted!();
    }
  }

  void _handleSubmit() {
    String pin = _controllers.map((c) => c.text).join();
    if (pin.length == widget.length && widget.onCompleted != null) {
      widget.onCompleted!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: List.generate(
        widget.length,
        (index) => SizedBox(
          width: 50,
          child: Center(
            child: TextField(
              controller: _controllers[index],
              focusNode: _focusNodes[index],
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(1),
              ],
              textInputAction: index == widget.length - 1
                  ? TextInputAction.done
                  : TextInputAction.next,
              onSubmitted: (_) {
                FocusScope.of(context).unfocus();
                _handleSubmit();
              },
              decoration: InputDecoration(
                hintText: '0',
                hintStyle:
                    body2TextMedium.copyWith(color: AppColors.placeHolder),
                fillColor: AppColors.backcolor,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4.0),
                  borderSide: BorderSide.none,
                ),
              ),
              style: bodyTextMedium,
              onChanged: (value) {
                if (value.isNotEmpty && index < widget.length - 1) {
                  _focusNodes[index + 1].requestFocus();
                } else if (value.isEmpty && index > 0) {
                  _focusNodes[index - 1].requestFocus();
                }
                _onChanged();
              },
            ),
          ),
        ),
      ),
    );
  }
}
