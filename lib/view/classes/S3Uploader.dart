// import 'dart:convert';

// import 'package:crypto/crypto.dart';

// class S3Uploader {
//   final String accessKeyId;
//   final String secretAccessKey;
//   final String region;
//   final String bucketName;

//   S3Uploader({
//     required this.accessKeyId,
//     required this.secretAccessKey,
//     required this.region,
//     required this.bucketName,
//   });

//   List<int> _sign(List<int> key, String message) {
//     final hmac = Hmac(sha256, key);
//     return hmac.convert(utf8.encode(message)).bytes;
//   }

//   List<int> _getSignatureKey(
//       String key, String dateStamp, String regionName, String serviceName) {
//     final kDate = _sign(utf8.encode('AWS4$key'), dateStamp);
//     final kRegion = _sign(kDate, regionName);
//     final kService = _sign(kRegion, serviceName);
//     final kSigning = _sign(kService, 'aws4_request');
//     return kSigning;
//   }

//   Future<String> generatePresignedUrl(
//       String objectKey, String contentType) async {
//     final method = 'PUT';
//     final service = 's3';
//     final host = '$bucketName.s3.$region.amazonaws.com';
//     final endpoint = 'https://$host/$objectKey';
//     final date = DateTime.now().toUtc();
//     final amzDate = date
//             .toIso8601String()
//             .replaceAll('-', '')
//             .replaceAll(':', '')
//             .split('.')
//             .first +
//         'Z';
//     final shortDate = amzDate.split('T').first;
//     final credentialScope = '$shortDate/$region/$service/aws4_request';
//     final signedHeaders = 'host';
//     final payloadHash = sha256.convert(utf8.encode('')).toString();

//     final canonicalRequest = [
//       method,
//       '/$objectKey',
//       '',
//       'host:$host',
//       '',
//       signedHeaders,
//       payloadHash,
//     ].join('\n');

//     final stringToSign = [
//       'AWS4-HMAC-SHA256',
//       amzDate,
//       credentialScope,
//       sha256.convert(utf8.encode(canonicalRequest)).toString(),
//     ].join('\n');

//     final signingKey =
//         _getSignatureKey(secretAccessKey, shortDate, region, service);
//     final signature =
//         Hmac(sha256, signingKey).convert(utf8.encode(stringToSign)).toString();

//     final presignedUrl = '$endpoint?X-Amz-Algorithm=AWS4-HMAC-SHA256'
//         '&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD'
//         '&X-Amz-Credential=${Uri.encodeComponent(accessKeyId + "/" + credentialScope)}'
//         '&X-Amz-Date=$amzDate'
//         '&X-Amz-Expires=900'
//         '&X-Amz-SignedHeaders=$signedHeaders'
//         '&X-Amz-Signature=$signature'
//         '&X-Amz-SignedHeaders=host'
//         '&X-id=PutObject';

//     return presignedUrl;
//   }
// }
