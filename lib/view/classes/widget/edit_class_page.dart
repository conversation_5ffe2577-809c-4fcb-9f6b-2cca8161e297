import 'dart:developer';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/model/class/category_model.dart';
import 'package:parenthing_dashboard/model/class/class_model.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';

class EditClassPage extends StatefulWidget {
  final Map<String, dynamic> arguments;
  final int? classId;
  // final bool isDuplicateClass;

  const EditClassPage({super.key, required this.arguments, this.classId = 0});

  @override
  State<EditClassPage> createState() => _EditClassPageState();
}

class _EditClassPageState extends State<EditClassPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final BusinessController businessController = Get.find<BusinessController>();
  RxBool isSelected = true.obs;
  late String classType;
  late bool isOfflineSelected;
  late bool isOnlineSelected;
  late bool isOfflineOnlineSelected;
  late String ticketType;
  late bool isTicketPaid;
  late bool isTicketFree;
  // bool isTicketPaid = false;
  // bool isTicketFree = false;
  late String sessionType;
  late bool isGroupSession;
  late bool is1Session;
  late bool isGroup1Session;
  bool isLoading = true;
  String selectedAddress = "";
  String selectedCity = "";

  final List<String> _ageOptions =
      List<String>.generate(17, (index) => (index).toString());

  final List<String> _maxAgeOptions =
      List<String>.generate(17, (index) => (index + 2).toString())..add('18+');
  String? _selectedMinAge;
  String? _selectedMaxAge;
  ClassModel? _classModel;
  int? id;
  String mode = "";
  bool isDuplicateClass = false;
  final ClassController classVM = Get.find<ClassController>();
  final EventController eventController = Get.find<EventController>();
  var classCategoryListData = <ClassCategoryModel>[].obs;

  // Uint8List? _imageBytes;
  String? imageUrl;
  // final ImagePicker picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    classType = "";
    isOfflineSelected = false;
    isOnlineSelected = false;
    isOfflineOnlineSelected = false;
    ticketType = "";
    isTicketPaid = false;
    isTicketFree = false;
    sessionType = "";
    isGroupSession = false;
    is1Session = false;
    isGroup1Session = false;
    selectedAddress = "";
    selectedCity = "";
    id = widget.arguments['classID']!;
    _classModel = widget.arguments['class_details']!;
    mode = widget.arguments['page_mode']!;
    isDuplicateClass = widget.arguments["isDuplicateClass"];
    selectedAddress = _classModel!.address;
    selectedCity = _classModel!.city;
    Future.delayed(const Duration(milliseconds: 300), () {
      setState(() {
        classVM.getClassCategoryListData().then((value) {
          if (value) {
            var matchedSubcategories = classVM.classCategoryList
                .where(
                    (category) => category.name.trim() == _classModel!.category)
                .expand((category) => category.subcategories)
                .toList();
            classVM.classSubCategories.value = matchedSubcategories;
          }
        });
        classVM.getClassDetailsData(widget.classId!);

        isLoading = false;
      });

      if (mode == 'EDIT') {
        if (_classModel != null) {
          log('class case 1');
          setBaseData();
        } else {
          log('class case 2');
        }
      }
    });
  }

  void setBaseData() {
    if (_classModel != null) {
      classVM.selectedClassCategoryName.value = _classModel!.category;
      classVM.selectedSubClassCategoryName.value = _classModel!.subCategory;
      setState(() {
        classType = _classModel!.classType.capitalizeFirst.toString();
        isOfflineSelected = classType == "Offline";
        isOnlineSelected = classType == "Online";
        isOfflineOnlineSelected = classType == "Offline + Online";
        ticketType = _classModel!.classFee.capitalizeFirst.toString();
        isTicketPaid = ticketType == "Paid";
        isTicketFree = ticketType == "Free";
        sessionType = _classModel!.sessionType.capitalizeFirst.toString();
        isGroupSession = sessionType == "Group";
        is1Session = sessionType == "1:1";
        isGroup1Session = sessionType == "Group + 1:1";

        if (_classModel!.maxAge == 0) {
          _selectedMaxAge = _ageOptions.first;
        } else if (_classModel!.maxAge > 18) {
          _selectedMaxAge = '18+';
        } else {
          _selectedMaxAge = _classModel!.maxAge.toString();
        }
        _selectedMinAge = _classModel!.minAge.toString();
        _selectedMaxAge = _classModel!.maxAge.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
        backgroundColor: AppColors.kwhite,
        body: isLoading
            ? const Center(child: CircularProgressIndicator.adaptive())
            : ScrollConfiguration(
                behavior:
                    ScrollConfiguration.of(context).copyWith(scrollbars: false),
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Container(
                      width: screenWidth <= 820 ? Get.width : Get.width * .7,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          width: 1.0,
                          color: AppColors.ktertiary.withOpacity(.4),
                        ),
                      ),
                      child: Form(
                        key: _formKey,
                        child: Column(children: [
                          Padding(
                            padding: const EdgeInsets.all(12),
                            child: Row(
                              children: [
                                InkWell(
                                  hoverColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  overlayColor: MaterialStateProperty.all(
                                      Colors.transparent),
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return CustomDialog(
                                          onConfirmTxt: "Yes, leave",
                                          onCancelText: "No",
                                          title: 'Leave this page?',
                                          content:
                                              'Are you sure you want to leave this page? All field details will be discarded',
                                          image:
                                              "assets/icons/WarningCircle.svg",
                                          onConfirm: () {
                                            Navigator.of(context).pop();
                                            locator<NavigationServices>()
                                                .goBack();
                                          },
                                          onCancel: () {
                                            Navigator.of(context).pop();
                                          },
                                        );
                                      },
                                    );
                                    // locator<NavigationServices>().goBack();
                                  },
                                  child: SvgPicture.asset(
                                    'assets/icons/arrow-left.svg',
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Text(
                                    mode == "EDIT"
                                        ? "Edit Class"
                                        : "Create a Class",
                                    style: heading2TextRegular),
                                const Spacer(),
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          screenWidth <= 820
                              ? Column(
                                  children: [
                                    buildEditClassBanner(),
                                    _buildEditClassNameField(),
                                  ],
                                )
                              : Row(
                                  children: [
                                    Expanded(child: _buildEditClassNameField()),
                                    buildEditClassBanner(),
                                  ],
                                ),
                          Obx(
                            () => Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: CustomDropdownUI(
                                      isValidatorReq: true,
                                      title: "Category",
                                      items: classVM.classCategoryList.map((e) {
                                        return DropdownMenuItem(
                                          onTap: () {
                                            classVM.updateSubCategories(e.name);
                                          },
                                          value: e.name,
                                          child: Text(
                                            e.name.toString(),
                                            style: body2TextBold,
                                          ),
                                        );
                                      }).toList(),
                                      value: classVM.selectedClassCategoryName
                                                  .value ==
                                              ''
                                          ? null
                                          : classVM
                                              .selectedClassCategoryName.value,
                                      onChanged: (v) {
                                        setState(() {
                                          classVM.selectedClassCategoryName
                                                  .value ==
                                              v;
                                          log(v.toString());
                                          _classModel!.category = v as String;
                                          log('Updated Model Category: ${_classModel!.category}');
                                        });
                                      },
                                    ),
                                  ),
                                  const Gap(20),
                                  Expanded(
                                    child: CustomDropdownUI(
                                      isValidatorReq: true,
                                      title: "Sub category",
                                      items:
                                          classVM.classSubCategories.map((e) {
                                        return DropdownMenuItem(
                                          onTap: () {
                                            //TODO:
                                          },
                                          value: e.name,
                                          child: Text(
                                            e.name.toString(),
                                            style: body2TextBold,
                                          ),
                                        );
                                      }).toList(),
                                      value: classVM
                                                  .selectedSubClassCategoryName
                                                  .value ==
                                              ''
                                          ? null
                                          : classVM.selectedSubClassCategoryName
                                              .value,
                                      onChanged: (v) {
                                        setState(() {
                                          classVM.selectedSubClassCategoryName
                                                  .value ==
                                              v;

                                          log(v.toString());
                                          _classModel!.subCategory =
                                              v as String;
                                          log('Updated Model SubCategory: ${_classModel!.subCategory}');
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Gap(10),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              screenWidth <= 820
                                  ? Column(
                                      children: [
                                        _buildEditClassAge(),
                                        _buildEditClassType()
                                      ],
                                    )
                                  : Row(
                                      children: [
                                        Expanded(
                                          child: _buildEditClassAge(),
                                        ),
                                        Expanded(
                                          child: _buildEditClassType(),
                                        )
                                      ],
                                    )
                            ],
                          ),
                          screenWidth < 820
                              ? Visibility(
                                  visible: !isOnlineSelected,
                                  child: Column(
                                    children: [
                                      //_buildEditClassCity(),
                                      _buildEditClassAddress()
                                    ],
                                  ))
                              : Visibility(
                                  visible: !isOnlineSelected,
                                  child: Row(
                                    children: [
                                      // Expanded(
                                      //   child: _buildEditClassCity(),
                                      // ),
                                      Expanded(
                                        child: _buildEditClassAddress(),
                                      ),
                                    ],
                                  )),
                          screenWidth <= 820
                              ? _buildEditClassSession()
                              : Row(
                                  children: [
                                    Expanded(
                                      child: _buildEditClassSession(),
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          screenWidth <= 820
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildEditClassFees(),
                                    if (!isTicketFree)
                                      SizedBox(
                                        width: Get.width * .5,
                                        child: _buildEditClassPrice(),
                                      )
                                  ],
                                )
                              : Row(
                                  children: [
                                    Expanded(
                                      child: _buildEditClassFees(),
                                    ),
                                    const Gap(20),
                                    isTicketFree
                                        ? const Spacer()
                                        : Expanded(
                                            child: _buildEditClassPrice())
                                  ],
                                ),
                          const Gap(10),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("Contact number* (booking and enquiry)",
                                    style: body2TextRegular),
                                const Gap(8),
                                Row(
                                  children: [
                                    Expanded(
                                      child: CustomTextFormField(
                                        initialValue: _classModel!.ctamobile,
                                        onChanged: (val) {
                                          _classModel!.ctamobile = "+91$val";
                                        },
                                        keyboardType: TextInputType.phone,
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                          LengthLimitingTextInputFormatter(10),
                                        ],
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Contact Required';
                                          }
                                          if (value.length != 10) {
                                            return 'Mobile number must be 10 digits';
                                          }
                                          if (!RegExp(r'^[6-9][0-9]{9}$')
                                              .hasMatch(value)) {
                                            return 'Please enter a valid Indian mobile number';
                                          }
                                          return null;
                                        },
                                        hintText: "+91-9001122445",
                                        prefixIcon: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 16),
                                          child: Text(
                                            "+91",
                                            style: body2TextMedium.copyWith(
                                                color: AppColors.txtprimary),
                                          ),
                                        ),
                                        maxLength: 10,
                                      ),
                                    ),
                                    const Gap(10),
                                    const Spacer(),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          screenWidth <= 820
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Padding(
                                      padding: EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          Icon(Icons.info_outline),
                                          Gap(8),
                                          Expanded(
                                            child: Text(
                                              "Please fill out all required fields to submit for review",
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Gap(15),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SecondaryButton(
                                          text: 'Save as draft',
                                          onTap: () async {
                                            onSubmitOrSaveDraft(false);
                                          },
                                        ),
                                        const Gap(20),
                                        PrimaryButton(
                                          text: 'Submit for review',
                                          onTap: () async {
                                            onSubmitOrSaveDraft(true);
                                          },
                                        ),
                                      ],
                                    ),
                                    const Gap(25),
                                  ],
                                )
                              : Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    children: [
                                      const Expanded(
                                        child: Row(
                                          children: [
                                            Icon(Icons.info_outline),
                                            Gap(8),
                                            Expanded(
                                              child: Text(
                                                  "Please fill out all required fields to submit for review"),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                          child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        children: [
                                          SecondaryButton(
                                            text: 'Save as draft',
                                            onTap: () async {
                                              onSubmitOrSaveDraft(false);
                                            },
                                          ),
                                          const Gap(20),
                                          PrimaryButton(
                                            text: 'Submit for review',
                                            onTap: () async {
                                              onSubmitOrSaveDraft(true);
                                            },
                                          ),
                                        ],
                                      ))
                                    ],
                                  ),
                                ),
                        ]),
                      ),
                    ),
                  ),
                ),
              ));
  }

  Widget buildEditClassBanner() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Class banner*", style: body2TextRegular),
              const Gap(10),
              Container(
                height: 260,
                // width: Get.width * 0.2,
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.kwhite,
                  border: Border.all(
                    color: AppColors.kblack.withOpacity(0.2),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: _classModel!.bannerUrl.isEmpty
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Icon(Icons.add_photo_alternate_outlined,
                              size: 60, color: AppColors.kprimarycolor),
                          const Gap(10),
                          Text(
                            "JPEG or PNG file formats supported in\n1080 x 1080px dimension, up to 1 MB",
                            style: body2TextRegular.copyWith(
                                color: AppColors.txtsecondary),
                          ),
                          const Gap(15),
                          TextButton(
                            child: Text(
                              'Upload Image',
                              textAlign: TextAlign.center,
                              style: bodyTextBold.copyWith(
                                color: const Color(0xff5E57E1),
                              ),
                            ),
                            onPressed: () async {
                              log("Upload Image button pressed");
                              FilePickerResult? result =
                                  await FilePicker.platform.pickFiles();

                              if (result != null) {
                                PlatformFile pickedFile = result.files.first;
                                Uint8List? fileBytes = result.files.first.bytes;
                                log("File selected: ${pickedFile.name}");
                                await _uploadFileHelper(
                                    pickedFile, null, fileBytes!);
                              } else {
                                log('No file selected.');
                              }
                            },
                          ),
                        ],
                      )
                    : Stack(
                        children: [
                          Image.network(
                            _classModel!.bannerUrl,
                            fit: BoxFit.cover,
                            height: 260,
                            width: screenWidth <= 820 ? Get.width : 260,
                          ),
                          Positioned(
                            top: 10,
                            right: 10,
                            child: InkWell(
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              overlayColor:
                                  MaterialStateProperty.all(Colors.transparent),
                              onTap: () async {
                                log("Upload Image button pressed");
                                FilePickerResult? result =
                                    await FilePicker.platform.pickFiles();

                                if (result != null) {
                                  PlatformFile pickedFile = result.files.first;
                                  Uint8List? fileBytes =
                                      result.files.first.bytes;
                                  log("File selected: ${pickedFile.name}");
                                  await _uploadFileHelper(
                                      pickedFile, null, fileBytes!);
                                } else {
                                  log('No file selected.');
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: AppColors.kprimarycolor,
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: const Icon(
                                  Icons.edit,
                                  size: 20,
                                  color: AppColors.kwhite,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ]));
  }

  Widget _buildEditClassNameField() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text('Class title (max.50 char)*', style: body2TextRegular),
          const Gap(10),
          CustomTextFormField(
            initialValue: _classModel!.title,
            hintText: 'e.g Art attack',
            maxLength: 50,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Class Title Required';
              }
              return null;
            },
            onChanged: (val) {
              _classModel!.title = val;
            },
          ),
          const Gap(20),
          Text('Description (max.500 char)*', style: body2TextRegular),
          const Gap(10),
          CustomTextFormField(
            initialValue: _classModel!.description,
            onChanged: (val) {
              _classModel!.description = val;
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Class Description Required';
              }
              return null;
            },
            hintText: "Type here",
            maxLines: 30,
            maxLength: 500,
          )
        ],
      ),
    );
  }

  Widget _buildEditClassAge() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Suitable age group*", style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: CustomDropdownFormField(
                  items: _ageOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age),
                    );
                  }).toList(),
                  value: _selectedMinAge,
                  hintText: 'Min.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMinAge = value;
                      _classModel!.minAge = int.parse(value!);
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Min Age Required';
                    }
                    return null;
                  },
                ),
              ),
              const Gap(10),
              Text(
                'To',
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CustomDropdownFormField(
                  items: _maxAgeOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age),
                    );
                  }).toList(),
                  value: _selectedMaxAge,
                  hintText: 'Max.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMaxAge = value;

                      if (_selectedMaxAge == "18+") {
                        _classModel!.maxAge = 19;
                        log("Selected max age: 19");
                      } else {
                        try {
                          int selectedMaxAge = int.parse(value!);
                          int selectedMinAge = _selectedMinAge == "18+"
                              ? 18
                              : int.parse(_selectedMinAge!);

                          if (selectedMaxAge <= selectedMinAge) {
                            CustomSnackBar.showError(
                              "Error",
                              "The maximum age should be greater than the minimum age.",
                            );
                          } else {
                            _classModel!.maxAge = selectedMaxAge;
                            log("Selected max age: ${_classModel!.maxAge}");
                          }
                        } catch (e) {
                          log("Error parsing age value: $e");
                          CustomSnackBar.showError(
                            "Error",
                            "Invalid age value.",
                          );
                        }
                      }
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Max Age Required';
                    } else if (_selectedMinAge == "18+" && value == "18+") {
                      return null;
                    } else if (value == "18+") {
                      return null;
                    } else {
                      try {
                        int selectedMaxAge = int.parse(value);
                        int selectedMinAge = _selectedMinAge == "18+"
                            ? 18
                            : int.parse(_selectedMinAge!);

                        if (selectedMaxAge <= selectedMinAge) {
                          return "The maximum age should be greater than the minimum age.";
                        }
                      } catch (e) {
                        return "Invalid age value.";
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditClassType() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Class type*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = true;
                      isOnlineSelected = false;
                      isOfflineOnlineSelected = false;
                      classType = "offline";
                      _classModel!.classType = classType;
                      selectedCity = "";
                      selectedAddress = "";
                      // classType = "Offline";
                      // eventController
                      //     .eventDetailsModel
                      //     .value
                      //     .classType = classType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isOfflineSelected
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isOfflineSelected
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text("Offline",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOfflineOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOfflineOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = false;
                      isOnlineSelected = true;
                      isOfflineOnlineSelected = false;
                      classType = "online";
                      _classModel!.classType = classType;

                      // classType = "Online";
                      // eventController
                      //     .eventDetailsModel
                      //     .value
                      //     .classType = classType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                        color: isOnlineSelected
                            ? AppColors.kwhite
                            : AppColors.scaffoldColor,
                        border: Border.all(
                          color: isOnlineSelected
                              ? AppColors.txtprimary
                              : AppColors.bordergrey,
                        )),
                    child: Center(
                      child: Text("Online",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = false;
                      isOnlineSelected = false;
                      isOfflineOnlineSelected = true;
                      classType = "offline + online";
                      _classModel!.classType = classType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isOfflineOnlineSelected
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                          color: isOfflineOnlineSelected
                              ? AppColors.txtprimary
                              : AppColors.bordergrey),
                    ),
                    child: Center(
                      child: Text("Offline + Online",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOfflineOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOfflineOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget _buildEditClassCity() {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         CustomDropdownUI(
  //           isValidatorReq: true,
  //           title: "City",
  //           items: classVM.serviceableCity.map((city) {
  //             return DropdownMenuItem(
  //               onTap: () {
  //                 _classModel!.city = city;
  //               },
  //               value: city,
  //               child: Text(
  //                 city,
  //                 style: body2TextBold,
  //               ),
  //             );
  //           }).toList(),
  //           value: selectedCity.isNotEmpty ? selectedCity : null,
  //           onChanged: (value) {
  //             setState(() {
  //               selectedCity = value.toString();
  //               selectedCity = "";
  //             });
  //           },
  //         ),
  //         // Text("City*", style: body2TextRegular),
  //         // const Gap(10),
  //         // CustomDropDown(
  //         //   items: classVM.serviceableCity,

  //         //   // hinttext: _classModel!.city,
  //         //   initialValue: _classModel!.city,
  //         //   onChanged: (value) {
  //         //     _classModel!.city = value!;
  //         //   },
  //         //   onSaved: (p0) {},
  //         //   // validator: (value) {
  //         //   //       if (value == null || value.isEmpty) {
  //         //   //         return 'City Required';
  //         //   //       }
  //         //   //       return null;
  //         //   //     },
  //         //   onTap: () {},
  //         // ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildEditClassAddress() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownUI(
            isValidatorReq: true,
            title: "Address",
            items: [
              ...businessController.userModel.value.location.map((location) {
                return DropdownMenuItem(
                  onTap: () {
                    var locationDetails = LocationDetails(
                        area: location.area,
                        city: location.city,
                        state: location.state,
                        address: location.address,
                        country: location.country,
                        latitude: double.parse(location.latitude),
                        pinCode: location.pinCode,
                        longitude: double.parse(location.longitude),
                        locationId: location.locationId,
                        subLocality: location.subLocality);
                    _classModel!.city = location.city;
                    _classModel!.locationDetails = locationDetails;
                    _classModel!.address = location.address;
                  },
                  value: location.address,
                  child: Text(
                    location.address.toString(),
                    style: body2TextBold,
                  ),
                );
              }).toList(),
              DropdownMenuItem(
                value: 'add_new_address',
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Add new address',
                      style: body2TextBold.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            value: selectedAddress.isNotEmpty ? selectedAddress : null,
            onChanged: (value) {
              if (value == 'add_new_address') {
                // Close dropdown first
                FocusScope.of(context).unfocus();

                // Navigate after a small delay to ensure dropdown closes
                Future.delayed(const Duration(milliseconds: 100), () {
                  locator<NavigationServices>()
                      .navigateTo(
                    addAddressPage,
                    arguments: const ProfileAddAddress(isEditing: false),
                  )
                      .then((result) async {
                    await businessController.businessProfileDetails();
                    businessController.userModel.refresh();
                    if (businessController
                        .userModel.value.location.isNotEmpty) {
                      // Select the last added address (assuming it's the newest)
                      var lastLocation =
                          businessController.userModel.value.location.last;
                      selectedAddress = lastLocation.address;
                      var locationDetails = LocationDetails(
                        area: lastLocation.area,
                        city: lastLocation.city,
                        state: lastLocation.state,
                        address: lastLocation.address,
                        country: lastLocation.country,
                        latitude: double.parse(lastLocation.latitude),
                        pinCode: lastLocation.pinCode,
                        longitude: double.parse(lastLocation.longitude),
                        locationId: lastLocation.locationId,
                        subLocality: lastLocation.subLocality,
                      );

                      _classModel!.city = lastLocation.city;
                      _classModel!.locationDetails = locationDetails;
                      _classModel!.address = lastLocation.address;
                    } else {
                      selectedAddress = '';
                    }
                    if (mounted) {
                      setState(() {});
                    }
                  });
                });
                return;
              }

              if (value == 'separator') {
                // Ignore separator selection
                return;
              }

              // Handle regular address selection
              var selectedLocation = businessController.userModel.value.location
                  .firstWhere((location) => location.address == value);

              var locationDetails = LocationDetails(
                  area: selectedLocation.area,
                  city: selectedLocation.city,
                  state: selectedLocation.state,
                  address: selectedLocation.address,
                  country: selectedLocation.country,
                  latitude: double.parse(selectedLocation.latitude),
                  pinCode: selectedLocation.pinCode,
                  longitude: double.parse(selectedLocation.longitude),
                  locationId: selectedLocation.locationId,
                  subLocality: selectedLocation.subLocality);

              _classModel!.city = selectedLocation.city;
              _classModel!.locationDetails = locationDetails;
              _classModel!.address = selectedLocation.address;

              setState(() {
                selectedAddress = value.toString();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEditClassSession() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Session type*", style: body2TextRegular),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isGroupSession = true;
                      is1Session = false;
                      isGroup1Session = false;
                      sessionType = "group";
                      _classModel!.sessionType = sessionType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isGroupSession
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isGroupSession
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Group",
                        style: body2TextSemiBold.copyWith(
                          color: isGroupSession
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isGroupSession = false;
                      is1Session = true;
                      isGroup1Session = false;
                      sessionType = "1:1";
                      _classModel!.sessionType = sessionType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      color: is1Session
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: is1Session
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "1:1",
                        style: body2TextSemiBold.copyWith(
                          color: is1Session
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isGroupSession = false;
                      is1Session = false;
                      isGroup1Session = true;
                      sessionType = "group + 1:1";
                      _classModel!.sessionType = sessionType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isGroup1Session
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isGroup1Session
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Group + 1:1",
                        style: body2TextSemiBold.copyWith(
                          color: isGroup1Session
                              ? AppColors.txtprimary
                              : AppColors.txtsecondary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditClassFees() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Class Fee*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isTicketPaid = true;
                      isTicketFree = false;
                      ticketType = "paid";
                      _classModel!.classFee = ticketType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(12),
                      ),
                      color: isTicketPaid
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketPaid
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Paid",
                        style: body2TextSemiBold.copyWith(
                            color: isTicketPaid
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isTicketPaid = false;
                      isTicketFree = true;
                      ticketType = "free";
                      _classModel!.classFee = ticketType;
                      if (isTicketFree) {
                        _classModel!.price = 0;
                      }
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isTicketFree
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketFree
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Free",
                        style: body2TextSemiBold.copyWith(
                            color: isTicketFree
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditClassPrice() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Amount", style: body2TextRegular),
          CustomTextFormField(
            initialValue: _classModel!.price.toString(),
            onChanged: (val) {
              int price = int.tryParse(val) ?? 0;
              _classModel!.price = price;
            },
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            maxLength: 8,
            hintText: "Enter amount",
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Amount Required';
              }
              return null;
            },
            isSuffixIcon: const Padding(
              padding: EdgeInsets.only(right: 8),
              child: Icon(Icons.currency_rupee,
                  size: 20, color: AppColors.txtsecondary),
            ),
          ),
        ],
      ),
    );
  }

  bool _isProcessing = false;

  void onSubmitOrSaveDraft(bool isSubmit) async {
    if (_isProcessing) return;
    _isProcessing = true;
    if (_formKey.currentState?.validate() ?? false) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return ConfirmPopup(
            dialogHeight: isSubmit ? 220 : 190,
            dialogWidth: 300,
            title: isSubmit ? 'Submit for review' : 'Save as draft',
            message: isSubmit
                ? 'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.'
                : 'Are you sure you want to save this as a draft?',
            onConfirm: () async {
              log(isSubmit ? "Submit for review" : "Save as draft");
              bool isSuccess = isDuplicateClass
                  ? await classVM.createClass(
                      createClassModel: _classModel!,
                      publish: isSubmit ? 1 : 0,
                      status: isSubmit ? "in-review" : "draft")
                  : await classVM.editClass(
                      publish: isSubmit ? 1 : 0,
                      isApproved: isSubmit ? 0 : 0,
                      editClassModel: _classModel!,
                      status: isSubmit ? "in-review" : "draft");
              if (isSuccess) {
                await Future.delayed(const Duration(milliseconds: 300));
                Navigator.pop(Get.context!);
                // Navigator.pop(Get.context!);
                CustomSnackBar.showInfo(
                  "Success",
                  isSubmit ? "Class submited for review." : "Saved as draft.",
                );
                locator<NavigationServices>().goBack();
                if (isDuplicateClass) {
                  locator<NavigationServices>().goBack();
                }
              } else {
                CustomSnackBar.showError(
                  "Failed",
                  isSubmit
                      ? "Failed to submit for review. Please try again.."
                      : "Failed to save as draft. Please try again.",
                );
              }
            },
            icon: SvgPicture.asset(
              isSubmit
                  ? 'assets/icons/Checks.svg'
                  : 'assets/icons/PencilLine.svg',
              height: 50,
              fit: BoxFit.fill,
            ),
            confirmText: isSubmit ? 'Submit' : 'Save as draft',
            cancelText: 'Cancel',
          );
        },
      );
    } else {
      CustomSnackBar.showError(
        "Error",
        "All field is required.",
      );
    }
    _isProcessing = false;
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(fileExtension)) {
      CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
      );
      return;
    }

    if (pickedFile.size > 1 * 1024 * 1024) {
      CustomSnackBar.showError(
        "Error",
        "File size exceeds 1 MB limit.",
      );
      return;
    }

    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "banner");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        imageUrl = newImageUrl;
        _classModel!.bannerUrl = newImageUrl;
      });
      log("_imageUrl set to: $newImageUrl");

      CustomSnackBar.showInfo(
        "Succes",
        "Class Banner uploaded successfully",
      );
    } else {
      log("unable to upload the image");
      CustomSnackBar.showError(
        "Failed",
        "Unable to upload class banner... try again later",
      );
    }
  }
}
