import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_cell.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

class ClassDetails extends StatefulWidget {
  final Map<String, int> arguments;
  const ClassDetails({super.key, required this.arguments});

  @override
  State<ClassDetails> createState() => _ClassDetailsState();
}

class _ClassDetailsState extends State<ClassDetails> {
  final ClassController classVM = Get.find<ClassController>();
  int id = 0;
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['classByID']!;
      });
      classVM.getClassDetailsData(id);
    });
  }

  @override
  void dispose() {
    classVM.resetClassDetailsModel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    log("class ID ${classVM.classDetailsModel.value.classId}");
    return Scaffold(
        backgroundColor: AppColors.kwhite,
        body: OrientationBuilder(builder: (context, orientation) {
          return _buildPage(orientation);
        }));
  }

  _buildPage(Orientation orientation) {
    double scrrenWidth = MediaQuery.of(context).size.width;

    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Obx(
            () => classVM.isClassDetailsLoading.value
                ? scrrenWidth <= 820
                    ? const Center(child: CircularProgressIndicator.adaptive())
                    : Container(
                        width: orientation == Orientation.portrait
                            ? Get.width
                            : Get.width * .65,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: AppColors.ktertiary.withOpacity(.3),
                              width: 1.0),
                        ),
                        child: const ClassDetailsShimmer())
                : Container(
                    width: orientation == Orientation.portrait
                        ? Get.width
                        : Get.width * .65,
                    padding: scrrenWidth <= 730
                        ? EdgeInsets.zero
                        : const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: AppColors.kgrey,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 0.0),
                          child: Row(
                            children: [
                              InkWell(
                                hoverColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                overlayColor: MaterialStateProperty.all(
                                    Colors.transparent),
                                onTap: () {
                                  locator<NavigationServices>().goBack();
                                },
                                child: SvgPicture.asset(
                                    "assets/svg/arrow-left.svg"),
                              ),
                              const Gap(16),
                              Text(
                                "Class details",
                                style: title2TextSemiBold,
                              ),
                              const Spacer(),
                              classVM.classDetailsModel.value.publish == 1
                                  ? const SizedBox()
                                  : scrrenWidth <= 820
                                      ? const SizedBox.shrink()
                                      : PrimaryButton(
                                          textColor:
                                              // businessController
                                              //             .userModel
                                              //             .value
                                              //             .kycDone ==
                                              //         0
                                              //     ? AppColors.ktertiary
                                              //     :
                                              AppColors.kwhite,
                                          backgroundColor:
                                              // businessController
                                              //             .userModel
                                              //             .value
                                              //             .kycDone ==
                                              //         0
                                              //     ? AppColors.scaffoldColor
                                              //     :
                                              AppColors.kprimarycolor,
                                          text: "Submit for review",
                                          onTap: () {
                                            // if (businessController
                                            //         .userModel.value.kycDone ==
                                            //     1) {
                                            showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return ConfirmPopup(
                                                  dialogHeight: 220,
                                                  dialogWidth: 300,
                                                  title: 'Submit for review',
                                                  message:
                                                      'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.',
                                                  onConfirm: () async {
                                                    log("Submit for review");
                                                    bool isSuccess =
                                                        await classVM.editClass(
                                                            publish: 1,
                                                            isApproved: 0,
                                                            editClassModel: classVM
                                                                .classDetailsModel
                                                                .value,
                                                            status:
                                                                "in-review");
                                                    if (isSuccess) {
                                                      await Future.delayed(
                                                          const Duration(
                                                              milliseconds:
                                                                  300));
                                                      Navigator.pop(
                                                          Get.context!);
                                                      locator<NavigationServices>()
                                                          .goBack();
                                                    } else {
                                                      CustomSnackBar.showError(
                                                          "Error",
                                                          'Failed to submit for review. Please try again.');
                                                    }
                                                  },
                                                  icon: SvgPicture.asset(
                                                    'assets/icons/Checks.svg',
                                                    height: 50,
                                                    fit: BoxFit.fill,
                                                  ),
                                                  confirmText: 'Submit',
                                                  cancelText: 'Cancel',
                                                );
                                              },
                                            );
                                            // }
                                          }),
                              const Gap(10),
                              Bounceable(
                                onTap: () {
                                  // if (businessController
                                  //         .userModel.value.kycDone ==
                                  //     1) {
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (BuildContext context) {
                                      return CustomDialog(
                                        onConfirmTxt: "Yes",
                                        onCancelText: "No",
                                        imageHeight: 48,
                                        title: 'Edit this Class?',
                                        content:
                                            'Editing a published class will send it for review again.\n\nWhile under review, the class will be temporarily removed from the app. Do you want to continue?',
                                        image: "assets/icons/PencilSimple.svg",
                                        onConfirm: () {
                                          Navigator.pop(context);
                                          locator<NavigationServices>()
                                              .navigateTo(editclassRoute,
                                                  arguments: <String, dynamic>{
                                                'classID': classVM
                                                    .classDetailsModel
                                                    .value
                                                    .classId,
                                                'class_details': classVM
                                                    .classDetailsModel.value,
                                                'page_mode': 'EDIT',
                                                "isDuplicateClass": false
                                              }).then((value) {
                                            classVM.getClassDetailsData(id);
                                          });
                                        },
                                        onCancel: () {
                                          Navigator.of(context).pop();
                                        },
                                      );
                                    },
                                  );
                                  // }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(7.0),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      border: Border.all(
                                          color: AppColors.ktertiary
                                              .withOpacity(.3),
                                          width: 1.0)),
                                  child: SvgPicture.asset(
                                      "assets/svg/PencilSimple.svg"),
                                ),
                              ),
                              const Gap(10),
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  child: PopupMenuButton<int>(
                                    useRootNavigator: false,
                                    color: AppColors.kwhite,
                                    surfaceTintColor: AppColors.kwhite,
                                    icon: Container(
                                      padding: const EdgeInsets.all(7.0),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: AppColors.ktertiary
                                                  .withOpacity(.3),
                                              width: 1.0)),
                                      child: SvgPicture.asset(
                                          "assets/svg/DotsThreeOutlineVertical.svg"),
                                    ),
                                    onSelected: (int result) {
                                      switch (result) {
                                        case 0:
                                          // Handle the duplicate action
                                          if (kDebugMode) {
                                            print("Duplicate selected");
                                          }
                                          // if (businessController
                                          //         .userModel.value.kycDone ==
                                          //     1) {
                                          showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) {
                                              return CustomDialog(
                                                onConfirmTxt: "Yes",
                                                onCancelText: "No",
                                                imageHeight: 48,
                                                title: 'Duplicate this Class?',
                                                content:
                                                    'Are you sure you want to duplicate this Class?',
                                                image: "assets/icons/Copy.svg",
                                                onConfirm: () {
                                                  Navigator.pop(context);
                                                  locator<NavigationServices>()
                                                      .navigateTo(
                                                          editclassRoute,
                                                          arguments: <String,
                                                              dynamic>{
                                                        'classID': classVM
                                                            .classDetailsModel
                                                            .value
                                                            .classId,
                                                        'class_details': classVM
                                                            .classDetailsModel
                                                            .value,
                                                        'page_mode': 'EDIT',
                                                        "isDuplicateClass": true
                                                      }).then((value) {
                                                    classVM.getClassDetailsData(
                                                        id);
                                                  });
                                                },
                                                onCancel: () {
                                                  Navigator.of(context).pop();
                                                },
                                              );
                                            },
                                          );
                                          // }

                                          break;
                                        case 1:
                                          if (kDebugMode) {
                                            log("Unpublish selected");
                                          }
                                          // if (businessController
                                          //         .userModel.value.kycDone ==
                                          //     1) {
                                          showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) {
                                              return CustomDialog(
                                                onConfirmTxt: "Yes",
                                                onCancelText: "No",
                                                imageHeight: 48,
                                                title: 'Unpublish this Class?',
                                                content:
                                                    'This class will be no longer visible to the users.',
                                                image:
                                                    "assets/icons/EyeSlash.svg",
                                                onConfirm: () async {
                                                  log("unpublish class");
                                                  bool isSuccess =
                                                      await classVM.editClass(
                                                          publish: 0,
                                                          isApproved: 0,
                                                          editClassModel: classVM
                                                              .classDetailsModel
                                                              .value,
                                                          status: "draft");
                                                  if (isSuccess) {
                                                    Navigator.pop(Get.context!);
                                                    locator<NavigationServices>()
                                                        .goBack();
                                                  } else {
                                                    CustomSnackBar.showError(
                                                        "Error",
                                                        'Failed to unpublish this class. Please try again.');
                                                  }
                                                },
                                                onCancel: () {
                                                  Navigator.of(context).pop();
                                                },
                                              );
                                            },
                                          );
                                          // }

                                          break;
                                        case 2:
                                          // if (businessController
                                          //         .userModel.value.kycDone ==
                                          //     1) {
                                          showDialog(
                                            context: context,
                                            barrierDismissible: false,
                                            builder: (BuildContext context) {
                                              return CustomDialog(
                                                onConfirmTxt: "Yes",
                                                onCancelText: "No",
                                                imageHeight: 48,
                                                title: 'Delete this Class?',
                                                content:
                                                    'Class will be deleted permanently for everyone.',
                                                image:
                                                    "assets/icons/Trash_red.svg",
                                                onConfirm: () async {
                                                  Navigator.pop(context);
                                                  classVM
                                                      .getClassDeleteData(
                                                          classVM
                                                              .classDetailsModel
                                                              .value
                                                              .classId)
                                                      .then((value) {
                                                    Future.delayed(
                                                        const Duration(
                                                            milliseconds: 300));
                                                    locator<NavigationServices>()
                                                        .goBack();
                                                    CustomSnackBar.showError(
                                                      'Success',
                                                      'Class is deleted permanently for everyone.',
                                                    );
                                                  });

                                                  log("class ID $id");
                                                },
                                                onCancel: () {
                                                  Navigator.of(context).pop();
                                                },
                                              );
                                            },
                                          );
                                          // }

                                          if (kDebugMode) {
                                            print("Delete selected");
                                          }
                                          break;
                                      }
                                    },
                                    itemBuilder: (BuildContext context) =>
                                        <PopupMenuEntry<int>>[
                                      PopupMenuItem<int>(
                                        value: 0,
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                                "assets/icons/Copy.svg"),
                                            const Gap(10),
                                            const Text('Duplicate'),
                                          ],
                                        ),
                                      ),
                                      if (classVM
                                              .classDetailsModel.value.status ==
                                          "published")
                                        PopupMenuItem<int>(
                                          value: 1,
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                  "assets/icons/EyeSlash.svg"),
                                              const Gap(10),
                                              const Text('Unpublish'),
                                            ],
                                          ),
                                        ),
                                      PopupMenuItem<int>(
                                        value: 2,
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                                "assets/icons/Trash.svg"),
                                            const Gap(10),
                                            const Text('Delete'),
                                          ],
                                        ),
                                      ),
                                    ],
                                    position: PopupMenuPosition.under,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Divider(
                          thickness: 1.0,
                          color: AppColors.kgrey,
                        ),
                        Column(
                          children: [
                            if (classVM.classDetailsModel.value.status ==
                                'rejected')
                              Column(
                                children: [
                                  Container(
                                    width: Get.width,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    margin: const EdgeInsets.all(8),
                                    decoration: const BoxDecoration(
                                      color: AppColors.lightPink,
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(12),
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            "Class submission has been rejected.",
                                            style: bodyTextBold),
                                        const Gap(5),
                                        Text(
                                            "Reason: ${classVM.classDetailsModel.value.reason}",
                                            style: body2TextRegular),
                                        const Gap(10),
                                        Row(
                                          children: [
                                            const Icon(
                                              Icons.info_outline,
                                              color: AppColors.txtsecondary,
                                            ),
                                            const Gap(6),
                                            Expanded(
                                              child: Text(
                                                "This banner will disappear after the event/ class is resubmitted for review.",
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 3,
                                                style:
                                                    body3TextRegular.copyWith(
                                                        color: AppColors
                                                            .txtsecondary),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            const Gap(20),
                            scrrenWidth <= 820
                                ? _buildMobileView()
                                : _buildDesktopView()
                          ],
                        ),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  _buildDesktopView() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      classVM.classDetailsModel.value.title,
                      style: heading2TextRegular,
                      maxLines: 2,
                    ),
                    const Gap(20),
                    Row(
                      children: [
                        ClassDetailsRowChild(
                          iconPath: "assets/svg/MapPin.svg",
                          textTitle: classVM
                              .classDetailsModel.value.classType.capitalizeFirst
                              .toString(),
                        ),
                        const Gap(15),
                        ClassDetailsRowChild(
                          iconPath: "assets/svg/Baby.svg",
                          textTitle:
                              "${classVM.classDetailsModel.value.minAge}-${classVM.classDetailsModel.value.maxAge.toString().replaceAll("19", "18+")} years",
                        ),
                        const Gap(15),
                        ClassDetailsRowChild(
                          iconPath: "assets/svg/group.svg",
                          textTitle: classVM.classDetailsModel.value.sessionType
                              .capitalizeFirst
                              .toString(),
                        ),
                      ],
                    ),
                    const Gap(25),
                    Text(
                      "About Class",
                      style: title3TextSemiBold,
                    ),
                    const Gap(6),
                    Text(
                      classVM.classDetailsModel.value.description,
                      style: bodyTextRegular.copyWith(
                          color: AppColors.txtsecondary),
                      maxLines: 15,
                      overflow: TextOverflow.ellipsis,
                    )
                  ],
                ),
              ),
              const Gap(50),
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: classVM.classDetailsModel.value.bannerUrl,
                    width: Get.width,
                    height: Get.height * .4,
                    fit: BoxFit.fill,
                    errorWidget: (context, url, error) => Image.asset(
                        "assets/png/class_list_empty.png",
                        height: 340,
                        width: 340,
                        fit: BoxFit.cover),
                    placeholder: (context, url) => Image.asset(
                        "assets/png/class_list_empty.png",
                        height: 340,
                        width: 340,
                        fit: BoxFit.cover),
                  ),
                ),
              ),
            ],
          ),
        ),
        const Gap(10),
        const Divider(
          thickness: 1.0,
          color: AppColors.kgrey,
        ),
        ClassCategoryRow(
          title: 'Category',
          content: classVM.classDetailsModel.value.category,
        ),
        const Divider(
          thickness: 1.0,
          color: AppColors.kgrey,
        ),
        ClassCategoryRow(
          title: 'Sub-Category',
          content: classVM.classDetailsModel.value.subCategory,
        ),
        const Divider(
          thickness: 1.0,
          color: AppColors.kgrey,
        ),
        ClassCategoryRow(
            title: 'Class fee',
            content: classVM.classDetailsModel.value.price == 0
                ? "Free"
                : "₹${classVM.classDetailsModel.value.price} ${classVM.classDetailsModel.value.price == 1 ? '' : 'onwards'}"),
        classVM.classDetailsModel.value.ctamobile.isEmpty
            ? const SizedBox.shrink()
            : Column(
                children: [
                  const Divider(
                    thickness: 1.0,
                    color: AppColors.kgrey,
                  ),
                  InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    overlayColor: MaterialStateProperty.all(Colors.transparent),
                    onTap: () => _launchPhoneNumber(
                        classVM.classDetailsModel.value.ctamobile),
                    child: ClassCategoryRow(
                      title: 'Phone',
                      content:
                          "+91 ${classVM.classDetailsModel.value.ctamobile}",
                    ),
                  ),
                ],
              ),
        classVM.classDetailsModel.value.classType.toLowerCase() == 'online'
            ? const SizedBox.shrink()
            : Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(
                    thickness: 1.0,
                    color: AppColors.kgrey,
                  ),
                  ClassCategoryRow(
                    title: 'City',
                    content: classVM.classDetailsModel.value.city,
                  ),
                  const Divider(
                    thickness: 1.0,
                    color: AppColors.kgrey,
                  ),
                  ClassCategoryRow(
                    title: 'Address',
                    content: classVM
                        .classDetailsModel.value.locationDetails.address
                        .replaceFirst(RegExp(r'^.*?,\s*'), ''),
                  ),
                ],
              ),
        const Gap(20),
      ],
    );
  }

  _buildMobileView() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              classVM.classDetailsModel.value.bannerUrl,
              width: Get.width,
              height: Get.height * .25,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) {
                  return child;
                }
                return Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Image.asset("assets/png/class_list_empty.png",
                    width: Get.width,
                    height: Get.height * .25,
                    fit: BoxFit.cover);
              },
            ),
          ),
        ),
        const Gap(10),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    classVM.classDetailsModel.value.title,
                    style: heading2TextMedium,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  kMinHeight,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      ClassDetailsRowChild(
                        iconPath: "assets/svg/MapPin.svg",
                        textTitle: classVM.classDetailsModel.value.classType,
                      ),
                      const Gap(10),
                      ClassDetailsRowChild(
                        iconPath: "assets/svg/Baby.svg",
                        textTitle:
                            "${classVM.classDetailsModel.value.minAge}-${classVM.classDetailsModel.value.maxAge.toString().replaceAll("19", "18+")} years",
                      ),
                      const Gap(10),
                      Expanded(
                        child: ClassDetailsRowChild(
                          iconPath: "assets/svg/group.svg",
                          textTitle:
                              classVM.classDetailsModel.value.sessionType,
                        ),
                      ),
                      const Gap(10),
                    ],
                  ),
                  const Gap(25),
                  Text(
                    "About Class",
                    style: title3TextSemiBold,
                    overflow: TextOverflow.ellipsis,
                  ),
                  kSmHeight,
                  Text(
                    classVM.classDetailsModel.value.description,
                    overflow: TextOverflow.ellipsis,
                    style: body2TextMedium.copyWith(color: AppColors.secondary),
                    maxLines: 12,
                  ),
                ],
              ),
            ),
            const Gap(20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: EventColumChild(
                title: 'Category',
                subTitle: classVM.classDetailsModel.value.category,
              ),
            ),
            const Divider(
              thickness: 1.0,
              color: AppColors.kgrey,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: EventColumChild(
                title: 'Sub-Category',
                subTitle: classVM.classDetailsModel.value.subCategory,
              ),
            ),
            const Divider(
              thickness: 1.0,
              color: AppColors.kgrey,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: EventColumChild(
                  title: 'Class fee',
                  subTitle: classVM.classDetailsModel.value.price == 0
                      ? "Free"
                      : "₹${classVM.classDetailsModel.value.price} ${classVM.classDetailsModel.value.price == 1 ? '' : 'onwards'}"),
            ),
            classVM.classDetailsModel.value.ctamobile.isEmpty
                ? const SizedBox.shrink()
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: InkWell(
                          hoverColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          overlayColor:
                              MaterialStateProperty.all(Colors.transparent),
                          onTap: () => _launchPhoneNumber(
                              classVM.classDetailsModel.value.ctamobile),
                          child: EventColumChild(
                            title: 'Phone',
                            subTitle:
                                "+91 ${classVM.classDetailsModel.value.ctamobile}",
                          ),
                        ),
                      ),
                    ],
                  ),
            classVM.classDetailsModel.value.classType.toLowerCase() == 'online'
                ? const SizedBox.shrink()
                : Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: EventColumChild(
                          title: 'City',
                          subTitle: classVM.classDetailsModel.value.city,
                        ),
                      ),
                      const Divider(
                        thickness: 1.0,
                        color: AppColors.kgrey,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: EventColumChild(
                          title: "Address",
                          subTitle: classVM
                              .classDetailsModel.value.locationDetails.address
                              .replaceFirst(RegExp(r'^.*?,\s*'), ''),
                        ),
                      )
                    ],
                  ),
          ],
        ),
        const Gap(15),
        classVM.classDetailsModel.value.publish == 1
            ? const SizedBox()
            : screenWidth >= 820
                ? const SizedBox.shrink()
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: PrimaryButton(
                        textColor:
                            // businessController.userModel.value.kycDone == 0
                            //     ? AppColors.ktertiary
                            //     :
                            AppColors.kwhite,
                        backgroundColor:
                            // businessController.userModel.value.kycDone == 0
                            //     ? AppColors.scaffoldColor
                            //     :
                            AppColors.kprimarycolor,
                        text: "Submit for review",
                        onTap: () {
                          // if (businessController.userModel.value.kycDone == 1) {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return ConfirmPopup(
                                dialogHeight: 220,
                                dialogWidth: 300,
                                title: 'Submit for review',
                                message:
                                    'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.',
                                onConfirm: () async {
                                  log("Submit for review");
                                  bool isSuccess = await classVM.editClass(
                                      publish: 1,
                                      isApproved: 0,
                                      editClassModel:
                                          classVM.classDetailsModel.value,
                                      status: "in-review");
                                  if (isSuccess) {
                                    await Future.delayed(
                                        const Duration(milliseconds: 300));
                                    Navigator.pop(Get.context!);
                                    locator<NavigationServices>().goBack();
                                  } else {
                                    CustomSnackBar.showError("Error",
                                        'Failed to submit for review. Please try again.');
                                  }
                                },
                                icon: SvgPicture.asset(
                                  'assets/icons/Checks.svg',
                                  height: 50,
                                  fit: BoxFit.fill,
                                ),
                                confirmText: 'Submit',
                                cancelText: 'Cancel',
                              );
                            },
                          );
                        }
                        // }
                        ),
                  ),
        const Gap(25),
      ],
    );
  }

  Future<void> _launchPhoneNumber(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      if (kDebugMode) {
        log("Could not launch $phoneNumber");
      }
    }
  }
}

class ClassRowChild extends StatelessWidget {
  const ClassRowChild(
      {super.key, required this.iconPath, required this.textTitle});
  final String iconPath;
  final String textTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          iconPath,
          height: 18,
        ),
        const Gap(5),
        Flexible(
          child: Text(textTitle,
              style: body2TextMedium.copyWith(color: AppColors.secondary),
              overflow: TextOverflow.ellipsis,
              maxLines: 1),
        )
      ],
    );
  }
}

class ClassDetailsRowChild extends StatelessWidget {
  const ClassDetailsRowChild(
      {super.key, required this.iconPath, required this.textTitle});
  final String iconPath;
  final String textTitle;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          iconPath,
          height: 24,
          width: 24,
        ),
        const Gap(5),
        Flexible(
          child: Text(textTitle,
              style: bodyTextRegular.copyWith(color: AppColors.txtsecondary),
              overflow: TextOverflow.ellipsis,
              maxLines: 1),
        )
      ],
    );
  }
}

class ClassCategoryRow extends StatelessWidget {
  const ClassCategoryRow(
      {super.key, required this.title, required this.content});
  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    double scrrenWidth = MediaQuery.of(context).size.width;
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: scrrenWidth <= 780 ? 0.0 : 20.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: scrrenWidth <= 780
            ? MainAxisAlignment.spaceBetween
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          scrrenWidth <= 780
              ? Text(title,
                  style:
                      bodyTextRegular.copyWith(color: AppColors.txtsecondary))
              : SizedBox(
                  width: Get.width * .12,
                  child: Text(title,
                      style: bodyTextRegular.copyWith(
                          color: AppColors.txtsecondary)),
                ),
          if (scrrenWidth <= 780) const Gap(50),
          scrrenWidth <= 780
              ? Text(
                  content,
                  style: title3TextMedium,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                )
              : Expanded(
                  child: Text(
                    content,
                    style: title3TextMedium,
                  ),
                ),
        ],
      ),
    );
  }
}

class ClassDetailsShimmer extends StatelessWidget {
  final bool isClass;
  const ClassDetailsShimmer({super.key, this.isClass = true});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12)),
                ),
                const Gap(16),
                Container(
                  width: 150,
                  height: 24,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12)),
                ),
                const Spacer(),
                // Container(
                //   width: 120,
                //   height: 40,
                //   decoration: BoxDecoration(
                //       color: AppColors.kwhite,
                //       borderRadius: BorderRadius.circular(12)),
                // ),
                const Gap(10),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12)),
                ),
                const Gap(10),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12)),
                ),
              ],
            ),
            const Divider(color: AppColors.kgrey),
            const Gap(20),
            // Main content
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isClass == false)
                  Container(
                    width: 370,
                    height: 350,
                    decoration: BoxDecoration(
                        color: AppColors.kwhite,
                        borderRadius: BorderRadius.circular(12)),
                  ),
                const Gap(20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 30,
                        decoration: BoxDecoration(
                            color: AppColors.kwhite,
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      const Gap(10),
                      Row(
                        children: [
                          Container(
                            width: 100,
                            height: 20,
                            decoration: BoxDecoration(
                                color: AppColors.kwhite,
                                borderRadius: BorderRadius.circular(12)),
                          ),
                          const Gap(15),
                          Container(
                            width: 100,
                            height: 20,
                            decoration: BoxDecoration(
                                color: AppColors.kwhite,
                                borderRadius: BorderRadius.circular(12)),
                          ),
                          const Gap(15),
                          Container(
                            width: 100,
                            height: 20,
                            decoration: BoxDecoration(
                                color: AppColors.kwhite,
                                borderRadius: BorderRadius.circular(12)),
                          ),
                        ],
                      ),
                      const Gap(20),
                      Container(
                        width: 120,
                        height: 24,
                        decoration: BoxDecoration(
                            color: AppColors.kwhite,
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      const Gap(10),
                      Container(
                        width: double.infinity,
                        height: 80,
                        decoration: BoxDecoration(
                            color: AppColors.kwhite,
                            borderRadius: BorderRadius.circular(12)),
                      ),
                    ],
                  ),
                ),
                const Gap(30),
                if (isClass)
                  Container(
                    width: 300,
                    height: 300,
                    decoration: BoxDecoration(
                        color: AppColors.kwhite,
                        borderRadius: BorderRadius.circular(12)),
                  ),
              ],
            ),
            const Gap(20),
            const Divider(color: AppColors.kgrey),
            // Additional details
            for (int i = 0; i < 5; i++) ...[
              const Gap(10),
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 20,
                    decoration: BoxDecoration(
                        color: AppColors.kwhite,
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  const Gap(50),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      height: 20,
                      decoration: BoxDecoration(
                          color: AppColors.kwhite,
                          borderRadius: BorderRadius.circular(12)),
                    ),
                  ),
                ],
              ),
              const Gap(10),
              const Divider(color: AppColors.kgrey),
            ],
          ],
        ),
      ),
    );
  }
}
