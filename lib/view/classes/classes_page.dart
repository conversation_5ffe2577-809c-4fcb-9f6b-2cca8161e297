import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_cell.dart';
import 'package:parenthing_dashboard/view/common_widgets/animation.dart';
import 'package:parenthing_dashboard/view/common_widgets/empty_case.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/event/widgets/option_container.dart';

class ClassesPage extends StatefulWidget {
  final String? initialTab;

  const ClassesPage({super.key, this.initialTab});

  @override
  State<ClassesPage> createState() => _ClassesPageState();
}

class _ClassesPageState extends State<ClassesPage> {
  RxBool isPublished = true.obs;
  RxBool isDraft = false.obs;
  final ClassController classVM = Get.find<ClassController>();
  RxBool isPast = false.obs;
  RxBool isContentSelected = true.obs;
  // with SingleTickerProviderStateMixin {
  // late TabController _tabController;
  //bool _isKycSubmitted = false;

  @override
  void initState() {
    super.initState();
    _setInitialTab();
    _fetchClassesBasedOnState();
    // _tabController = TabController(length: 3, vsync: this);
  }

  void _setInitialTab() {
    if (widget.initialTab != null) {
      switch (widget.initialTab) {
        case 'draft':
          isPublished.value = false;
          isDraft.value = true;
          isPast.value = false;
          break;
        case 'past':
          isPublished.value = false;
          isDraft.value = false;
          isPast.value = true;
          break;
        case 'published':
        default:
          isPublished.value = true;
          isDraft.value = false;
          isPast.value = false;
          break;
      }
    }
  }

  void _fetchClassesBasedOnState() {
    Future.microtask(() {
      classVM.classList.clear();
      if (isPublished.value) {
        classVM.getAllClassList("published");
      } else if (isDraft.value) {
        classVM.getAllClassList("draft");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              Container(
                width: Get.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      12.0), // Adjust border radius as needed
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Gap(16),
                    // Obx(
                    //   () => businessController.showKycBanner.value
                    //       ? Column(
                    //           crossAxisAlignment: CrossAxisAlignment.start,
                    //           children: [
                    //             KycBannerWrapper(
                    //               type: businessController
                    //                   .userModel.value.kycStatus,
                    //               onDismissTap: () {
                    //                 businessController.dismissKycBanner();
                    //               },
                    //             ),
                    //             const Gap(20),
                    //             Divider(
                    //               thickness: 1.0,
                    //               color: AppColors.ktertiary.withOpacity(
                    //                   .3), // Set the color of the divider
                    //             ),
                    //           ],
                    //         )
                    //       : const SizedBox.shrink(),
                    // ),
                    const Gap(15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("My Classes", style: heading2TextRegular),
                        PrimaryButton(
                          text: '+ Create class',
                          onTap: () {
                            locator<NavigationServices>()
                                .navigateTo(createclassPageRoute)
                                .then((value) {
                              if (value != null && value is String) {
                                setState(() {
                                  switch (value) {
                                    case 'draft':
                                      isPublished.value = false;
                                      isDraft.value = true;
                                      isPast.value = false;
                                      break;
                                    case 'published':
                                      isPublished.value = true;
                                      isDraft.value = false;
                                      isPast.value = false;
                                      break;
                                    default:
                                      break;
                                  }
                                });
                              }
                              _fetchClassesBasedOnState();
                            });
                          },
                        ),
                      ],
                    ),
                    // Obx(
                    //   () => Row(
                    //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       Text("My Classes", style: heading2TextRegular),
                    //       MouseRegion(
                    //         child:  businessController.userModel.value.kycDone == 0 ? Tooltip(  message:
                    //                     "Complete your eKYC to create and publish class.",
                    //                 child: PrimaryButton(
                    //                   textColor: AppColors.ktertiary,
                    //                   backgroundColor: AppColors.scaffoldColor,
                    //                   text: '+ Create class',
                    //                   onTap: () {},
                    //                 ),) :
                    //         PrimaryButton(
                    //           text: '+ Create class',

                    //           onTap: () {

                    //               locator<NavigationServices>()
                    //                   .navigateTo(createclassPageRoute)
                    //                   .then((value) {
                    //                 _fetchClassesBasedOnState();
                    //               });

                    //           },
                    //         ),
                    //       )
                    //     ],
                    //   ),
                    // ),
                    const Divider(
                      thickness: 1.0,
                      color: AppColors.kgrey,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Container(
                        height: 46,
                        width: screenWidth >= 1025
                            ? screenWidth * .25
                            : screenWidth,
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 4),
                        decoration: BoxDecoration(
                            color: AppColors.scaffoldColor,
                            borderRadius: BorderRadius.circular(50)),
                        child: Row(
                          children: [
                            Obx(
                              () => OptionContainer(
                                text: 'Published',
                                isSelected: isPublished.value,
                                onTap: () {
                                  // classVM.getAllClassList("published");
                                  setState(() {
                                    isPublished.value = true;
                                    isDraft.value = false;
                                    isPast.value = false;
                                  });
                                  _fetchClassesBasedOnState();
                                },
                              ),
                            ),
                            Obx(
                              () => OptionContainer(
                                text: 'Draft',
                                isSelected: isDraft.value,
                                onTap: () {
                                  // classVM.getAllClassList("draft");
                                  setState(() {
                                    isPublished.value = false;
                                    isDraft.value = true;
                                    isPast.value = false;
                                  });
                                  _fetchClassesBasedOnState();
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    isPublished.value
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 10),
                            child: publishedWidget(),
                          )
                        : Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 10),
                            child: draftWidget(),
                          ),
                    const Gap(20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  publishedWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => classVM.isClassListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : classVM.classList.isEmpty
              ? SizedBox(
                  width: Get.width,
                  child: const EmptyCaseContainer(
                    type: "class_published",
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const ScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: screenWidth <= 480
                        ? 1
                        : screenWidth <= 1024
                            ? 2
                            : 3,
                    mainAxisExtent: 190,
                    crossAxisSpacing: 10.0,
                    mainAxisSpacing: 10.0,
                  ),
                  itemCount: classVM.classList.length,
                  itemBuilder: (context, index) {
                    return GridAnimationWidget(
                      columnCount: screenWidth <= 480
                          ? 1
                          : screenWidth <= 1024
                              ? 2
                              : 3,
                      index: index,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: ClassCell(
                          classModelObject: classVM.classList[index],
                          currentTab: "published",
                          classVM: classVM,
                        ),
                      ),
                    );
                  },
                ),
    );
  }

  draftWidget() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => classVM.isClassListLoading.value
          ? const Center(
              child: CircularProgressIndicator.adaptive(),
            )
          : classVM.classList.isEmpty
              ? SizedBox(
                  width: Get.width,
                  child: const EmptyCaseContainer(
                    type: "class_published",
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const ScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: screenWidth <= 480
                        ? 1
                        : screenWidth <= 1024
                            ? 2
                            : 3,
                    mainAxisExtent: 200,
                    crossAxisSpacing: 10.0, // Adjusts spacing between columns
                    mainAxisSpacing: 10.0, // Adjusts spacing between rows
                  ),
                  itemCount: classVM.classList.length,
                  itemBuilder: (context, index) {
                    return GridAnimationWidget(
                      columnCount: screenWidth <= 480
                          ? 1
                          : screenWidth <= 1024
                              ? 2
                              : 3,
                      index: index,
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: ClassCell(
                          classModelObject: classVM.classList[index],
                          currentTab: "draft",
                          classVM: classVM,
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
