import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/sidebar_button.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/router.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/view/loginPage/loginpage.dart';

import '../../routing/navigation_services.dart';

class MobileViewPage extends StatefulWidget {
  const MobileViewPage({super.key});

  @override
  State<MobileViewPage> createState() => _MobileViewPageState();
}

class _MobileViewPageState extends State<MobileViewPage> {
final BusinessController businessController = Get.find<BusinessController>();



  @override
  void initState() {
    super.initState();
    selectedBusinessPageNotifier.addListener(_updateSelectedPage);
  }

  @override
  void dispose() {
    selectedBusinessPageNotifier.removeListener(_updateSelectedPage);
    super.dispose();
  }

  void _updateSelectedPage() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      appBar: AppBar(
        backgroundColor: AppColors.kwhite,
        surfaceTintColor: AppColors.kwhite,
        leading: Builder(
          builder: (context) {
            return IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            );
          },
        ),
        title: Image.asset("assets/svg/mobile_view_logo.png",
            width: 150, fit: BoxFit.contain),
        // actions:  [
        //   Padding(
        //     padding: const EdgeInsets.only(right: 20 ),
        //     child: CircleAvatar(
        //       minRadius: 20,
        //       maxRadius: 20,
        //       backgroundColor: AppColors.kprimarycolor.withOpacity(.7),
        //     ),
        //   )
        // ],
      ),
      body: Navigator(
        key: locator<NavigationServices>().navigatorKey,
        onGenerateRoute: genarateRoute,
        // initialRoute: homeRoute,
        initialRoute: profileRoute,
      ),
      drawer: Obx(
        () => Drawer(
          backgroundColor: AppColors.kwhite,
          // shadowColor: AppColors.backcolor,
          // surfaceTintColor: AppColors.kwhite,
          child: Column(
            children: [
              UserAccountsDrawerHeader(
                accountName: Text(
                  businessController.userName.value,
                ),
                accountEmail: Text(businessController.userMobile.value),
                currentAccountPicture: CircleAvatar(
                  maxRadius: 25,
                  minRadius: 25,
                  backgroundImage:
                      NetworkImage(businessController.userProfilePic.value),
                ),
                decoration: BoxDecoration(
                    color: AppColors.kprimarycolor.withOpacity(0.8)),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    kSmHeight,
                    SideBardButton(
                      buttonText: 'Notifications',
                      buttonIcon: 'assets/svg/Bell.svg',
                      selectedIcon: "assets/svg/Bell.svg",
                      isSelected:
                          selectedBusinessPageNotifier.value == "Notifications",
                      onClick: () {
                        locator<NavigationServices>()
                            .navigateTo(businessNotificationsPage);
                        selectedBusinessPageNotifier.value = "Notifications";
                        _updateSelectedPage();
                        Navigator.pop(context);
                      },
                    ),
                    const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Divider(
                        height: 1.0,
                        color: AppColors.detailgrey,
                      ),
                    ),
                    // SideBardButton(
                    //   buttonText: 'Home',
                    //   buttonIcon: 'assets/icons/Vector.svg',
                    //   selectedIcon: "assets/icons/admin_home_Active.svg",
                    //   isSelected:
                    //       selectedBusinessPageNotifier.value == "Home",
                    //   onClick: () {
                    //     _updateSelectedPage();
                    //     locator<NavigationServices>().navigateTo(homeRoute);
                    //     selectedBusinessPageNotifier.value = "Home";
                    //      Navigator.pop(context);
                    //   },
                    // ),
                    Center(
                      child: SideBardButton(
                        buttonText: 'Events',
                        buttonIcon: 'assets/icons/Event.svg',
                        selectedIcon: "assets/icons/Event__Active.svg",
                        isSelected:
                            selectedBusinessPageNotifier.value == "Events",
                        onClick: () {
                          // _updateSelectedPage();
                          selectedBusinessPageNotifier.value = "Events";
                          locator<NavigationServices>().navigateTo(eventRoute);
                          _updateSelectedPage();
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    Center(
                      child: SideBardButton(
                        buttonText: 'Classes',
                        buttonIcon: 'assets/icons/Classes.svg',
                        selectedIcon: "assets/icons/Classes_Active.svg",
                        isSelected:
                            selectedBusinessPageNotifier.value == "Classes",
                        onClick: () {
                          selectedBusinessPageNotifier.value = "Classes";
                          locator<NavigationServices>()
                              .navigateTo(classesRoute);
                          _updateSelectedPage();
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    Center(
                      child: SideBardButton(
                        buttonText: 'Profile',
                        buttonIcon: 'assets/icons/User.svg',
                        selectedIcon: "assets/icons/User_Active.svg",
                        isSelected:
                            selectedBusinessPageNotifier.value == "Profile",
                        onClick: () {
                          selectedBusinessPageNotifier.value = 'Profile';
                          locator<NavigationServices>()
                              .navigateTo(profileRoute);
                          _updateSelectedPage();
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    Center(
                      child: SideBardButton(
                        buttonText: 'Help',
                        buttonIcon: 'assets/icons/business_help_active.svg',
                        selectedIcon: "assets/icons/business_help.svg",
                        isSelected:
                            selectedBusinessPageNotifier.value == "Help",
                        onClick: () {
                          selectedBusinessPageNotifier.value = 'Help';
                          locator<NavigationServices>().navigateTo(helpRoute);
                          _updateSelectedPage();
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(children: [
                  const Divider(
                    height: 1.0,
                    color: AppColors.detailgrey,
                  ),
                  kSmHeight,
                  SideBardButton(
                    buttonText: 'Logout',
                    buttonIcon: 'assets/icons/logout.svg',
                    isSelected: selectedBusinessPageNotifier.value == 'Logout',
                    selectedIcon: "",
                    onClick: () {
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return CustomDialog(
                            onConfirmTxt: "Log out",
                            onCancelText: "Cancel",
                            title: 'Log out?',
                            content:
                                'You will be returned to the login screen.',
                            image: "assets/icons/SignOut.svg",
                            onConfirm: () {
                              Navigator.pop(context);
                              storage.erase();
                              Navigator.pushAndRemoveUntil(
                                Get.context!,
                                MaterialPageRoute(
                                    builder: (context) => const LoginPage()),
                                (Route<dynamic> route) => false,
                              );
                            },
                            onCancel: () {
                              Navigator.of(context).pop();
                            },
                          );
                        },
                      );
                    },
                  ),
                ]),
              ),
              kSmHeight,
            ],
          ),
        ),
      ),
    );
  }
}
