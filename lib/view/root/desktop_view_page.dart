import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/router.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/drawer.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/header.dart';

class RootDashboardPage extends StatefulWidget {
  const RootDashboardPage({super.key});

  @override
  State<RootDashboardPage> createState() => _RootDashboardPageState();
}

class _RootDashboardPageState extends State<RootDashboardPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      endDrawer: SizedBox(
        height: MediaQuery.of(context).size.height,
        width: 500,
      ),
      body: Column(
        // Wrap the existing structure in a Column to accommodate the header
        children: [
          const HeaderWidget(), // Add the header widget here
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const DrawerPage(),
                Expanded(
                  child: Navigator(
                    key: locator<NavigationServices>().navigator<PERSON><PERSON>,
                    initialRoute: profileRoute,
                    onGenerateRoute: genarateRoute,
                    // initialRoute: homeRoute,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
