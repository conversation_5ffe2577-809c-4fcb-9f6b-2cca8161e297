import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class EmptyCaseContainer extends StatelessWidget {
  const EmptyCaseContainer(
      {super.key, this.type = 'empty', this.title = 'Data Not Found'});
  final String type;
  final String title;

  @override
  Widget build(BuildContext context) {
    // code to convert this picture based on type conditions switch case
    switch (type) {
      case 'event_published':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                'assets/png/event_empty.png',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No events created yet",
                style: body2TextBold,
              ),
              Text(
                "Use the button above to create and publish your first event.",
                style: body2TextRegular,
              ),
              const Gap(10),
              // SizedBox(
              //   width: 160,
              //   child: PrimaryButton(
              //     text: '+ Create event',
              //     onTap: () {
              //       log("+ Create event");
              //       locator<NavigationServices>()
              //           .navigateTo(createEventPage)
              //           .then((value) {
              //         eventController.getAllEventList("published");
              //       });
              //     },
              //   ),
              // MouseRegion(
              //   child: businessController.userModel.value.kycDone == 0
              //       ? Tooltip(
              //           message:
              //               "Complete your eKYC to create and publish events.",
              //           child: PrimaryButton(
              //             textColor: AppColors.ktertiary,
              //             backgroundColor: AppColors.scaffoldColor,
              //             text: '+ Create event',
              //             onTap: () {},
              //           ),
              //         )
              //       : PrimaryButton(
              //           text: '+ Create event',
              //           onTap: () {

              //               log("+ Create event");
              //               locator<NavigationServices>()
              //                   .navigateTo(createEventPage)
              //                   .then((value) {
              //                 eventController.getAllEventList("published");
              //               });

              //           },
              //         ),
              // ),
              // )
            ],
          ),
        );

      case 'event_details':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/png/event_empty.png',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No events created yet",
                style: body2TextMedium,
              ),
              Text(
                "Use the button above to create and publish your first event.",
                style: body2TextRegular,
              ),
              // SizedBox(
              //     width: 160,
              //     child: PrimaryButton(
              //         text: "+ Create event",
              //         onTap: () {
              //           locator<NavigationServices>()
              //               .navigateTo(
              //             createEventPage,
              //           )
              //               .then((value) {
              //             eventController.getAllEventList("published");
              //           });
              //         }))
            ],
          ),
        );

      case 'class_published':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 30.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/svg/Classes.svg',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No Class created yet",
                style: bodyTextBold,
              ),
              const Gap(10),
              Text(
                "Use the button above to create and publish your first event.",
                style: body2TextRegular,
                textAlign: TextAlign.center,
              ),
              const Gap(20),
              // SizedBox(
              //     width: 160,
              //     child: PrimaryButton(
              //       text: '+ Create class',
              //       textColor:
              //           // businessController.userModel.value.kycDone == 0
              //           //     ? AppColors.ktertiary
              //           //     :
              //           AppColors.kwhite,
              //       backgroundColor:
              //           // businessController.userModel.value.kycDone == 0
              //           //     ? AppColors.scaffoldColor
              //           //     :
              //           AppColors.kprimarycolor,
              //       onTap: () {
              //         locator<NavigationServices>()
              //             .navigateTo(createclassPageRoute)
              //             .then((value) {
              //           classVM.getAllClassList("published");
              //         });
              //         // if (businessController.userModel.value.kycDone == 1) {
              //         //   locator<NavigationServices>()
              //         //       .navigateTo(createclassPageRoute)
              //         //       .then((value) {
              //         //     classVM.getAllClassList("published");
              //         //   });
              //         // }
              //       },
              //     ))
            ],
          ),
        );

      case 'class_details':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/svg/Classes.svg',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No class created yet",
                style: body2TextMedium,
              ),
              const Gap(10),
              Text(
                "Use the button above to create and publish your first class.",
                textAlign: TextAlign.center,
                style: body2TextRegular,
              ),
              // SizedBox(
              //   width: 160  ,

              //   child:
              //      PrimaryButton(

              //             text: '+ Create class',
              //             textColor: businessController.userModel.value.kycDone == 0 ? AppColors.ktertiary : AppColors.kwhite ,
              //           backgroundColor: businessController.userModel.value.kycDone == 0 ?  AppColors.scaffoldColor : AppColors.kprimarycolor,
              //             onTap: () {
              //               if (businessController.userModel.value.kycDone == 1) {
              //                  locator<NavigationServices>()
              //                   .navigateTo(createclassPageRoute)
              //                   .then((value) {
              //                 classVM.getAllClassList("published");
              //               });
              //               }

              //             },
              //           )
              // )
            ],
          ),
        );
      case 'admin_event_published':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                'assets/png/event_empty.png',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No events",
                style: body2TextBold,
              ),
              Text(
                "This business hasn't submitted any\nevents for review yet.",
                textAlign: TextAlign.center,
                style: body2TextRegular,
              ),
              const Gap(10),
            ],
          ),
        );
      case 'admin_class':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/svg/Classes.svg',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                "No classes",
                style: body2TextMedium,
              ),
              const Gap(10),
              Text(
                "This business hasn't submitted any\nclasses for review yet.",
                textAlign: TextAlign.center,
                style: body2TextRegular,
              ),
            ],
          ),
        );
      default:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
          // width: MediaQuery.of(context).size.width,
          // height: MediaQuery.of(context).size.height,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Gap(16),
              SvgPicture.asset(
                'assets/svg/ic_empty_box.svg',
                height: 48.0,
              ),
              const Gap(10),
              Text(
                title,
                style: body2TextMedium,
              ),
            ],
          ),
        );
    }
  }
}
