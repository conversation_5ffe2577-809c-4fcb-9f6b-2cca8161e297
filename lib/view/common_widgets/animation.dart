import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class GridAnimationWidget extends StatelessWidget {
  final Widget child;
  final int columnCount;
  final int index;
  const GridAnimationWidget(
      {super.key,
      required this.child,
      required this.columnCount,
      required this.index});

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: AnimationConfiguration.staggeredGrid(
        columnCount: columnCount,
        position: index,
        child: SlideAnimation(
          duration: const Duration(milliseconds: 400),
          child: FadeInAnimation(
            child: child,
          ),
        ),
      ),
    );
  }
}
