import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';

class KycBannerWrapper extends StatelessWidget {
  const KycBannerWrapper({
    super.key,
    required this.type,
    this.onDismissTap,
    required this.businessController,
  });
  final String type;
  final VoidCallback? onDismissTap;
  final BusinessController businessController;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    switch (type) {
      case 'requested':
        return screenWidth <= 480
            ? _buildRequestedBanner()
            : Container(
                padding: const EdgeInsets.all(8),
                width: screenWidth <= 1180 ? Get.width : Get.width * .41,
                decoration: BoxDecoration(
                  color: AppColors.ksecondary3.withOpacity(.2),
                  border: Border.all(color: AppColors.ksecondary3, width: 1.0),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Row(
                  children: [
                    const Gap(16),
                    SvgPicture.asset(
                      "assets/icons/kyc_icon.svg",
                      height: 55.0,
                    ),
                    const Gap(10),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'KYC verification is currently in process',
                            overflow: TextOverflow.ellipsis,
                            style: bodyTextSemiBold.copyWith(fontSize: 13),
                          ),
                          Text(
                            'We will notify you of the status once the process is completed.',
                            overflow: TextOverflow.ellipsis,
                            style: body2TextRegular,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
      case 'rejected':
        return screenWidth <= 820
            ? _buildRejectedBanner(context)
            : Container(
                width: screenWidth <= 1180 ? Get.width : Get.width * .6,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.errorSubtle,
                  border: Border.all(color: AppColors.errorRed, width: 1.0),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Row(
                  children: [
                    const Gap(16),
                    SvgPicture.asset(
                      "assets/svg/kyc_rejected.svg",
                      height: 55.0,
                    ),
                    const Gap(10),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'KYC verification has been rejected',
                          style: bodyTextSemiBold,
                        ),
                        Text(
                          "Click on the 'See Why' button to view the reason for rejection.",
                          style: body2TextRegular,
                        ),
                      ],
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 16.0, bottom: 15.0, right: 16.0),
                      child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                        onTap: () {
                          //_onBusinessButtonTap('Profile');
                          locator<NavigationServices>()
                              .navigateTo(profileRoute);
                          //_showCustomDialog(context);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: AppColors.bluecolor,
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 12.0, horizontal: 30.0),
                          child: Text(
                            'See why',
                            textAlign: TextAlign.center,
                            style: rubikStyle.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.kwhite,
                              fontSize: 12.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // const Gap(16),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 16.0, bottom: 15.0, right: 16.0),
                      child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                        onTap: () {
                          _showCustomDialog(context);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: AppColors.bluecolor,
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 12.0, horizontal: 30.0),
                          child: Text(
                            'Apply again',
                            textAlign: TextAlign.center,
                            style: rubikStyle.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.kwhite,
                              fontSize: 12.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      case 'approved':
        return screenWidth <= 480
            ? _buildApprovedBanner()
            : Container(
                width: screenWidth <= 1180 ? Get.width : Get.width * .5,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.information,
                  border:
                      Border.all(color: AppColors.kprimarycolor, width: 1.0),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Row(
                  children: [
                    const Gap(16),
                    SvgPicture.asset(
                      "assets/icons/kyc_icon.svg",
                      height: 55.0,
                    ),
                    const Gap(10),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'KYC verification has been completed and approved',
                            style: bodyTextSemiBold,
                          ),
                          Text(
                            "You can now create and publish events and classes on Parenthing.",
                            style: body2TextRegular,
                          ),
                        ],
                      ),
                    ),
                    const Gap(15),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 16.0, bottom: 15.0, right: 16.0),
                      child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                        onTap: onDismissTap,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: AppColors.bluecolor,
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 12.0, horizontal: 30.0),
                          child: Text(
                            'Dismiss',
                            textAlign: TextAlign.center,
                            style: rubikStyle.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.kwhite,
                              fontSize: 12.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      default:
        return screenWidth <= 480
            ? _buildDefaultBanner()
            : Container(
                width: screenWidth <= 1180 ? Get.width : Get.width * .5,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.ksecondary3.withOpacity(.2),
                  border: Border.all(color: AppColors.ksecondary3, width: 1.0),
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Row(
                  children: [
                    const Gap(16),
                    SvgPicture.asset(
                      "assets/icons/kyc_icon.svg",
                      height: 55.0,
                    ),
                    const Gap(10),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Complete your eKYC',
                            style: bodyTextSemiBold.copyWith(fontSize: 13),
                          ),
                          const Gap(5),
                          Text(
                            'eKYC verification is required to publish events and classes on Parenthing.',
                            style: body3TextRegular,
                          ),
                        ],
                      ),
                    ),
                    const Gap(15),
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 16.0, bottom: 15.0, right: 16.0),
                      child: InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                        onTap: () {
                          locator<NavigationServices>().navigateTo(
                            kycFormPage,
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.0),
                            color: AppColors.bluecolor,
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 12.0, horizontal: 30.0),
                          child: Text(
                            'Proceed',
                            textAlign: TextAlign.center,
                            style: rubikStyle.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.kwhite,
                              fontSize: 12.0,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
    }
  }

  Widget _buildRequestedBanner() {
    return Container(
      padding: const EdgeInsets.all(16),
      width: Get.width,
      decoration: BoxDecoration(
        color: AppColors.ksecondary3.withOpacity(.2),
        border: Border.all(color: AppColors.ksecondary3, width: 1.0),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(
            "assets/icons/kyc_icon.svg",
            height: 40.0,
          ),
          const Gap(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'KYC verification is currently in process',
                  style: bodyTextSemiBold.copyWith(fontSize: 16),
                ),
                const Gap(8),
                Text(
                  'We will notify you of the status once the process is completed.',
                  style: body2TextRegular,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRejectedBanner(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.errorSubtle,
        border: Border.all(color: AppColors.errorRed, width: 1.0),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                "assets/svg/kyc_rejected.svg",
                height: 40.0,
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KYC verification has been rejected',
                      style: bodyTextSemiBold.copyWith(fontSize: 16),
                    ),
                    const Gap(8),
                    Text(
                      "Click on the 'See Why' button to view the reason for rejection.",
                      style: body2TextRegular,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Gap(10),
       Row(
         mainAxisAlignment: screenWidth <= 730 ? MainAxisAlignment.center : MainAxisAlignment.end,
         crossAxisAlignment: CrossAxisAlignment.end,
        children: [
           InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
            onTap: () {
              //_onBusinessButtonTap('Profile');
              locator<NavigationServices>().navigateTo(profileRoute);
              //_showCustomDialog(context);
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.0),
                color: AppColors.bluecolor,
              ),
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 30.0),
              child: Text(
                'See why',
                textAlign: TextAlign.center,
                style: rubikStyle.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.kwhite,
                  fontSize: 12.0,
                ),
              ),
            ),
          ),
          const Gap(10),
          InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
            onTap: () {
              _showCustomDialog(context);
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.0),
                color: AppColors.bluecolor,
              ),
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 30.0),
              child: Text(
                'Apply again',
                textAlign: TextAlign.center,
                style: rubikStyle.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.kwhite,
                  fontSize: 12.0,
                ),
              ),
            ),
          ),
       ],)
        ],
      ),
    );
  }

  Widget _buildApprovedBanner() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.information,
        border: Border.all(color: AppColors.kprimarycolor, width: 1.0),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                "assets/icons/kyc_icon.svg",
                height: 40.0,
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KYC verification has been completed and approved',
                      style: bodyTextSemiBold.copyWith(fontSize: 16),
                    ),
                    const Gap(8),
                    Text(
                      "You can now create and publish events and classes on Parenthing.",
                      style: body2TextRegular,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Gap(10),
          InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
            onTap: onDismissTap,
            child: Container(
              width: Get.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.0),
                color: AppColors.bluecolor,
              ),
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 30.0),
              child: Text(
                'Dismiss',
                textAlign: TextAlign.center,
                style: rubikStyle.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.kwhite,
                  fontSize: 12.0,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultBanner() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.ksecondary3.withOpacity(.2),
        border: Border.all(color: AppColors.ksecondary3, width: 1.0),
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                "assets/icons/kyc_icon.svg",
                height: 40.0,
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Complete your eKYC',
                      style: bodyTextSemiBold.copyWith(fontSize: 16),
                    ),
                    const Gap(8),
                    Text(
                      'eKYC verification is required to publish events and classes on Parenthing.',
                      style: body3TextRegular,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Gap(10),
          InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
            onTap: () {
              locator<NavigationServices>().navigateTo(
                kycFormPage,
              );
            },
            child: Container(
              width: Get.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.0),
                color: AppColors.bluecolor,
              ),
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 30.0),
              child: Text(
                'Proceed',
                textAlign: TextAlign.center,
                style: rubikStyle.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.kwhite,
                  fontSize: 12.0,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomDialog(
          onConfirmTxt: "Apply again",
          title: 'Complete your eKYC',
          content:
              'eKYC is required to verify your business to create and publish classes and events on the Parenthing platform',
          image: "assets/svg/kyc_rejected.svg",
          onConfirm: () {
            Navigator.of(context).pop();
            locator<NavigationServices>()
                .navigateTo(
              kycFormPage,
            )
                .then((value) {
              businessController.businessProfileDetails();
            });
          },
          onCancel: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }
}
