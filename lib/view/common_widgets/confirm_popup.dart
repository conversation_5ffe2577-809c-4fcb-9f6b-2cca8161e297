import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';

class ConfirmPopup extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onConfirm;
  final String confirmText;
  final String cancelText;
  final TextStyle? titleStyle;
  final TextStyle? messageStyle;
  final TextStyle? confirmTextStyle;
  final TextStyle? cancelTextStyle;
  final Color? backgroundColor;
  final Color? confirmButtonColor;
  final Color? cancelButtonColor;
  final double borderRadius;
  final double dialogWidth;
  final double dialogHeight;
  final Widget? icon; // SVG icon widget

  const ConfirmPopup({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    required this.confirmText,
    required this.cancelText,
    this.titleStyle,
    this.messageStyle,
    this.confirmTextStyle,
    this.cancelTextStyle,
    this.backgroundColor,
    this.confirmButtonColor,
    this.cancelButtonColor,
    this.borderRadius = 12.0,
    required this.dialogWidth,
    required this.dialogHeight,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      surfaceTintColor: AppColors.kwhite,
      backgroundColor: AppColors.kwhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      content: SizedBox(
        width: dialogWidth,
        height: dialogHeight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (icon != null) Center(child: icon!), // Display icon if provided
            kSmHeight, // Add space if icon is provided
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            kSmHeight,
            Expanded(
              child: Center(
                child: Text(
                  message,
                  textAlign: TextAlign.center, // Center align the message text
                  style:
                      const TextStyle(fontSize: 14, color: AppColors.secondary),
                ),
              ),
            ),
            kSmHeight,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 40,
                  width: 130,
                  child: TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(AppColors.kwhite),
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          side: const BorderSide(
                            color: AppColors.kgrey,
                            width: 1.0,
                          ),
                        ),
                      ),
                    ),
                    child: Text(
                      cancelText,
                      style:  const TextStyle(
                          color: AppColors.bluecolor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14.0),
                    ),
                  ),
                ),
                smMinWidth,
                SizedBox(
                  height: 40,
                  width: 130,
                  child: TextButton(
                    onPressed: onConfirm,
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(AppColors.bluecolor),
                      shape: MaterialStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                      ),
                    ),
                    child: Text(
                      confirmText,
                      style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14.0),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
