import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class NotificationsCell extends StatelessWidget {
  final String title, msg, notificationsType, createdAt;
  const NotificationsCell(
      {super.key,
      required this.title,
      required this.msg,
      required this.notificationsType,
      required this.createdAt});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      child: Container(
        padding: const EdgeInsets.all(9.0),
        // height: 80,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.backcolor),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  notificationsType.capitalizeFirst.toString(),
                  style: body2TextBold,
                ),
                const Spacer(),
                Text(
                  createdAt,
                  style: body2TextRegular,
                )
              ],
            ),
            const Gap(5),
            Text(
              title,
              style: body2TextMedium,
            ),
            Text(
              msg,
              style: body2TextRegular,
            ),
          ],
        ),
      ),
    );
  }
}
