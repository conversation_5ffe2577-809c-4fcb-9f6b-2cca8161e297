import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/notifications/notifications_cell.dart';

class BusinessNotificationsPage extends StatefulWidget {
  const BusinessNotificationsPage({super.key});

  @override
  State<BusinessNotificationsPage> createState() =>
      _BusinessNotificationsPageState();
}

class _BusinessNotificationsPageState extends State<BusinessNotificationsPage> {
  final UserController userController = Get.find<UserController>();
  final List<String> items =
      List<String>.generate(20, (index) => 'Item ${index + 1}');

  @override
  void initState() {
    userController.getAllNotificationsList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SizedBox(
        width: screenWidth <= 820 ? Get.width : Get.width / 2,
        child: Obx(
          () => ListView.builder(
            itemCount:
                userController.notificationsList.value.notifications.length,
            itemBuilder: (context, index) {
              return NotificationsCell(
                title: userController
                    .notificationsList.value.notifications[index].title,
                msg: userController
                    .notificationsList.value.notifications[index].msg,
                notificationsType: userController.notificationsList.value
                    .notifications[index].notificationType,
                createdAt: userController
                    .notificationsList.value.notifications[index].createdAt,
              );
            },
          ),
        ),
      ),
    );
  }
}

class NotificationDropdown extends StatefulWidget {
  const NotificationDropdown({super.key});

  @override
  State<NotificationDropdown> createState() => _NotificationDropdownState();
}

class _NotificationDropdownState extends State<NotificationDropdown>
    with WidgetsBindingObserver {
  final UserController userController = Get.find<UserController>();
  final EventController eventController = Get.find<EventController>();
  final ClassController classController = Get.find<ClassController>();

  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    userController.getAllNotificationsList();
    selectedBusinessPageNotifier.addListener(_updateSelectedPage);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    // _removeOverlay();
    _overlayEntry?.remove();
    _overlayEntry = null;
    selectedBusinessPageNotifier.removeListener(_updateSelectedPage);
  }

  @override
  void didChangeMetrics() {
    if (_isOpen) {
      _removeOverlay();
    }
  }

  Future<void> fetchNotifications() async {
    if (!mounted) return;
    setState(() {
      isLoading = true;
    });

    try {
      await userController.getAllNotificationsList();
    } catch (e) {
      log('Error fetching notifications: $e');
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _toggleDropdown() async {
    if (_isOpen) {
      _removeOverlay();
    } else {
      await fetchNotifications();
      _showOverlay();
    }
  }

  void _showOverlay() {
    if (!mounted) return;
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOpen = true;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    if (mounted) {
      setState(() {
        _isOpen = false;
      });
    }
  }

  void navigateToAllNotifications() {
    _removeOverlay();
    Future.microtask(() {
      locator<NavigationServices>().navigateTo(businessNotificationsPage);
      selectedBusinessPageNotifier.value = "Notifications";
    });
  }

  void _updateSelectedPage() {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;
    Offset offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              onTap: _removeOverlay,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),
          Positioned(
            left: offset.dx - 250 + size.width,
            top: offset.dy + size.height + 8,
            child: GestureDetector(
              onTap: () {},
              child: Material(
                elevation: 8,
                borderRadius: BorderRadius.circular(12),
                child: Obx(() {
                  final displayList = userController
                      .notificationsList.value.notifications
                      .toList();
                  double maxHeight = MediaQuery.of(context).size.height *
                      0.4; // 60% of screen height
                  double minHeight = 200;
                  if (displayList.isEmpty) {
                    maxHeight = minHeight;
                  }

                  return Container(
                    width: 300,
                    constraints: BoxConstraints(
                      maxHeight: maxHeight,
                      minHeight: minHeight,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.kwhite,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: AppColors.backcolor.withOpacity(0.3)),
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.backcolor.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Notifications',
                                style: body2TextBold,
                              ),
                              // TextButton(
                              //   onPressed: navigateToAllNotifications,
                              //   child: Text(
                              //     'View All',
                              //     style: body2TextRegular.copyWith(
                              //       color: Colors.blue,
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                        Expanded(
                            child: isLoading
                                ? const Center(
                                    child: CircularProgressIndicator(),
                                  )
                                : displayList.isEmpty
                                    ? const Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text('No notifications'),
                                            SizedBox(height: 8),
                                            // TextButton(
                                            //   onPressed: fetchNotifications,
                                            //   child: const Text('Refresh'),
                                            // ),
                                          ],
                                        ),
                                      )
                                    : ListView.builder(
                                        padding: EdgeInsets.zero,
                                        itemCount: displayList.length,
                                        itemBuilder: (context, index) {
                                          final notification =
                                              displayList[index];
                                          final isUnread =
                                              notification.isRead == 0;

                                          return InkWell(
                                            hoverColor: Colors.transparent,
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                            overlayColor:
                                                MaterialStateProperty.all(
                                                    Colors.transparent),
                                            onTap: () async {
                                              _removeOverlay();
                                              if (isUnread) {
                                                await userController
                                                    .markNotificationAsRead(
                                                        notificationId:
                                                            notification.id);
                                              }
                                              final notificationType =
                                                  displayList[index]
                                                      .notificationType;
                                              final meta =
                                                  displayList[index].meta;

                                              log('Notification tapped:');
                                              log('  Meta: $meta');

                                              if (notificationType == 'event' &&
                                                  meta?.eventId != null) {
                                                log('Checking if event exists with eventID: ${meta!.eventId}');

                                                // Check if event exists before navigating
                                                final eventExists =
                                                    await eventController
                                                        .getEventDetailsData(
                                                            meta.eventId!);

                                                if (eventExists) {
                                                  log('Event exists, navigating to EventDetailsPage with eventID: ${meta.eventId}');
                                                  selectedBusinessPageNotifier
                                                      .value = "Events";
                                                  locator<NavigationServices>()
                                                      .navigateTo(
                                                    eventDetailsRoute,
                                                    arguments: <String,
                                                        dynamic>{
                                                      'eventID': meta.eventId!,
                                                      'eventTab': 'details',
                                                    },
                                                  );
                                                } else {
                                                  log('Event with ID ${meta.eventId} does not exist');
                                                  // Show error message to user
                                                  CustomSnackBar.showError(
                                                    'Error',
                                                    'The event no longer exists or has been removed',
                                                  );
                                                }
                                              } else if (notificationType ==
                                                      'class' &&
                                                  meta?.classId != null) {
                                                log('Checking if class exists with classID: ${meta!.classId}');

                                                // Check if class exists before navigating
                                                final classExists =
                                                    await classController
                                                        .getClassDetailsData(
                                                            meta.classId!);

                                                if (classExists) {
                                                  log('Class exists, navigating to ClassDetailsPage with classID: ${meta.classId}');
                                                  selectedBusinessPageNotifier
                                                      .value = "Classes";
                                                  locator<NavigationServices>()
                                                      .navigateTo(
                                                    classDetailsRoute,
                                                    arguments: <String, int>{
                                                      'classByID':
                                                          meta.classId!,
                                                    },
                                                  );
                                                } else {
                                                  log('Class with ID ${meta.classId} does not exist');
                                                  // Show error message to user
                                                  CustomSnackBar.showError(
                                                    'Error',
                                                    'The class no longer exists or has been removed',
                                                  );
                                                }
                                              } else {
                                                log('Unknown notification type or missing meta data');
                                                // Handle other notification types or show generic message
                                                CustomSnackBar.showWarning(
                                                  'Info',
                                                  'Unable to navigate to the referenced content',
                                                );
                                              }
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 12,
                                                vertical: 8,
                                              ),
                                              decoration: BoxDecoration(
                                                color: isUnread
                                                    ? Colors.blue
                                                        .withOpacity(0.05)
                                                    : Colors.transparent,
                                                border: Border(
                                                  bottom: BorderSide(
                                                    color: AppColors.backcolor
                                                        .withOpacity(0.2),
                                                    width: 0.5,
                                                  ),
                                                ),
                                              ),
                                              child: Row(
                                                children: [
                                                  // Unread indicator dot
                                                  Container(
                                                    width: 8,
                                                    height: 8,
                                                    decoration: BoxDecoration(
                                                      color: isUnread
                                                          ? Colors.blue
                                                          : Colors.transparent,
                                                      shape: BoxShape.circle,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Expanded(
                                                              child: Text(
                                                                notification
                                                                    .notificationType
                                                                    .capitalizeFirst
                                                                    .toString(),
                                                                style:
                                                                    body2TextBold
                                                                        .copyWith(
                                                                  fontSize: 12,
                                                                  fontWeight: isUnread
                                                                      ? FontWeight
                                                                          .bold
                                                                      : FontWeight
                                                                          .w600,
                                                                ),
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                              ),
                                                            ),
                                                            Text(
                                                              formatDateForDisplay(
                                                                  notification
                                                                      .createdAt),
                                                              style: body2TextRegular
                                                                  .copyWith(
                                                                      fontSize:
                                                                          10),
                                                            ),
                                                          ],
                                                        ),
                                                        const SizedBox(
                                                            height: 4),
                                                        Text(
                                                          notification.title,
                                                          style: body2TextMedium
                                                              .copyWith(
                                                            fontSize: 12,
                                                            fontWeight: isUnread
                                                                ? FontWeight
                                                                    .w600
                                                                : FontWeight
                                                                    .w500,
                                                          ),
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                        Text(
                                                          notification.msg,
                                                          style:
                                                              body2TextRegular
                                                                  .copyWith(
                                                                      fontSize:
                                                                          11),
                                                          maxLines: 2,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        })),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        onTap: () async {
          if (Get.width <= 820) {
            // For mobile, refresh notifications and navigate directly
            await fetchNotifications();
            navigateToAllNotifications();
          } else {
            // For desktop, show dropdown with fresh data
            _toggleDropdown();
          }
        },
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              child: SvgPicture.asset(
                "assets/svg/Bell.svg",
                height: 30,
                width: 30,
                fit: BoxFit.cover,
              ),
            ),
            Obx(() {
              final unreadCount = userController.unreadNotificationsCount.value;
              log("Current unread count in UI: $unreadCount");

              return unreadCount > 0
                  ? Positioned(
                      right: 6,
                      top: 6,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 99 ? '99+' : '$unreadCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )
                  : const SizedBox.shrink();
            }),
            if (isLoading)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
