import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:flutter/services.dart';

class CustomTextFormField extends StatelessWidget {
  final String hintText;
  final TextEditingController? controller;
  final FormFieldValidator<String>? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final int? maxLength;
  final TextStyle? fontstyle;
  final bool readOnly;
  final bool isEnabled;
  final int maxLines;
  final bool isCounttextRequired;
  final Widget? isSuffixIcon;
  final Widget? suffixIcon;
  final String? suffixText;
  final Color fillColor;
  final void Function()? onTap;
  final TextStyle? hintstyle;
  final Widget? prefixIcon;
  final void Function()? onEditTap;
  final bool isStyle;
  final Function(String) onChanged;
  final Function(String)? onSubmit;
  final VoidCallback? onSaved;
  final bool isAutoValidateRequired;
  final FocusNode? focusNode;
  final String? initialValue;

  const CustomTextFormField({
    super.key,
    this.hintText = "",
    this.controller,
    this.validator, 
    this.initialValue,
    required this.onChanged,
    this.readOnly = false,
    this.isAutoValidateRequired = false,
    this.isEnabled = true,
    this.onSubmit,
    this.onSaved,
    this.keyboardType,
    this.obscureText = false,
    this.maxLength,
    this.maxLines = 1,
    this.isCounttextRequired = false,
    this.isSuffixIcon,
    this.focusNode,
    this.suffixIcon,
    this.suffixText,
    this.prefixIcon,
    this.onTap,
    this.fillColor = AppColors.scaffoldColor,
    this.hintstyle,
    this.onEditTap,
    this.isStyle = false,
    this.inputFormatters,
    this.fontstyle,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          shadowColor: Colors.transparent,
          focusColor: Colors.transparent),
      child: TextFormField(
        autovalidateMode: AutovalidateMode.onUserInteraction,
        controller: controller,
        focusNode: focusNode,
        onSaved: (newValue) {},
        readOnly: readOnly,
        onTap: onTap,
        maxLines: maxLines,
        minLines: 1,
        maxLength: maxLength,
        validator: validator,
        onFieldSubmitted: onSubmit,
        onChanged: (newvalue) {
          onChanged(newvalue);
          if (isAutoValidateRequired) {
            if (newvalue != "") {
              Form.of(context).validate();
            }
          }
        },
        keyboardType: keyboardType,
        initialValue: initialValue,
        inputFormatters: inputFormatters,
        obscureText: obscureText,
        style: fontstyle ?? bodyTextMedium,
        decoration: InputDecoration(
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon ?? isSuffixIcon,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: const BorderSide(color: AppColors.errorRed),
            ),
            filled: true,
            counterText: '',
            fillColor: AppColors.backcolor,
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
              borderSide: BorderSide.none,
            ),
            contentPadding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
            hintStyle: body2TextMedium.copyWith(color: AppColors.placeHolder)),
      ),
    );
  }
}
