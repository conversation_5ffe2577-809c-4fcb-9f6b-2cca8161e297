import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';

class Loader {
  static void showLoading([String description = '']) {
    Get.isSnackbarOpen ? Get.closeAllSnackbars() : null;
    Future.delayed(const Duration(milliseconds: 0)).then((_) {
      Get.dialog(
        Dialog(
          elevation: 0,
          backgroundColor: Colors.white,
          alignment: Alignment.center,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.all(16),
              height: 130.0,
              child: const Column(
                children: [
                  Gap(16),
                  SizedBox(
                    height: 24,
                    width: 24,
                    child: CircularProgressIndicator(
                      color: AppColors.kprimarycolor,
                    ),
                  ),
                   Gap(16),
                  Text(
                    'Loading...',
                    style: TextStyle(fontSize: 14.0, color: Colors.black54),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  static void hideLoading() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }
}
