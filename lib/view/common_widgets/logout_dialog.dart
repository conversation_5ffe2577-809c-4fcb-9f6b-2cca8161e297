import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';

class LogoutDialog extends StatelessWidget {
  // ignore: use_super_parameters
  const LogoutDialog({Key? key, required this.onTap}) : super(key: key);
  final VoidCallback onTap;

  dialogContent(BuildContext context) {
    return 
    Container(
      height: 165,
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10.0,
            offset: Offset(0.0, 10.0),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // To make the card compact
        children: [
          const SizedBox(height: 30.0),
          const Padding(
            padding: EdgeInsets.all(15.0),
            child: Text(
              "Are you sure you want to logout?",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.0,
                fontFamily: 'rubik',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Spacer(),
          const Divider(
            color: Colors.black54,
            height: 1.0,
          ),
          Row(
            children: [
              Expanded(
                child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: TextButton(
                      style: TextButton.styleFrom(
                          foregroundColor: Colors.white,
                          shadowColor: Colors.white,
                          backgroundColor: Colors.white),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 14.0,
                            fontFamily: 'rubik'),
                      ),
                    )),
              ),
              Container(
                height: 40.0,
                width: 1.0,
                color: Colors.black54,
              ),
              Expanded(
                child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: TextButton(
                      onPressed: onTap,
                      child: const Text(
                        'Okay',
                        style: TextStyle(
                            color: AppColors.bluecolor,
                            fontSize: 14.0,
                            fontFamily: 'rubik'),
                      ),
                    )),
              ),
            ],
          ).paddingSymmetric(horizontal: 16),
          // const SizedBox(height: 10.0),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      child: dialogContent(context),
    );
  }
}

//! common dialog box
class CommonConfermationDialog extends StatelessWidget {
  const CommonConfermationDialog(
      {super.key, required this.onTap, required this.title});
  final VoidCallback onTap;
  final String title;

  dialogContent(BuildContext context) {
    return Container(
      height: 165,
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(10),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10.0,
            offset: Offset(0.0, 10.0),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // To make the card compact
        children: [
          const SizedBox(height: 30.0),
          Padding(
            padding: const EdgeInsets.all(15.0),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14.0,
                fontFamily: 'rubik',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Spacer(),
          Divider(
            color: AppColors.ktertiary.withOpacity(.3),
            height: 1.0,
          ),
          Row(
            children: [
              Expanded(
                child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: TextButton(
                      style: TextButton.styleFrom(
                          foregroundColor: Colors.white,
                          shadowColor: Colors.white,
                          backgroundColor: Colors.white),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text(
                        'No',
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 14.0,
                            fontFamily: 'rubik'),
                      ),
                    )),
              ),
              Container(
                height: 40.0,
                width: 1.0,
                color: AppColors.ktertiary.withOpacity(.3),
              ),
              Expanded(
                child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: TextButton(
                      onPressed: onTap,
                      child: const Text(
                        'Yes',
                        style: TextStyle(
                            color: AppColors.bluecolor,
                            fontSize: 14.0,
                            fontFamily: 'rubik'),
                      ),
                    )),
              ),
            ],
          ).paddingSymmetric(horizontal: 16),
          // const SizedBox(height: 10.0),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      child: dialogContent(context),
    );
  }
}
