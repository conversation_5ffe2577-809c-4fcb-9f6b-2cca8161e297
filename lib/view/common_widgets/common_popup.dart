import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

// class ConfirmPopup extends StatelessWidget {
//   final String title;
//   final String message;
//   final VoidCallback onConfirm;

//   const ConfirmPopup(
//       {super.key,
//       required this.title,
//       required this.message,
//       required this.onConfirm});

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       surfaceTintColor: AppColors.kwhite,
//       backgroundColor: AppColors.kwhite,
//       title: Text(title),
//       content: Text(message),
//       actions: [
//         TextButton(
//           onPressed: () {
//             Navigator.pop(context);
//           },
//           child: const Text('Cancel'),
//         ),
//         TextButton(
//           onPressed: onConfirm,
//           child: const Text('OK'),
//         ),
//       ],
//     );
//   }
// }

class SuccesPopup extends StatelessWidget {
  final String imagePath;
  final String title;
  final String subtitle;
  final dynamic Function() onTap;
  final String onTaptitle;
  const SuccesPopup(
      {super.key,
      required this.imagePath,
      required this.title,
      required this.subtitle,
      required this.onTap,
      required this.onTaptitle});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Container(
          // height: 100,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.kwhite,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(imagePath),
              const Gap(10),
              Text(title,
                  style: title2TextSemiBold, textAlign: TextAlign.center),
              const Gap(10),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                maxLines: 2,
                style: bodyTextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              if (subtitle.isNotEmpty)
                const SizedBox(
                  height: 15,
                ),
              // PrimaryButton(
              //   backgroundColor: AppColors.kprimarycolor,
              //   textColor: AppColors.kblack,
              //   onTap: onTap,
              //   text: onTaptitle,
              // )
              InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                onTap: onTap,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.0),
                    color: AppColors.bluecolor,
                  ),
                  padding: const EdgeInsets.symmetric(
                      vertical: 12.0, horizontal: 30.0),
                  child: Text(
                    onTaptitle,
                    textAlign: TextAlign.center,
                    style: rubikStyle.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.kwhite,
                      fontSize: 12.0,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ErrorPopup extends StatelessWidget {
  final String errorMsg, errorTitle, btnLabel;
  final Function()? onTap;
  const ErrorPopup(
      {super.key,
      required this.errorMsg,
      required this.errorTitle,
      this.btnLabel = "",
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.kwhite,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              errorTitle,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: AppColors.kblack,
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
            ),
            errorMsg == "" ? const SizedBox() : const SizedBox(height: 10),
            errorMsg == ""
                ? const SizedBox()
                : Text(
                    errorMsg,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: AppColors.txtsecondary,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
            const Gap(20),
            GestureDetector(
              onTap: onTap ??
                  () {
                    Get.back();
                  },
              child: Container(
                height: 40,
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
                decoration: ShapeDecoration(
                  color: AppColors.kprimarycolor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      btnLabel == "" ? 'Retry' : btnLabel,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: AppColors.kprimarycolor,
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
