import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:shimmer/shimmer.dart';

class ProfileShimmer extends StatelessWidget {
  final int length;

  const ProfileShimmer({super.key, required this.length});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: length,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: AppColors.lightpurple.withOpacity(0.4),
          highlightColor: AppColors.lightyellow,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
            height: 70,
            width: Get.width,
            decoration: BoxDecoration(
              color: AppColors.kwhite,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }
}
