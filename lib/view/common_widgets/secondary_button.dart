import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final String? imagePath;
  final double? width;
  final bool isImage;
  final double height;
  final Color backgroundColor;
  final Color textColor;

  const SecondaryButton({
    super.key,
    required this.text,
    required this.onTap,
    this.backgroundColor = AppColors.kwhite,
    this.textColor = AppColors.kprimarycolor,
    this.width,
    this.imagePath,
    this.isImage = false,
    this.height = 48.0,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(splashColor: Colors.transparent),
      child: InkWell(
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        onTap: onTap,
        child: Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border.all(color: AppColors.bordergrey),
            borderRadius: BorderRadius.circular(6.0),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // const Gap(20),
              if (isImage && imagePath != null)
                Row(children: [
                  SvgPicture.asset(imagePath!, fit: BoxFit.contain),
                  const Gap(10),
                ]),
              Text(text,
                  textAlign: TextAlign.center,
                  style: body2TextBold.copyWith(color: textColor)),
              // const Gap(10),
            ],
          ),
        ),
      ),
    );
  }
}
