import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class CustomDropdownFormField<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final FormFieldValidator<T>? validator;
  final T? value;
  final String hintText;
  final TextStyle? textStyle;

  const CustomDropdownFormField({
    super.key,
    required this.items,
    required this.hintText,
    this.onChanged,
    this.validator,
    this.value,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        splashColor: Colors.transparent,
        shadowColor: Colors.transparent,
        focusColor: Colors.transparent,
      ),
      child: DropdownButtonFormField2<T>(
        isExpanded: true,
        value: value,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          errorStyle: const TextStyle(color: AppColors.errorRed),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide.none,
          ),
          fillColor: AppColors.backcolor,
          filled: true,
          hintText: hintText,
          hintStyle: bodyTextMedium.copyWith(color: AppColors.placeHolder),
        ),
        hint: Text(
          hintText,
          style: bodyTextMedium.copyWith(color: AppColors.placeHolder),
        ),
        items: items,
        onChanged: onChanged,
        validator: validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        style: textStyle ?? bodyTextMedium,
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.only(right: 8),
        ),
        iconStyleData: const IconStyleData(
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColors.bottomlightgrey,
          ),
          iconSize: 26,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 300,
          elevation: 4,
          offset: const Offset(0, -5),
          decoration: BoxDecoration(
            color: AppColors.scaffoldColor,
            borderRadius: BorderRadius.circular(8),
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all(6),
            thumbColor: MaterialStateProperty.all(Colors.grey[300]),
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 48,
          padding: EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }
}

class CustomDropdownUI extends StatelessWidget {
  final List<DropdownMenuItem<Object>>? items;
  final String? value, title, hintText;
  final Function(Object?)? onChanged;
  final void Function(Object?)? onSaved;
  final bool isValidatorReq;
  final bool isTitleRequired;
  final bool isRemoveButton;
  final Function()? onTapRemoveButton;
  final bool isReadOnly;

  const CustomDropdownUI(
      {super.key,
      this.items,
      this.value,
      this.onChanged,
      this.onSaved,
      this.title,
      this.isValidatorReq = false,
      this.isTitleRequired = true,
      this.hintText,
      this.isRemoveButton = false,
      this.onTapRemoveButton,
      this.isReadOnly = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title == "" ? const SizedBox() : Text(title!, style: body2TextRegular),
        const SizedBox(height: 5),
        Container(
          margin: const EdgeInsets.only(top: 4),
          child: DropdownButtonFormField2<Object>(
            isExpanded: true,
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.transparent),
                borderRadius: BorderRadius.circular(6),
              ),
              errorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(6),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Colors.red),
                borderRadius: BorderRadius.circular(6),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
              fillColor: AppColors.scaffoldColor,
              filled: true,
              errorStyle: const TextStyle(color: Colors.red, height: 0.5),
              border: InputBorder.none,
            ),
            hint: Text(
              hintText ?? "Select",
              style: bodyTextMedium.copyWith(color: AppColors.placeHolder),
            ),
            items: items,
            value: value,
            validator: (value) {
              if (isValidatorReq == true) {
                if (value == null) {
                  return "Required";
                }
                return null;
              }
              return null;
            },
            onChanged: isReadOnly ? null : onChanged,
            onSaved: onSaved,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            style: bodyTextMedium,
            iconStyleData: const IconStyleData(
              icon: Icon(
                Icons.arrow_drop_down,
                color: AppColors.kgrey,
              ),
              iconSize: 36,
            ),
            dropdownStyleData: DropdownStyleData(
              maxHeight: 250,
              width: null,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.scaffoldColor,
              ),
              offset: const Offset(0, -5),
              scrollbarTheme: ScrollbarThemeData(
                radius: const Radius.circular(40),
                thickness: MaterialStateProperty.all(6),
                thumbColor: MaterialStateProperty.all(Colors.grey[300]),
              ),
            ),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
              padding: EdgeInsets.only(left: 14, right: 14),
            ),
            buttonStyleData: const ButtonStyleData(
              padding: EdgeInsets.only(right: 8),
            ),
          ),
        ),
      ],
    );
  }
}

class CustomDropDown extends StatefulWidget {
  final List<String> items;
  final String? initialValue;
  final String hinttext;
  final String? Function(String?)? validator;
  final ValueChanged<String?>? onChanged;
  final Function(String?)? onSaved;
  final void Function()? onTap;

  const CustomDropDown({
    super.key,
    required this.items,
    this.initialValue,
    this.onChanged,
    this.onSaved,
    this.validator,
    this.onTap,
    this.hinttext = '',
  });

  @override
  State<CustomDropDown> createState() => _CustomDropDownState();
}

class _CustomDropDownState extends State<CustomDropDown> {
  String? _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        splashColor: Colors.transparent,
        shadowColor: Colors.transparent,
        focusColor: Colors.transparent,
      ),
      child: DropdownButtonFormField2<String>(
        isExpanded: true,
        style: bodyTextMedium,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: const BorderSide(color: AppColors.errorRed),
          ),
          errorStyle: const TextStyle(color: AppColors.errorRed),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide.none,
          ),
          fillColor: AppColors.backcolor,
          filled: true,
          hintText: widget.hinttext,
          hintStyle: body2TextMedium.copyWith(color: AppColors.placeHolder),
        ),
        hint: Text(
          widget.hinttext,
          style: body2TextMedium.copyWith(color: AppColors.placeHolder),
        ),
        items: widget.items
            .map((item) => DropdownMenuItem<String>(
                  onTap: widget.onTap,
                  value: item,
                  child: Text(
                    item,
                    style: bodyTextMedium,
                  ),
                ))
            .toList(),
        onChanged: (value) {
          setState(() {
            _value = value;
          });
          widget.onChanged?.call(value);
        },
        value: _value,
        validator: widget.validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        onSaved: widget.onSaved,
        buttonStyleData: const ButtonStyleData(
          padding: EdgeInsets.only(right: 8),
        ),
        iconStyleData: const IconStyleData(
          icon: Icon(
            Icons.keyboard_arrow_down_outlined,
            color: AppColors.bottomlightgrey,
          ),
          iconSize: 26,
        ),
        dropdownStyleData: DropdownStyleData(
          maxHeight: 300,
          elevation: 4,
          offset: const Offset(0, -5),
          decoration: BoxDecoration(
            color: AppColors.scaffoldColor,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          scrollbarTheme: ScrollbarThemeData(
            radius: const Radius.circular(40),
            thickness: MaterialStateProperty.all(6),
            thumbColor: MaterialStateProperty.all(Colors.grey[300]),
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 48,
          padding: EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }
}

class PaginatedCustomDropdownUI extends StatefulWidget {
  final List<DropdownMenuItem<Object>>? items;
  final String? title, hintText;
  final Function(Object?)? onChanged;
  final Object? value;
  final void Function(Object?)? onSaved;
  final bool isValidatorReq;
  final bool isTitleRequired;
  final bool isRemoveButton;
  final Function()? onTapRemoveButton;
  final bool isReadOnly;
  final VoidCallback? onLoadMore;
  final bool isLoadingMore;
  final bool hasMoreData;

  const PaginatedCustomDropdownUI({
    super.key,
    this.items,
    this.value,
    this.onChanged,
    this.onSaved,
    this.title,
    this.isValidatorReq = false,
    this.isTitleRequired = true,
    this.hintText,
    this.isRemoveButton = false,
    this.onTapRemoveButton,
    this.isReadOnly = false,
    this.onLoadMore,
    required this.isLoadingMore,
    required this.hasMoreData,
  });

  @override
  State<PaginatedCustomDropdownUI> createState() =>
      _PaginatedCustomDropdownUIState();
}

class _PaginatedCustomDropdownUIState extends State<PaginatedCustomDropdownUI> {
  final LayerLink _layerLink = LayerLink();
  final ScrollController _scrollController = ScrollController();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 50) {
      if (widget.hasMoreData &&
          !widget.isLoadingMore &&
          widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  void _toggleDropdown() {
    if (widget.isReadOnly) return;

    if (_isOpen) {
      _removeOverlay();
    } else {
      _createOverlay();
    }
  }

  void _createOverlay() {
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: _getButtonWidth(),
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 52), // Adjust based on your button height
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(6),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 250),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.scaffoldColor,
                border: Border.all(color: AppColors.kgrey.withOpacity(0.3)),
              ),
              child: _buildDropdownList(),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      _isOpen = true;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() {
      _isOpen = false;
    });
  }

  double _getButtonWidth() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    return renderBox.size.width;
  }

  Widget _buildDropdownList() {
    List<Widget> children = [];

    // Add regular items
    if (widget.items != null) {
      for (int i = 0; i < widget.items!.length; i++) {
        final item = widget.items![i];
        children.add(
          InkWell(
            onTap: () {
              if (widget.onChanged != null) {
                widget.onChanged!(item.value);
              }
              _removeOverlay();
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
              decoration: BoxDecoration(
                color: widget.value == item.value
                    ? AppColors.kgrey.withOpacity(0.1)
                    : Colors.transparent,
              ),
              child: item.child,
            ),
          ),
        );

        // Add divider except for last item
        if (i < widget.items!.length - 1) {
          children
              .add(Divider(height: 1, color: AppColors.kgrey.withOpacity(0.2)));
        }
      }
    }

    // Add loading indicator
    if (widget.isLoadingMore && widget.hasMoreData) {
      if (children.isNotEmpty) {
        children
            .add(Divider(height: 1, color: AppColors.kgrey.withOpacity(0.2)));
      }
      children.add(
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.kgrey),
              ),
            ),
          ),
        ),
      );
    }
    // Add "scroll for more" text
    else if (!widget.isLoadingMore &&
        widget.hasMoreData &&
        (widget.items?.length ?? 0) > 0) {
      if (children.isNotEmpty) {
        children
            .add(Divider(height: 1, color: AppColors.kgrey.withOpacity(0.2)));
      }
      children.add(
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Center(
            child: Text(
              'Scroll down for more...',
              style: bodyTextMedium.copyWith(
                color: AppColors.kgrey,
                fontStyle: FontStyle.italic,
                fontSize: 12,
              ),
            ),
          ),
        ),
      );
    }

    if (children.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'No items available',
            style: bodyTextMedium.copyWith(color: AppColors.kgrey),
          ),
        ),
      );
    }

    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      radius: const Radius.circular(40),
      thickness: 6,
      child: ListView(
        controller: _scrollController,
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        children: children,
      ),
    );
  }

  String _getDisplayText() {
    if (widget.value == null) {
      return widget.hintText ?? "Select";
    }

    // Find the matching item to get its display text
    final matchingItem = widget.items?.firstWhere(
      (item) => item.value == widget.value,
      orElse: () => DropdownMenuItem(
          value: widget.value, child: Text(widget.value.toString())),
    );

    if (matchingItem?.child is Text) {
      return (matchingItem!.child as Text).data ?? widget.value.toString();
    }

    return widget.value.toString();
  }

  void _validateField() {
    if (widget.isValidatorReq && widget.value == null) {
      setState(() {
        _errorText = "Required";
      });
    } else {
      setState(() {
        _errorText = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title?.isNotEmpty == true) ...[
          Text(widget.title!, style: body2TextRegular),
          const SizedBox(height: 5),
        ],
        Container(
          margin: const EdgeInsets.only(top: 4),
          child: CompositedTransformTarget(
            link: _layerLink,
            child: GestureDetector(
              onTap: _toggleDropdown,
              child: Container(
                padding: const EdgeInsets.symmetric(
                    vertical: 12.0, horizontal: 12.0),
                decoration: BoxDecoration(
                  color: AppColors.scaffoldColor,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: _errorText != null ? Colors.red : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        _getDisplayText(),
                        style: widget.value == null
                            ? bodyTextMedium.copyWith(
                                color: AppColors.placeHolder)
                            : bodyTextMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(
                      _isOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                      color: AppColors.kgrey,
                      size: 36,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (_errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            _errorText!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 12,
              height: 0.5,
            ),
          ),
        ],
      ],
    );
  }
}

// Extension to handle form validation if needed
class PaginatedDropdownFormField extends FormField<Object> {
  final PaginatedCustomDropdownUI dropdown;

  PaginatedDropdownFormField({
    super.key,
    required this.dropdown,
    super.onSaved,
    super.validator,
    super.initialValue,
    super.autovalidateMode,
  }) : super(
          builder: (FormFieldState<Object> state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PaginatedCustomDropdownUI(
                  items: dropdown.items,
                  value: state.value ?? dropdown.value,
                  onChanged: (value) {
                    state.didChange(value);
                    if (dropdown.onChanged != null) {
                      dropdown.onChanged!(value);
                    }
                  },
                  title: dropdown.title,
                  hintText: dropdown.hintText,
                  isValidatorReq: false, // Handle validation through FormField
                  isReadOnly: dropdown.isReadOnly,
                  onLoadMore: dropdown.onLoadMore,
                  isLoadingMore: dropdown.isLoadingMore,
                  hasMoreData: dropdown.hasMoreData,
                ),
                if (state.hasError) ...[
                  const SizedBox(height: 4),
                  Text(
                    state.errorText!,
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      height: 0.5,
                    ),
                  ),
                ],
              ],
            );
          },
        );
}
