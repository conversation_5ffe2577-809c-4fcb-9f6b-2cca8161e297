import 'dart:developer';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';
import 'package:parenthing_dashboard/view/event/widgets/date_time_widget.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:parenthing_dashboard/model/location_model.dart';
import 'package:parenthing_dashboard/view/profile/add_address.dart';

class EventEditPage extends StatefulWidget {
  final int? eventID;
  final bool isEdit;
  final bool isDuplicateEvent;

  const EventEditPage(
      {super.key,
      this.eventID,
      required this.isEdit,
      required this.isDuplicateEvent});

  @override
  State<EventEditPage> createState() => _EventEditPageState();
}

class _EventEditPageState extends State<EventEditPage> {
  // final _formKey = GlobalKey<FormState>();
  final BusinessController businessController = Get.find<BusinessController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  late final EventController eventController;
  String? selectedTime;
  late String eventType;
  late bool isOfflineSelected;
  late bool isOnlineSelected;
  late bool isOfflineOnlineSelected;
  RxBool isSelected = true.obs;
  late String ticketType;
  late bool isTicketPaid;
  late bool isTicketFree;
  late String multipleBooking;
  late bool isMultipleTicketYes;
  late bool isMultipleTicketNo;
  RxBool isWebUrl = false.obs;
  RxBool isWhatsAppNumber = true.obs;
  String selectedCity = "";
  bool isLoading = true;

  // bool is1Selected = false;
  // bool is2Selected = false;
  // bool is3Selected = false;

  final List<String> _ageOptions =
      List<String>.generate(17, (index) => (index).toString());

  final List<String> _maxAgeOptions =
      List<String>.generate(17, (index) => (index + 2).toString())..add('18+');
  String? _selectedMinAge;
  String? _selectedMaxAge;
  String selectedAddress = '';

  // Uint8List? _imageBytes;
  String? _imageUrl;
  // final ImagePicker _picker = ImagePicker();

  final whatsappNumberController = TextEditingController();
  final webUrlController = TextEditingController();

  @override
  void initState() {
    super.initState();
    eventController = Get.find<EventController>();
    _initializeDefaultValues();

    _selectedMinAge = eventController.eventDetailsModel.value.minAge.toString();
    _selectedMaxAge = eventController.eventDetailsModel.value.maxAge.toString();
    if (widget.isEdit) {
      loadData();
    } else {}
  }

  void _initializeDefaultValues() {
    eventType = '';
    isOfflineSelected = false;
    isOnlineSelected = false;
    isOfflineOnlineSelected = false;
    ticketType = '';
    isTicketPaid = false;
    isTicketFree = false;
    multipleBooking = '';
    isMultipleTicketYes = false;
    isMultipleTicketNo = false;
    selectedCity = '';
  }

  Future<void> loadData() async {
    await Future.delayed(const Duration(milliseconds: 300), () {
      final eventDetails = eventController.eventDetailsModel.value;
      setState(() {
        businessController.businessProfileDetails();
        eventController.getEventDetailsData(widget.eventID!);
        eventType = eventDetails.eventType.capitalizeFirst.toString();
        isOfflineSelected = eventType == "Offline";
        isOnlineSelected = eventType == "Online";
        isOfflineOnlineSelected = eventType == "Offline + Online";
        ticketType = eventDetails.ticketType.capitalizeFirst.toString();
        isTicketPaid = ticketType == "Paid";
        isTicketFree = ticketType == "Free";
        multipleBooking =
            eventDetails.multiplePricing.capitalizeFirst.toString();
        isMultipleTicketYes = multipleBooking == "Yes";
        isMultipleTicketNo = multipleBooking == "No";
        _imageUrl =
            eventDetails.bannerUrl == "null" ? "" : eventDetails.bannerUrl;
        _selectedMinAge = eventDetails.minAge.toString();
        // selectedAddress = eventDetails.address;
        selectedCity = eventDetails.city;
        selectedAddress = eventDetails.locationDetails.address;
        if (eventDetails.maxAge == 0) {
          _selectedMaxAge = _ageOptions.first;
        } else if (eventDetails.maxAge > 18) {
          _selectedMaxAge = '18+';
        } else {
          _selectedMaxAge = eventDetails.maxAge.toString();
        }
        if (eventDetails.ctaUrl.isEmpty) {
          isWhatsAppNumber.value = true;
          isWebUrl.value = false;
        } else if (eventDetails.ctaMobile.isEmpty) {
          isWebUrl.value = true;
          isWhatsAppNumber.value = false;
        }
        if (isWebUrl.value) {
          webUrlController.text = eventDetails.ctaUrl;
        } else {
          whatsappNumberController.text = eventDetails.ctaMobile;
        }

        log("CTA URL: ${eventDetails.ctaUrl}");
        log("multiplePricing ${multipleBooking = eventDetails.multiplePricing}");
        isLoading = false;
      });
    });
  }

  void _updateDuration() {
    DateTime startDateTime = widget.isEdit
        ? DateTime.parse(eventController.eventDetailsModel.value.startDate)
        : getValidDateTime(eventController.eventDetailsModel.value.startDate,
            defaultValue: DateTime.now());
    DateTime endDateTime = widget.isEdit
        ? DateTime.parse(eventController.eventDetailsModel.value.endDate)
        : getValidDateTime(eventController.eventDetailsModel.value.endDate,
            defaultValue: startDateTime.add(const Duration(minutes: 30)));

    Duration difference = endDateTime.difference(startDateTime);

    if (widget.isEdit) {
      eventController.eventDetailsModel.value.duration = difference.inMinutes;
    } else {
      eventController.eventDetailsModel.value.duration = difference.inMinutes;
    }
  }

  @override
  void dispose() {
    webUrlController.dispose();
    whatsappNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
        backgroundColor: AppColors.kwhite,
        body: isLoading
            ? const Center(child: CircularProgressIndicator.adaptive())
            : ScrollConfiguration(
                behavior:
                    ScrollConfiguration.of(context).copyWith(scrollbars: false),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Container(
                    width: screenWidth <= 820 ? Get.width : Get.width * .7,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(
                        color: AppColors.kgrey,
                      ),
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                InkWell(
                                  hoverColor: Colors.transparent,
                                  splashColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  overlayColor: MaterialStateProperty.all(
                                      Colors.transparent),
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return CustomDialog(
                                          onConfirmTxt: "Yes, leave",
                                          onCancelText: "No",
                                          title: 'Leave this page?',
                                          content:
                                              'Are you sure you want to leave this page? All field details will be discarded',
                                          image:
                                              "assets/icons/WarningCircle.svg",
                                          onConfirm: () {
                                            Navigator.of(context).pop();
                                            locator<NavigationServices>()
                                                .goBack();
                                          },
                                          onCancel: () {
                                            Navigator.of(context).pop();
                                          },
                                        );
                                      },
                                    );
                                  },
                                  child: SvgPicture.asset(
                                      'assets/icons/arrow-left.svg',
                                      fit: BoxFit.fill,
                                      height: 32,
                                      width: 32),
                                ),
                                mdWidth,
                                Text("Edit an event",
                                    style: heading2TextRegular),
                              ],
                            ),
                          ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          screenWidth <= 820
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 8),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      buildEditEventBanner(),
                                      buildEditEventName(),
                                    ],
                                  ),
                                )
                              :
                               Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    children: [
                                      Expanded(child: buildEditEventName()),
                                      const Gap(20),
                                      buildEditEventBanner(),
                                    ],
                                  ),
                                ),
                          const Divider(
                            thickness: 1.0,
                            color: AppColors.kgrey,
                          ),
                          screenWidth <= 820
                              ? Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                            child: buildEditStartTime(context))
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Expanded(
                                            child:
                                                buildEditEndDateTime(context))
                                      ],
                                    )
                                  ],
                                )
                              :
                               Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                        child: buildEditStartTime(context)),
                                    const Gap(20),
                                    Expanded(
                                        child: buildEditEndDateTime(context)),
                                  ],
                                ),
                          const Gap(20),
                          buildEditDurationDisplay(),
                          // buildEditDurationDropDown(),
                          const Gap(10),
                          const Divider(thickness: 1.0, color: AppColors.kgrey),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              screenWidth <= 820
                                  ? Column(
                                      children: [
                                        buildAgeGroupSection(),
                                        builEditdEventTypeSection(),
                                      ],
                                    )
                                  :
                                   Row(
                                      children: [
                                        Expanded(
                                          child: buildAgeGroupSection(),
                                        ),
                                        const Gap(20),
                                        Expanded(
                                          child: builEditdEventTypeSection(),
                                        ),
                                      ],
                                    ),
                              screenWidth < 820
                                  ? Visibility(
                                      visible: !isOnlineSelected,
                                      child: Column(
                                        children: [
                                          //_buildEventCity(),
                                          buildEventAddress()
                                        ],
                                      ))
                                  :
                                  
                                   Visibility(
                                      visible: !isOnlineSelected,
                                      child: Row(
                                        children: [
                                          // Expanded(
                                          //   child: _buildEventCity(),
                                          // ),
                                          Expanded(child: buildEventAddress()),
                                        ],
                                      ),
                                    ),
                              const Divider(
                                  thickness: 1.0, color: AppColors.kgrey),
                              screenWidth <= 820
                                  ? Column(
                                      children: [
                                        buildEditTicketTypeSection(),
                                        // isTicketFree
                                        //     ? const Spacer()
                                        //     :
                                             buildMultipleTicketPricing(),
                                      ],
                                    )
                                  : 
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                            child:
                                                buildEditTicketTypeSection()),
                                        const Gap(30),
                                        isTicketFree
                                            ? const Spacer()
                                            : Expanded(
                                                child:
                                                    buildMultipleTicketPricing(),
                                              ),
                                      ],
                                    ),
                              isTicketFree
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text("Price starts at*",
                                              style: body2TextRegular),
                                          const Gap(10),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: CustomTextFormField(
                                                  initialValue: eventController
                                                      .eventDetailsModel
                                                      .value
                                                      .price
                                                      .toString(),
                                                  onChanged: (val) {
                                                    int price =
                                                        int.tryParse(val) ?? 0;
                                                    eventController
                                                        .eventDetailsModel
                                                        .value
                                                        .price = price;
                                                  },
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  maxLength: 8,
                                                  hintText: "Enter amount",
                                                  validator: (value) {
                                                    if (value == null ||
                                                        value.isEmpty) {
                                                      return 'Amount Required';
                                                    }
                                                    return null;
                                                  },
                                                  prefixIcon: const Padding(
                                                    padding: EdgeInsets.only(
                                                        right: 8),
                                                    child: Icon(
                                                        Icons.currency_rupee,
                                                        size: 20,
                                                        color: AppColors
                                                            .bottomlightgrey),
                                                  ),
                                                ),
                                              ),
                                              const Spacer()
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                              const Gap(10),
                              const Divider(
                                  thickness: 1.0, color: AppColors.kgrey),
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("Bookings", style: title3TextSemiBold),
                                    const Gap(15),
                                    screenWidth <= 820
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                                Text(
                                                    "Preferred ticket booking*",
                                                    style: body2TextRegular),
                                                const Gap(8),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isWebUrl.value =
                                                                true;
                                                            isWhatsAppNumber
                                                                .value = false;
                                                            eventController
                                                                .eventDetailsModel
                                                                .value
                                                                .ctaMobile = "";
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 42,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              left: Radius
                                                                  .circular(12),
                                                            ),
                                                            color: isWebUrl
                                                                    .value
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .scaffoldColor,
                                                            border: Border.all(
                                                              color: isWebUrl
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .bordergrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "Web URL",
                                                              style: body2TextSemiBold.copyWith(
                                                                  color: isWebUrl.value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .txtsecondary),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: InkWell(
                                                        hoverColor:
                                                            Colors.transparent,
                                                        splashColor:
                                                            Colors.transparent,
                                                        highlightColor:
                                                            Colors.transparent,
                                                        overlayColor:
                                                            MaterialStateProperty
                                                                .all(Colors
                                                                    .transparent),
                                                        onTap: () {
                                                          setState(() {
                                                            isWebUrl.value =
                                                                false;
                                                            isWhatsAppNumber
                                                                .value = true;
                                                            eventController
                                                                .eventDetailsModel
                                                                .value
                                                                .ctaUrl = "";
                                                          });
                                                        },
                                                        child: Container(
                                                          height: 42,
                                                          decoration:
                                                              BoxDecoration(
                                                            borderRadius:
                                                                const BorderRadius
                                                                    .horizontal(
                                                              right: Radius
                                                                  .circular(12),
                                                            ),
                                                            color: isWhatsAppNumber
                                                                    .value
                                                                ? AppColors
                                                                    .kwhite
                                                                : AppColors
                                                                    .scaffoldColor,
                                                            border: Border.all(
                                                              color: isWhatsAppNumber
                                                                      .value
                                                                  ? AppColors
                                                                      .txtprimary
                                                                  : AppColors
                                                                      .bordergrey,
                                                            ),
                                                          ),
                                                          child: Center(
                                                            child: Text(
                                                              "WhatsApp number",
                                                              style: body2TextSemiBold.copyWith(
                                                                  color: isWhatsAppNumber.value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .txtsecondary),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                const Gap(15),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        isWebUrl.value
                                                            ? "Booking URL*"
                                                            : "WhatsApp Number",
                                                        style:
                                                            body2TextRegular),
                                                    const Gap(8),
                                                    CustomTextFormField(
                                                      maxLines: isWebUrl.value
                                                          ? 10
                                                          : 1,
                                                      controller: isWebUrl.value
                                                          ? webUrlController
                                                          : whatsappNumberController,
                                                      keyboardType: isWebUrl
                                                              .value
                                                          ? TextInputType.url
                                                          : TextInputType.phone,
                                                      inputFormatters:
                                                          isWebUrl.value
                                                              ? []
                                                              : [
                                                                  FilteringTextInputFormatter
                                                                      .digitsOnly,
                                                                  LengthLimitingTextInputFormatter(
                                                                      10),
                                                                ],
                                                      onChanged: (val) {
                                                        if (isWebUrl.value) {
                                                          eventController
                                                              .eventDetailsModel
                                                              .value
                                                              .ctaUrl = val;
                                                        } else {
                                                          eventController
                                                              .eventDetailsModel
                                                              .value
                                                              .ctaMobile = val;
                                                        }
                                                      },
                                                      validator: (value) {
                                                        if (value == null ||
                                                            value.isEmpty) {
                                                          return isWebUrl.value
                                                              ? 'URL Required'
                                                              : 'WhatsApp Number Required';
                                                        }
                                                        if (isWebUrl.value) {
                                                          // URL validation
                                                          final urlPattern = RegExp(
                                                              r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
                                                          if (!urlPattern
                                                              .hasMatch(
                                                                  value)) {
                                                            return 'Please enter a valid URL (e.g., https://example.com)';
                                                          }
                                                        } else {
                                                          // Mobile number validation
                                                          if (value.length !=
                                                              10) {
                                                            return 'Mobile number must be 10 digits';
                                                          }
                                                          if (!RegExp(
                                                                  r'^[6-9][0-9]{9}$')
                                                              .hasMatch(
                                                                  value)) {
                                                            return 'Please enter a valid Indian mobile number';
                                                          }
                                                        }

                                                        return null;
                                                      },
                                                      hintText: isWebUrl.value
                                                          ? "e.g https://www.events.com"
                                                          : "+91-9001122445",
                                                      prefixIcon: isWebUrl.value
                                                          ? null
                                                          : Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          12,
                                                                      vertical:
                                                                          16),
                                                              child: Text(
                                                                "+91",
                                                                style: body2TextMedium
                                                                    .copyWith(
                                                                        color: AppColors
                                                                            .txtprimary),
                                                              ),
                                                            ),
                                                      maxLength: isWebUrl.value
                                                          ? null
                                                          : 10,
                                                    ),
                                                  ],
                                                ),
                                              ])
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        "Preferred ticket booking*",
                                                        style:
                                                            body2TextRegular),
                                                    const Gap(8),
                                                    Row(
                                                      children: [
                                                        Expanded(
                                                          child: InkWell(
                                                            hoverColor: Colors
                                                                .transparent,
                                                            splashColor: Colors
                                                                .transparent,
                                                            highlightColor:
                                                                Colors
                                                                    .transparent,
                                                            overlayColor:
                                                                MaterialStateProperty
                                                                    .all(Colors
                                                                        .transparent),
                                                            onTap: () {
                                                              setState(() {
                                                                isWebUrl.value =
                                                                    true;
                                                                isWhatsAppNumber
                                                                        .value =
                                                                    false;
                                                                eventController
                                                                    .eventDetailsModel
                                                                    .value
                                                                    .ctaMobile = "";
                                                              });
                                                            },
                                                            child: Container(
                                                              height: 42,
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    const BorderRadius
                                                                        .horizontal(
                                                                  left: Radius
                                                                      .circular(
                                                                          12),
                                                                ),
                                                                color: isWebUrl
                                                                        .value
                                                                    ? AppColors
                                                                        .kwhite
                                                                    : AppColors
                                                                        .scaffoldColor,
                                                                border:
                                                                    Border.all(
                                                                  color: isWebUrl.value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .bordergrey,
                                                                ),
                                                              ),
                                                              child: Center(
                                                                child: Text(
                                                                  "Web URL",
                                                                  style: body2TextSemiBold.copyWith(
                                                                      color: isWebUrl.value
                                                                          ? AppColors
                                                                              .txtprimary
                                                                          : AppColors
                                                                              .txtsecondary),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: InkWell(
                                                            hoverColor: Colors
                                                                .transparent,
                                                            splashColor: Colors
                                                                .transparent,
                                                            highlightColor:
                                                                Colors
                                                                    .transparent,
                                                            overlayColor:
                                                                MaterialStateProperty
                                                                    .all(Colors
                                                                        .transparent),
                                                            onTap: () {
                                                              setState(() {
                                                                isWebUrl.value =
                                                                    false;
                                                                isWhatsAppNumber
                                                                        .value =
                                                                    true;
                                                                eventController
                                                                    .eventDetailsModel
                                                                    .value
                                                                    .ctaUrl = "";
                                                              });
                                                            },
                                                            child: Container(
                                                              height: 42,
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    const BorderRadius
                                                                        .horizontal(
                                                                  right: Radius
                                                                      .circular(
                                                                          12),
                                                                ),
                                                                color: isWhatsAppNumber.value
                                                                    ? AppColors
                                                                        .kwhite
                                                                    : AppColors
                                                                        .scaffoldColor,
                                                                border:
                                                                    Border.all(
                                                                  color: isWhatsAppNumber.value
                                                                      ? AppColors
                                                                          .txtprimary
                                                                      : AppColors
                                                                          .bordergrey,
                                                                ),
                                                              ),
                                                              child: Center(
                                                                child: Text(
                                                                  "WhatsApp number",
                                                                  style: body2TextSemiBold.copyWith(
                                                                      color: isWhatsAppNumber.value
                                                                          ? AppColors
                                                                              .txtprimary
                                                                          : AppColors
                                                                              .txtsecondary),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              const Gap(15),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                        isWebUrl.value
                                                            ? "Booking URL*"
                                                            : "WhatsApp Number",
                                                        style:
                                                            body2TextRegular),
                                                    const Gap(8),
                                                    CustomTextFormField(
                                                      controller: isWebUrl.value
                                                          ? webUrlController
                                                          : whatsappNumberController,
                                                      maxLines: isWebUrl.value
                                                          ? 6
                                                          : 1,
                                                      keyboardType: isWebUrl
                                                              .value
                                                          ? TextInputType.url
                                                          : TextInputType.phone,
                                                      inputFormatters:
                                                          isWebUrl.value
                                                              ? []
                                                              : [
                                                                  FilteringTextInputFormatter
                                                                      .digitsOnly,
                                                                  LengthLimitingTextInputFormatter(
                                                                      10),
                                                                ],
                                                      onChanged: (val) {
                                                        if (isWebUrl.value) {
                                                          eventController
                                                              .eventDetailsModel
                                                              .value
                                                              .ctaUrl = val;
                                                        } else {
                                                          eventController
                                                              .eventDetailsModel
                                                              .value
                                                              .ctaMobile = val;
                                                        }
                                                      },
                                                      validator: (value) {
                                                        if (value == null ||
                                                            value.isEmpty) {
                                                          return isWebUrl.value
                                                              ? 'URL Required'
                                                              : 'WhatsApp Number Required';
                                                        }
                                                        if (isWebUrl.value) {
                                                          // URL validation
                                                          final urlPattern = RegExp(
                                                              r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
                                                          if (!urlPattern
                                                              .hasMatch(
                                                                  value)) {
                                                            return 'Please enter a valid URL (e.g., https://example.com)';
                                                          }
                                                        } else {
                                                          // Mobile number validation
                                                          if (value.length !=
                                                              10) {
                                                            return 'Mobile number must be 10 digits';
                                                          }
                                                          if (!RegExp(
                                                                  r'^[6-9][0-9]{9}$')
                                                              .hasMatch(
                                                                  value)) {
                                                            return 'Please enter a valid Indian mobile number';
                                                          }
                                                        }
                                                        return null;
                                                      },
                                                      hintText: isWebUrl.value
                                                          ? "e.g https://www.events.com"
                                                          : "+91-9001122445",
                                                      prefixIcon: isWebUrl.value
                                                          ? null
                                                          : Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      horizontal:
                                                                          12,
                                                                      vertical:
                                                                          16),
                                                              child: Text(
                                                                "+91",
                                                                style: body2TextMedium
                                                                    .copyWith(
                                                                        color: AppColors
                                                                            .txtprimary),
                                                              ),
                                                            ),
                                                      maxLength: isWebUrl.value
                                                          ? null
                                                          : 10,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                  ],
                                ),
                              ),
                              const Divider(
                                  thickness: 1.0, color: AppColors.kgrey),
                              screenWidth <= 820
                                  ? Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Padding(
                                          padding: EdgeInsets.all(16),
                                          child: Row(
                                            children: [
                                              Icon(Icons.info_outline),
                                              Gap(8),
                                              Expanded(
                                                child: Text(
                                                    "Please fill out all required fields to submit for review",
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const Gap(15),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SecondaryButton(
                                              text: 'Save as draft',
                                              onTap: () async {
                                                onSubmitOrSaveDraft(false);
                                              },
                                            ),
                                            const Gap(10),
                                            PrimaryButton(
                                              text: 'Submit for review',
                                              onTap: () async {
                                                onSubmitOrSaveDraft(true);
                                              },
                                            ),
                                          ],
                                        ),
                                        const Gap(25),
                                      ],
                                    )
                                  : Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          const Expanded(
                                            child: Row(
                                              children: [
                                                Icon(Icons.info_outline),
                                                Gap(8),
                                                Expanded(
                                                  child: Text(
                                                      "Please fill out all required fields to submit for review"),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Expanded(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                SecondaryButton(
                                                  text: 'Save as draft',
                                                  onTap: () async {
                                                    onSubmitOrSaveDraft(false);
                                                  },
                                                ),
                                                const Gap(10),
                                                PrimaryButton(
                                                  text: 'Submit for review',
                                                  onTap: () async {
                                                    onSubmitOrSaveDraft(true);
                                                  },
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    )
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ));
  }

  Widget buildEditEventName() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          'Event name*',
          style: body2TextRegular,
        ),
        const Gap(10),
        CustomTextFormField(
          initialValue: eventController.eventDetailsModel.value.title,
          maxLength: 50,
          maxLines: 5,
          hintText: 'e.g Art attack',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Event Title Required';
            }
            return null;
          },
          onChanged: (val) {
            eventController.eventDetailsModel.value.title = val;
          },
        ),
        const Gap(30),
        Text('Description', style: body2TextRegular),
        const Gap(10),
        CustomTextFormField(
          initialValue: eventController.eventDetailsModel.value.description,
          onChanged: (val) {
            eventController.eventDetailsModel.value.description = val;
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Event Desc. Required';
            }
            return null;
          },
          hintText: "Type here",
          maxLines: 30,
          maxLength: 500,
        )
      ],
    );
  }

  Widget buildEditEventBanner() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Event banner*", style: body2TextRegular),
        const Gap(10),
        Container(
          height: screenWidth <= 820 ? 200 : 260,
          width: screenWidth <= 820 ? Get.width : null,
          margin: const EdgeInsets.only(top: 10),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.kwhite,
            border: Border.all(
              color: AppColors.kblack.withOpacity(0.2),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: (_imageUrl == null ||
                  _imageUrl!.isEmpty ||
                  _imageUrl == "null")
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.add_photo_alternate_outlined,
                        size: 60, color: AppColors.kprimarycolor),
                    const Gap(10),
                    Text(
                      "JPEG or PNG file formats supported in\n1080 x 1080px dimension, up to 1 MB",
                      style: body2TextRegular.copyWith(
                          color: AppColors.txtsecondary),
                    ),
                    const Gap(15),
                    TextButton(
                      child: Text(
                        'Upload Image',
                        textAlign: TextAlign.center,
                        style: bodyTextBold.copyWith(
                          color: const Color(0xff5E57E1),
                        ),
                      ),
                      onPressed: () async {
                        log("Upload Image button pressed");
                        FilePickerResult? result =
                            await FilePicker.platform.pickFiles();

                        if (result != null) {
                          PlatformFile pickedFile = result.files.first;
                          Uint8List? fileBytes = result.files.first.bytes;
                          log("File selected: ${pickedFile.name}");
                          await _uploadFileHelper(pickedFile, null, fileBytes!);
                        } else {
                          log('No file selected.');
                        }
                      },
                    ),
                  ],
                )
              : Stack(
                  children: [
                    Container(
                      height: 260,
                      width: screenWidth <= 820 ? Get.width : 260,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: NetworkImage(eventController
                                  .eventDetailsModel.value.bannerUrl),
                              fit: BoxFit.cover)),
                    ),
                    Positioned(
                      // top: 0,
                      bottom: 5,
                      right: 4,
                      child: InkWell(
                        hoverColor: Colors.transparent,
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        onTap: () async {
                          log("Upload Image button pressed");
                          FilePickerResult? result =
                              await FilePicker.platform.pickFiles();

                          if (result != null) {
                            PlatformFile pickedFile = result.files.first;
                            Uint8List? fileBytes = result.files.first.bytes;
                            log("File selected: ${pickedFile.name}");
                            await _uploadFileHelper(
                                pickedFile, null, fileBytes!);
                          } else {
                            log('No file selected.');
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppColors.kprimarycolor,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Icon(
                            Icons.edit,
                            size: 20,
                            color: AppColors.kwhite,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
      ],
    );
  }

  Widget buildEditStartTime(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: DateTimeWidget(
        title1: "Start Date*",
        title2: "Start Time*",
        onTapDate: () {
          DateTime now = DateTime.now();
          DateTime minDate = now.add(const Duration(days: 2));

          showDatePicker(
                  context: context,
                  initialDate: minDate,
                  firstDate: minDate,
                  lastDate: DateTime(2101),
                  barrierDismissible: false)
              .then((selectedDate) {
            if (selectedDate != null) {
              setState(() {
                DateTime currentStartDateTime = DateTime.parse(
                    eventController.eventDetailsModel.value.startDate);
                DateTime newStartDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  currentStartDateTime.hour,
                  currentStartDateTime.minute,
                );
                eventController.eventDetailsModel.value.startDate =
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(newStartDateTime);
                eventController.update();
              });
            }
          });
        },
        onTapTime: () {
          eventController.selectTimeFn(context, true).then((value) {
            setState(() {
              DateTime originalDate = DateTime.parse(formatDate(
                  format: "yyyy-MM-dd",
                  date: DateTime.parse(
                      eventController.eventDetailsModel.value.startDate)));
              List<String> parts = eventController.startTime.value.split(':');
              int hours = int.tryParse(parts[0]) ?? 0;
              int minutes = int.tryParse(parts[1]) ?? 0;
              Duration timeToAdd = Duration(hours: hours, minutes: minutes);
              DateTime newDateTime = originalDate.add(timeToAdd);

              String formattedDateTime =
                  DateFormat('yyyy-MM-dd HH:mm:ss').format(newDateTime);
              eventController.eventDetailsModel.value.startDate =
                  formattedDateTime;
              eventController.update();
            });
          });
        },
        selectedDate: eventController.eventDetailsModel.value.startDate,
        selectedTime: eventController.eventDetailsModel.value.startDate,
      ),
    );
  }

  Widget buildEditEndDateTime(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: DateTimeWidget(
        title1: "End Date*",
        title2: "End Time*",
        onTapDate: () {
          DateTime startDate =
              DateTime.parse(eventController.eventDetailsModel.value.startDate);
          DateTime endDate =
              DateTime.parse(eventController.eventDetailsModel.value.endDate);
          showDatePicker(
            context: context,
            initialDate: endDate.isAfter(startDate)
                ? endDate
                : startDate.add(const Duration(days: 1)),
            firstDate: startDate,
            lastDate: DateTime(2101),
            barrierDismissible: false,
          ).then((selectedDate) {
            if (selectedDate != null) {
              setState(() {
                DateTime currentEndDateTime = DateTime.parse(
                    eventController.eventDetailsModel.value.endDate);
                DateTime newEndDateTime = DateTime(
                  selectedDate.year,
                  selectedDate.month,
                  selectedDate.day,
                  currentEndDateTime.hour,
                  currentEndDateTime.minute,
                );
                eventController.eventDetailsModel.value.endDate =
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(newEndDateTime);
                eventController.update();
              });
            }
          });
        },
        onTapTime: () {
          DateTime startDateTime =
              DateTime.parse(eventController.eventDetailsModel.value.startDate);
          DateTime currentEndDateTime =
              DateTime.parse(eventController.eventDetailsModel.value.endDate);

          DateTime minEndDateTime =
              startDateTime.add(const Duration(minutes: 30));

          TimeOfDay initialTime = currentEndDateTime.isAfter(minEndDateTime)
              ? TimeOfDay.fromDateTime(currentEndDateTime)
              : TimeOfDay.fromDateTime(minEndDateTime);

          showTimePicker(
            context: context,
            initialTime: initialTime,
            barrierDismissible: false,
            builder: (BuildContext context, Widget? child) {
              return MediaQuery(
                data: MediaQuery.of(context)
                    .copyWith(alwaysUse24HourFormat: false),
                child: child!,
              );
            },
          ).then((TimeOfDay? timeOfDay) {
            if (timeOfDay != null) {
              setState(() {
                DateTime selectedEndDateTime = DateTime(
                  currentEndDateTime.year,
                  currentEndDateTime.month,
                  currentEndDateTime.day,
                  timeOfDay.hour,
                  timeOfDay.minute,
                );
                if (selectedEndDateTime.isBefore(minEndDateTime)) {
                  selectedEndDateTime = minEndDateTime;
                }
                eventController.eventDetailsModel.value.endDate =
                    DateFormat('yyyy-MM-dd HH:mm:ss')
                        .format(selectedEndDateTime);
                eventController.update();
              });
            }
          });
        },
        selectedDate: eventController.eventDetailsModel.value.endDate,
        selectedTime: eventController.eventDetailsModel.value.endDate,
      ),
    );
  }

  // Widget buildEditStartTime(BuildContext context) {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: ShimmerWidget(
  //       isLoading: eventController.isStartDateLoading.value,
  //       child: DateTimeWidget(
  //         title1: "Start Date*",
  //         title2: "Start Time*",
  //         onTapDate: () {
  //           eventController.selectDateFn(context, true).then((value) {
  //             setState(() {
  //               eventController.eventDetailsModel.value.startDate =
  //                   eventController.startDate.value;
  //             });
  //           });
  //         },
  //         onTapTime: () {
  //           eventController.selectTimeFn(context, true).then((value) {
  //             setState(() {
  //               DateTime originalDate = DateTime.parse(formatDate(
  //                   format: "yyyy-MM-dd",
  //                   date: DateTime.parse(
  //                       eventController.eventDetailsModel.value.startDate)));
  //               List<String> parts = eventController.startTime.value.split(':');
  //               int hours = int.tryParse(parts[0]) ?? 0;
  //               int minutes = int.tryParse(parts[1]) ?? 0;
  //               Duration timeToAdd = Duration(hours: hours, minutes: minutes);
  //               DateTime newDateTime = originalDate.add(timeToAdd);

  //               String formattedDateTime =
  //                   DateFormat('yyyy-MM-dd HH:mm:ss').format(newDateTime);
  //               eventController.eventDetailsModel.value.startDate =
  //                   formattedDateTime;
  //             });
  //           });
  //         },
  //         selectedDate: eventController.eventDetailsModel.value.startDate,
  //         selectedTime: eventController.eventDetailsModel.value.startDate,
  //       ),
  //     ),
  //   );
  // }

  // Widget buildEditEndDateTime(BuildContext context) {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: ShimmerWidget(
  //       isLoading: eventController.isEndDateLoading.value,
  //       child: DateTimeWidget(
  //         title1: "End Date*",
  //         title2: "End Time*",
  //         onTapDate: () {
  //           eventController.selectDateFn(context, false).then((value) {
  //             setState(() {
  //               eventController.eventDetailsModel.value.endDate =
  //                   eventController.endDate.value;
  //             });
  //           });
  //         },
  //         onTapTime: () {
  //           DateTime startDateTime = DateTime.parse(
  //               eventController.eventDetailsModel.value.startDate);
  //           DateTime minEndDateTime = startDateTime.add(
  //               const Duration(minutes: 30)); // 30 minutes after the start time

  //           showTimePicker(
  //             context: context,
  //             initialTime: TimeOfDay.fromDateTime(minEndDateTime),
  //             builder: (BuildContext context, Widget? child) {
  //               return MediaQuery(
  //                 data: MediaQuery.of(context)
  //                     .copyWith(alwaysUse24HourFormat: false),
  //                 child: child!,
  //               );
  //             },
  //           ).then((TimeOfDay? timeOfDay) {
  //             if (timeOfDay != null) {
  //               setState(() {
  //                 DateTime selectedEndDateTime = DateTime(
  //                   startDateTime.year,
  //                   startDateTime.month,
  //                   startDateTime.day,
  //                   timeOfDay.hour,
  //                   timeOfDay.minute,
  //                 );
  //                 if (selectedEndDateTime.isBefore(minEndDateTime)) {
  //                   selectedEndDateTime = minEndDateTime;
  //                 }
  //                 String formattedDateTime = DateFormat('yyyy-MM-dd HH:mm')
  //                     .format(selectedEndDateTime);
  //                 eventController.eventDetailsModel.value.endDate =
  //                     formattedDateTime;
  //                 log(formattedDateTime);
  //               });
  //             }
  //           });
  //         },
  //         selectedDate: eventController.eventDetailsModel.value.endDate,
  //         selectedTime: eventController.eventDetailsModel.value.endDate,
  //       ),
  //     ),
  //   );
  // }

  Widget buildEditDurationDisplay() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Duration", style: body2TextRegular),
          const Gap(10),
          GetBuilder<EventController>(
            builder: (controller) {
              _updateDuration();
              int durationMinutes = widget.isEdit
                  ? controller.eventDetailsModel.value.duration
                  : controller.eventDetailsModel.value.duration;
              String durationText =
                  _formatDuration(Duration(minutes: durationMinutes));
              return Text(
                durationText,
                style: body2TextRegular.copyWith(fontWeight: FontWeight.bold),
              );
            },
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    int days = duration.inDays;
    int hours = duration.inHours % 24;
    int minutes = duration.inMinutes % 60;

    List<String> parts = [];

    if (days > 0) {
      parts.add('$days ${days == 1 ? 'day' : 'days'}');
    }
    if (hours > 0 || days > 0) {
      parts.add('$hours ${hours == 1 ? 'hour' : 'hours'}');
    }
    if (minutes > 0 || hours > 0 || days > 0) {
      parts.add('$minutes ${minutes == 1 ? 'minute' : 'minutes'}');
    }

    if (parts.isEmpty) {
      return 'Less than a minute';
    }

    return parts.join(' ');
  }

  // Widget buildEditDurationDropDown() {
  //   double screenWidth = MediaQuery.of(context).size.width;
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text("Duration (in mins.)*", style: body2TextRegular),
  //         const Gap(10),
  //         SizedBox(
  //           width: screenWidth <= 820 ? Get.width / 2.5 : Get.width / 6.2,
  //           child: CustomDropDown(
  //             items: eventController.duration.map((e) => e.toString()).toList(),

  //                  initialValue: eventController.eventDetailsModel.value.duration.toString(),

  //             onChanged: (value) {
  //               eventController.selectedDuration = int.parse(value!);
  //               eventController.eventDetailsModel.update((val) {
  //                 val!.duration = int.parse(value);
  //               });
  //             },
  //             onSaved: (value) {},
  //             onTap: () {},
  //             validator: (value) {
  //               if (value == null || value.isEmpty) {
  //                 return 'Duration Required';
  //               }
  //               return null;
  //             },
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildAgeGroupSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("For age group*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: CustomDropdownFormField(
                  items: _ageOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age, style: bodyTextMedium),
                    );
                  }).toList(),
                  value: _selectedMinAge,
                  hintText: 'Min.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMinAge = value;
                      eventController.eventDetailsModel.value.minAge =
                          int.parse(value!);
                      log("Selected min age: $value");
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Min Age Required';
                    }
                    return null;
                  },
                ),
              ),
              const Gap(10),
              Text(
                'To',
                style: body3TextRegular.copyWith(color: AppColors.txtsecondary),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: CustomDropdownFormField(
                  items: _maxAgeOptions.map((age) {
                    return DropdownMenuItem(
                      value: age,
                      child: Text(age),
                    );
                  }).toList(),
                  value: _selectedMaxAge,
                  hintText: 'Max.age',
                  onChanged: (value) {
                    setState(() {
                      _selectedMaxAge = value;

                      if (_selectedMaxAge == "18+") {
                        eventController.eventDetailsModel.value.maxAge = 19;
                        log("Selected max age: 19");
                      } else {
                        try {
                          int selectedMaxAge = int.parse(value!);
                          int selectedMinAge = _selectedMinAge == "18+"
                              ? 18
                              : int.parse(_selectedMinAge!);

                          if (selectedMaxAge <= selectedMinAge) {
                            CustomSnackBar.showError(
                              "Error",
                              "The maximum age should be greater than the minimum age.",
                            );
                          } else {
                            eventController.eventDetailsModel.value.maxAge =
                                selectedMaxAge;
                            log("Selected max age: ${eventController.eventDetailsModel.value.maxAge}");
                          }
                        } catch (e) {
                          log("Error parsing age value: $e");
                          CustomSnackBar.showError(
                            "Error",
                            "Invalid age value.",
                          );
                        }
                      }
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Max Age Required';
                    } else if (_selectedMinAge == "18+" && value == "18+") {
                      return null;
                    } else if (value == "18+") {
                      return null;
                    } else {
                      try {
                        int selectedMaxAge = int.parse(value);
                        int selectedMinAge = _selectedMinAge == "18+"
                            ? 18
                            : int.parse(_selectedMinAge!);

                        if (selectedMaxAge <= selectedMinAge) {
                          return "The maximum age should be greater than the minimum age.";
                        }
                      } catch (e) {
                        return "Invalid age value.";
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget builEditdEventTypeSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Event type*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = true;
                      isOnlineSelected = false;
                      isOfflineOnlineSelected = false;
                      eventType = "offline";
                      eventController.eventDetailsModel.value.eventType =
                          eventType;
                      selectedCity = "";
                      selectedAddress = "";
                      // eventController.eventTypeController.value.text = eventType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                        right: Radius.zero,
                      ),
                      color: isOfflineSelected
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isOfflineSelected
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text("Offline",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOfflineOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOfflineOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = false;
                      isOnlineSelected = true;
                      isOfflineOnlineSelected = false;
                      eventType = "online";
                      eventController.eventDetailsModel.value.eventType =
                          eventType;
                      // eventController.eventTypeController.value.text = eventType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                        color: isOnlineSelected
                            ? AppColors.kwhite
                            : AppColors.scaffoldColor,
                        border: Border.all(
                          color: isOnlineSelected
                              ? AppColors.txtprimary
                              : AppColors.bordergrey,
                        )),
                    child: Center(
                      child: Text("Online",
                          style: body2TextMedium.copyWith(
                              fontWeight: isOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Flexible(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isOfflineSelected = false;
                      isOnlineSelected = false;
                      isOfflineOnlineSelected = true;
                      eventType = "offline + online";
                      eventController.eventDetailsModel.value.eventType =
                          eventType;
                      // eventController.eventTypeController.value.text = eventType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(8),
                      ),
                      color: isOfflineOnlineSelected
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                          color: isOfflineOnlineSelected
                              ? AppColors.txtprimary
                              : AppColors.bordergrey),
                    ),
                    child: Center(
                      child: Text("Offline + Online",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: body2TextMedium.copyWith(
                              fontWeight: isOfflineOnlineSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isOfflineOnlineSelected
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Widget _buildEventCity() {
  //   return Padding(
  //     padding: const EdgeInsets.all(16),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         // Text("City*", style: body2TextRegular),
  //         // const Gap(10),
  //         CustomDropdownUI(
  //           isValidatorReq: true,
  //           title: "City",
  //           items: eventController.serviceableCity.map((city) {
  //             return DropdownMenuItem(
  //               onTap: () {
  //                 eventController.eventDetailsModel.value.city = city;
  //               },
  //               value: city,
  //               child: Text(
  //                 city,
  //                 style: body2TextBold,
  //               ),
  //             );
  //           }).toList(),
  //           value: selectedCity.isNotEmpty ? selectedCity : null,
  //           onChanged: (value) {
  //             setState(() {
  //               selectedCity = value.toString();
  //               selectedCity = "";
  //             });
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget buildEventAddress() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomDropdownUI(
            isValidatorReq: true,
            title: "Address",
            items: [
              ...businessController.userModel.value.location.map((location) {
                return DropdownMenuItem(
                  onTap: () {
                    var locationDetails = LocationDetails(
                        area: location.area,
                        city: location.city,
                        state: location.state,
                        address: location.address,
                        country: location.country,
                        latitude: double.parse(location.latitude),
                        pinCode: location.pinCode,
                        longitude: double.parse(location.longitude),
                        locationId: location.locationId,
                        subLocality: location.subLocality);
                    eventController.eventDetailsModel.value.city =
                        location.city;
                    eventController.eventDetailsModel.value.locationDetails =
                        locationDetails;

                    eventController.eventDetailsModel.value.address =
                        location.address;
                  },
                  value: location.address,
                  child: Text(
                    location.address.toString(),
                    style: body2TextBold,
                  ),
                );
              }).toList(),
              DropdownMenuItem(
                value: 'add_new_address',
                child: Row(
                  children: [
                    Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Add new address',
                      style: body2TextBold.copyWith(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            value: selectedAddress.isNotEmpty ? selectedAddress : null,
            onChanged: (value) {
              if (value == 'add_new_address') {
                FocusScope.of(context).unfocus();

                Future.delayed(const Duration(milliseconds: 100), () {
                  locator<NavigationServices>()
                      .navigateTo(
                    addAddressPage,
                    arguments: const ProfileAddAddress(isEditing: false),
                  )
                      .then((result) async {
                    await businessController.businessProfileDetails();
                    businessController.userModel.refresh();
                    if (businessController
                        .userModel.value.location.isNotEmpty) {
                      var lastLocation =
                          businessController.userModel.value.location.last;
                      selectedAddress = lastLocation.address;
                      var locationDetails = LocationDetails(
                        area: lastLocation.area,
                        city: lastLocation.city,
                        state: lastLocation.state,
                        address: lastLocation.address,
                        country: lastLocation.country,
                        latitude: double.parse(lastLocation.latitude),
                        pinCode: lastLocation.pinCode,
                        longitude: double.parse(lastLocation.longitude),
                        locationId: lastLocation.locationId,
                        subLocality: lastLocation.subLocality,
                      );

                      eventController.eventDetailsModel.value.city =
                          lastLocation.city;
                      eventController.eventDetailsModel.value.locationDetails =
                          locationDetails;
                      eventController.eventDetailsModel.value.address =
                          lastLocation.address;
                    } else {
                      selectedAddress = '';
                    }
                    if (mounted) {
                      setState(() {});
                    }
                  });
                });
                return;
              }

              if (value == 'separator') {
                return;
              }
              var selectedLocation = businessController.userModel.value.location
                  .firstWhere((location) => location.address == value);

              var locationDetails = LocationDetails(
                  area: selectedLocation.area,
                  city: selectedLocation.city,
                  state: selectedLocation.state,
                  address: selectedLocation.address,
                  country: selectedLocation.country,
                  latitude: double.parse(selectedLocation.latitude),
                  pinCode: selectedLocation.pinCode,
                  longitude: double.parse(selectedLocation.longitude),
                  locationId: selectedLocation.locationId,
                  subLocality: selectedLocation.subLocality);

              eventController.eventDetailsModel.value.city =
                  selectedLocation.city;
              eventController.eventDetailsModel.value.locationDetails =
                  locationDetails;
              eventController.eventDetailsModel.value.address =
                  selectedLocation.address;

              setState(() {
                selectedAddress = value.toString();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget buildEditTicketTypeSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Ticket Type*", style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isTicketPaid = true;
                      isTicketFree = false;
                      ticketType = "paid";
                      eventController.eventDetailsModel.value.ticketType =
                          ticketType;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(12),
                      ),
                      color: isTicketPaid
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketPaid
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Paid",
                        style: body2TextMedium
                          ..copyWith(
                              fontWeight: isTicketPaid
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isTicketPaid
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isTicketPaid = false;
                      isTicketFree = true;
                      ticketType = "free";
                      eventController.eventDetailsModel.value.ticketType =
                          ticketType;
                      if (isTicketFree) {
                        eventController.eventDetailsModel.value.price = 0;
                      }
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isTicketFree
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isTicketFree
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        "Free",
                        style: body2TextMedium.copyWith(
                            fontWeight: isTicketFree
                                ? FontWeight.w600
                                : FontWeight.w500,
                            color: isTicketFree
                                ? AppColors.txtprimary
                                : AppColors.txtsecondary),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildMultipleTicketPricing() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Do you have multiple ticket pricing?*",
              style: body2TextRegular),
          const Gap(8),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isMultipleTicketYes = true;
                      isMultipleTicketNo = false;
                      multipleBooking = "yes";
                      eventController.eventDetailsModel.value.multiplePricing =
                          multipleBooking;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        left: Radius.circular(8),
                      ),
                      color: isMultipleTicketYes
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isMultipleTicketYes
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text("Yes",
                          style: body2TextMedium.copyWith(
                              fontWeight: isMultipleTicketYes
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isMultipleTicketYes
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  hoverColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  overlayColor: MaterialStateProperty.all(Colors.transparent),
                  onTap: () {
                    setState(() {
                      isMultipleTicketYes = false;
                      isMultipleTicketNo = true;
                      multipleBooking = "no";
                      eventController.eventDetailsModel.value.multiplePricing =
                          multipleBooking;
                    });
                  },
                  child: Container(
                    height: 42,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(
                        right: Radius.circular(12),
                      ),
                      color: isMultipleTicketNo
                          ? AppColors.kwhite
                          : AppColors.scaffoldColor,
                      border: Border.all(
                        color: isMultipleTicketNo
                            ? AppColors.txtprimary
                            : AppColors.bordergrey,
                      ),
                    ),
                    child: Center(
                      child: Text("No",
                          style: body2TextMedium.copyWith(
                              fontWeight: isMultipleTicketNo
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isMultipleTicketNo
                                  ? AppColors.txtprimary
                                  : AppColors.txtsecondary)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void onSubmitOrSaveDraft(bool isSubmit) async {
    if (_formKey.currentState?.validate() ?? false) {
      if (_imageUrl == null || _imageUrl!.isEmpty || _imageUrl == "null") {
        CustomSnackBar.showError(
          "Error",
          "Event banner image is required.",
        );
        return;
      }
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return ConfirmPopup(
            dialogHeight: isSubmit ? 220 : 190,
            dialogWidth: 300,
            title: isSubmit ? 'Submit for review' : 'Save as draft',
            message: isSubmit
                ? 'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.'
                : 'Are you sure you want to save this as a draft?',
            onConfirm: () async {
              log(isSubmit ? "Submit for review" : "Save as draft");
              Map<String, dynamic> duplicateEventPayload = isSubmit
                  ? {
                      "business_id": int.parse(
                        storage.read("USER_ID"),
                      ),
                      "title": eventController.eventDetailsModel.value.title,
                      "banner_url": _imageUrl,
                      "price": eventController.eventDetailsModel.value.price,
                      "description":
                          eventController.eventDetailsModel.value.description,
                      "duration":
                          eventController.eventDetailsModel.value.duration,
                      "cta_url": eventController.eventDetailsModel.value.ctaUrl,
                      "event_type":
                          eventController.eventDetailsModel.value.eventType,
                      "city": eventController.eventDetailsModel.value.city,
                      "location_details": {
                        "area": eventController
                            .eventDetailsModel.value.locationDetails.area,
                        "city": eventController
                            .eventDetailsModel.value.locationDetails.city,
                        "state": eventController
                            .eventDetailsModel.value.locationDetails.state,
                        "address": eventController
                            .eventDetailsModel.value.locationDetails.address,
                        "country": eventController
                            .eventDetailsModel.value.locationDetails.country,
                        "latitude": eventController
                            .eventDetailsModel.value.locationDetails.latitude,
                        "pin_code": eventController
                            .eventDetailsModel.value.locationDetails.pinCode,
                        "longitude": eventController
                            .eventDetailsModel.value.locationDetails.longitude,
                        "location_id": eventController
                            .eventDetailsModel.value.locationDetails.locationId,
                        "sub_locality": eventController
                            .eventDetailsModel.value.locationDetails.subLocality
                      },
                      "ticket_type":
                          eventController.eventDetailsModel.value.ticketType,
                      "cta_mobile":
                          eventController.eventDetailsModel.value.ctaMobile,
                      "multiple_pricing": eventController
                          .eventDetailsModel.value.multiplePricing,
                      "min_age": eventController.eventDetailsModel.value.minAge,
                      "publish": 1,
                      "max_age": eventController.eventDetailsModel.value.maxAge,
                      "start_date":
                          eventController.eventDetailsModel.value.startDate,
                      "end_date":
                          eventController.eventDetailsModel.value.endDate,
                      "status": "inreview"
                    }
                  : {
                      "business_id": int.parse(
                        storage.read("USER_ID"),
                      ),
                      "title": eventController.eventDetailsModel.value.title,
                      "banner_url": _imageUrl,
                      "price": eventController.eventDetailsModel.value.price,
                      "description":
                          eventController.eventDetailsModel.value.description,
                      "duration":
                          eventController.eventDetailsModel.value.duration,
                      "cta_url": eventController.eventDetailsModel.value.ctaUrl,
                      "event_type":
                          eventController.eventDetailsModel.value.eventType,
                      "city": eventController.eventDetailsModel.value.city,
                      "location_details": {
                        "area": eventController
                            .eventDetailsModel.value.locationDetails.area,
                        "city": eventController
                            .eventDetailsModel.value.locationDetails.city,
                        "state": eventController
                            .eventDetailsModel.value.locationDetails.state,
                        "address": eventController
                            .eventDetailsModel.value.locationDetails.address,
                        "country": eventController
                            .eventDetailsModel.value.locationDetails.country,
                        "latitude": eventController
                            .eventDetailsModel.value.locationDetails.latitude,
                        "pin_code": eventController
                            .eventDetailsModel.value.locationDetails.pinCode,
                        "longitude": eventController
                            .eventDetailsModel.value.locationDetails.longitude,
                        "location_id": eventController
                            .eventDetailsModel.value.locationDetails.locationId,
                        "sub_locality": eventController
                            .eventDetailsModel.value.locationDetails.subLocality
                      },
                      "ticket_type":
                          eventController.eventDetailsModel.value.ticketType,
                      "cta_mobile":
                          eventController.eventDetailsModel.value.ctaMobile,
                      "multiple_pricing": eventController
                          .eventDetailsModel.value.multiplePricing,
                      "min_age": eventController.eventDetailsModel.value.minAge,
                      "publish": 0,
                      "max_age": eventController.eventDetailsModel.value.maxAge,
                      "start_date":
                          eventController.eventDetailsModel.value.startDate,
                      "end_date":
                          eventController.eventDetailsModel.value.endDate,
                      "status": "draft"
                    };

              bool isSuccess = widget.isDuplicateEvent
                  ? await eventController.createEvent(duplicateEventPayload)
                  : await eventController.editEventData(
                      isApproved: 0,
                      publish: isSubmit ? 1 : 0,
                      status: isSubmit ? "in-review" : "draft");
              if (isSuccess) {
                await Future.delayed(const Duration(milliseconds: 300))
                    .then((value) {
                  Navigator.pop(Get.context!);
                  CustomSnackBar.showInfo(
                    "Success",
                    isSubmit ? "Event submited for review." : "Saved as draft.",
                  );
                  locator<NavigationServices>().goBack();
                  if (widget.isDuplicateEvent) {
                    locator<NavigationServices>().goBack();
                  }
                });
              } else {
                CustomSnackBar.showError(
                  "Failed",
                  isSubmit
                      ? "Failed to submit for review. Please try again.."
                      : "Failed to save as draft. Please try again.",
                );
              }
            },
            icon: SvgPicture.asset(
              isSubmit
                  ? 'assets/icons/Checks.svg'
                  : 'assets/icons/PencilLine.svg',
              height: 50,
              fit: BoxFit.fill,
            ),
            confirmText: isSubmit ? 'Submit' : 'Save as draft',
            cancelText: 'Cancel',
          );
        },
      );
    } else {
      CustomSnackBar.showError(
        "Error",
        "All field is required.",
      );
    }
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(fileExtension)) {
      CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png).",
      );
      return;
    }

    if (pickedFile.size > 1 * 1024 * 1024) {
      CustomSnackBar.showError(
        "Error",
        "File size exceeds 1 MB limit.",
      );
      return;
    }

    String contentType = 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "banner");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        _imageUrl = newImageUrl;
        eventController.eventDetailsModel.value.bannerUrl = newImageUrl;
        log("_imageUrl set to: $newImageUrl");
      });
      CustomSnackBar.showInfo(
        "Success",
        "Event Banner uploaded successfully",
      );
    } else {
      log("unable to upload the image");
      CustomSnackBar.showError(
        "Failed",
        "Unable to upload Banner... try again later",
      );
    }
  }
}
