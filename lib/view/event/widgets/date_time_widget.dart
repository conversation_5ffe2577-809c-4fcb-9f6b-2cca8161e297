import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';

class DateTimeWidget extends StatelessWidget {
  final Function() onTapDate;
  final Function() onTapTime;
  final String title1;
  final String title2;
  final String selectedDate;
  final String selectedTime;
  const DateTimeWidget(
      {super.key,
      required this.onTapDate,
      required this.onTapTime,
      required this.selectedDate,
      required this.selectedTime,
      required this.title1,
      required this.title2});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title1, style: body2TextRegular),
              const Gap(10),
              InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                onTap: onTapDate,
                child: Container(
                    width: Get.width,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppColors.scaffoldColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Text(
                          selectedDate == ""
                              ? 'Date'
                              : formatDate(
                                  format: "dd/MM/yyyy",
                                  date: DateTime.parse(selectedDate),
                                ),
                          style: bodyTextMedium.copyWith(
                              color: selectedDate == ""
                                  ? AppColors.placeHolder
                                  : AppColors.txtprimary),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.calendar_today_rounded,
                          size: 20,
                          color: AppColors.bottomlightgrey,
                        )
                      ],
                    )),
              ),
            ],
          ),
        ),
        const Gap(10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title2, style: body2TextRegular),
              const Gap(10),
              GestureDetector(
                onTap: onTapTime,
                child: Container(
                    width: Get.width,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppColors.scaffoldColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Text(
                          selectedTime == ""
                              ? '00:00'
                              : formatTime(selectedTime),
                          style: bodyTextSemiBold.copyWith(
                              color: selectedTime == ""
                                  ? AppColors.placeHolder
                                  : AppColors.txtprimary),
                        ),
                        const Spacer(),
                        const Icon(
                          Icons.access_time_filled_sharp,
                          size: 20,
                          color: AppColors.bottomlightgrey,
                        )
                      ],
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
