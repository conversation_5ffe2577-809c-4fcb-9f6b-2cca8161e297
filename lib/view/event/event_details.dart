import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/utils/date_utils.dart';
import 'package:parenthing_dashboard/view/classes/widget/class_details.dart';
import 'package:parenthing_dashboard/view/common_widgets/confirm_popup.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/event/widgets/event_cell.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:url_launcher/url_launcher.dart';

class EventDetailsPage extends StatefulWidget {
  const EventDetailsPage({super.key, required this.arguments});
  final Map<String, dynamic> arguments;

  @override
  State<EventDetailsPage> createState() => _EventDetailsPageState();
}

class _EventDetailsPageState extends State<EventDetailsPage> {
  final EventController eventController = Get.find<EventController>();
  int id = 0;
  String eventType = '';

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      setState(() {
        id = widget.arguments['eventID']!;
        eventType = widget.arguments['eventTab']!;
      });
      eventController.getEventDetailsData(id);
    });
  }

  @override
  void dispose() {
    super.dispose();
    eventController.clearEventData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: OrientationBuilder(
        builder: (context, orientation) {
          return _buildPage(orientation);
        },
      ),
    );
  }

  _buildPage(Orientation orientation) {
    double scrrenWidth = MediaQuery.of(context).size.width;

    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(scrollbars: false),
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Obx(
            () => eventController.isEventDetailsLoading.value
                ? scrrenWidth <= 820
                    ? const Center(child: CircularProgressIndicator.adaptive())
                    : Container(
                        width: orientation == Orientation.portrait
                            ? Get.width
                            : Get.width * .7,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: AppColors.ktertiary.withOpacity(.3),
                              width: 1.0),
                        ),
                        child: const ClassDetailsShimmer(isClass: false))
                : Container(
                    width: orientation == Orientation.portrait
                        ? Get.width
                        : Get.width * .7,
                    // padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                          color: AppColors.ktertiary.withOpacity(.3),
                          width: 1.0),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          child: Row(
                            children: [
                              InkWell(
                                hoverColor: Colors.transparent,
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                overlayColor: MaterialStateProperty.all(
                                    Colors.transparent),
                                onTap: () {
                                  locator<NavigationServices>().goBack();
                                },
                                child: SvgPicture.asset(
                                    "assets/svg/arrow-left.svg"),
                              ),
                              const Gap(16),
                              Text(
                                "Event details",
                                style: title2TextSemiBold,
                              ),
                              const Spacer(),
                              // eventType == "past"
                              //     ? const SizedBox()
                              //     : eventController.eventDetailsModel.value
                              //                 .publish ==
                              //             1
                              //         ? const SizedBox()
                              //         : scrrenWidth <= 820 ? const SizedBox.shrink() :
                              //          PrimaryButton(
                              //             textColor: businessController
                              //                         .userModel
                              //                         .value
                              //                         .kycDone ==
                              //                     0
                              //                 ? AppColors.ktertiary
                              //                 : AppColors.kwhite,
                              //             backgroundColor: businessController
                              //                         .userModel
                              //                         .value
                              //                         .kycDone ==
                              //                     0
                              //                 ? AppColors.scaffoldColor
                              //                 : AppColors.kprimarycolor,
                              //             text: "Submit for review",
                              //             onTap: () {
                              //               if (businessController
                              //                       .userModel.value.kycDone ==
                              //                   1) {
                              //                 showDialog(
                              //                   context: context,
                              //                   builder:
                              //                       (BuildContext context) {
                              //                     return ConfirmPopup(
                              //                       dialogHeight: 220,
                              //                       dialogWidth: 300,
                              //                       title: 'Submit for review',
                              //                       message:
                              //                           'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.',
                              //                       onConfirm: () async {
                              //                         log("Submit for review");
                              //                         bool isSuccess =
                              //                             await eventController
                              //                                 .editEventData(
                              //                                     publish: 1,
                              //                                     isApproved: 0,
                              //                                     status:
                              //                                         "in-review");
                              //                         if (isSuccess) {
                              //                           Navigator.pop(
                              //                               Get.context!);
                              //                           locator<NavigationServices>()
                              //                               .goBack();
                              //                         } else {
                              //                           ScaffoldMessenger.of(
                              //                                   Get.context!)
                              //                               .showSnackBar(
                              //                             const SnackBar(
                              //                                 content: Text(
                              //                                     'Failed to submit for review. Please try again.')),
                              //                           );
                              //                         }
                              //                       },
                              //                       icon: SvgPicture.asset(
                              //                         'assets/icons/Checks.svg',
                              //                         height: 50,
                              //                         fit: BoxFit.fill,
                              //                       ),
                              //                       confirmText: 'Submit',
                              //                       cancelText: 'Cancel',
                              //                     );
                              //                   },
                              //                 );
                              //               }
                              //             }),

                              eventType == "past"
                                  ? const SizedBox()
                                  : const Gap(10),
                              eventType == "past"
                                  ? const SizedBox()
                                  : Bounceable(
                                      onTap: () {
                                        // if (businessController
                                        //         .userModel.value.kycDone ==
                                        //     1) {
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (BuildContext context) {
                                            return CustomDialog(
                                              onConfirmTxt: "Yes",
                                              onCancelText: "No",
                                              imageHeight: 48,
                                              title: 'Edit this Event?',
                                              content:
                                                  'Editing a published event will send it for review again.\n\nWhile under review, the event will be temporarily removed from the app. Do you want to continue?',
                                              image:
                                                  "assets/icons/PencilSimple.svg",
                                              onConfirm: () {
                                                Navigator.pop(context);
                                                locator<NavigationServices>()
                                                    .navigateTo(
                                                  eventEditPage,
                                                  arguments: {
                                                    'isEdit': true,
                                                    'eventID': id
                                                  },
                                                ).then((value) {
                                                  eventController
                                                      .getEventDetailsData(id);
                                                });
                                              },
                                              onCancel: () {
                                                Navigator.of(context).pop();
                                              },
                                            );
                                          },
                                        );
                                        // }
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(7.0),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            border: Border.all(
                                                color: AppColors.ktertiary
                                                    .withOpacity(.3),
                                                width: 1.0)),
                                        child: SvgPicture.asset(
                                            "assets/svg/PencilSimple.svg"),
                                      ),
                                    ),
                              const Gap(10),
                              MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: PopupMenuButton<int>(
                                  useRootNavigator: false,
                                  shadowColor: Colors.transparent,
                                  color: AppColors.kwhite,
                                  surfaceTintColor: AppColors.kwhite,
                                  icon: Container(
                                    padding: const EdgeInsets.all(7.0),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                            color: AppColors.ktertiary
                                                .withOpacity(.3),
                                            width: 1.0)),
                                    child: SvgPicture.asset(
                                        "assets/svg/DotsThreeOutlineVertical.svg"),
                                  ),
                                  onSelected: (int result) {
                                    switch (result) {
                                      case 0:
                                        if (kDebugMode) {
                                          log("Duplicate selected");
                                        }
                                        // if (businessController
                                        //         .userModel.value.kycDone ==
                                        //     1) {
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (BuildContext context) {
                                            return CustomDialog(
                                              onConfirmTxt: "Yes",
                                              onCancelText: "No",
                                              imageHeight: 48,
                                              title: 'Duplicate this Event?',
                                              content:
                                                  'Are you sure you want to duplicate this Event?',
                                              image: "assets/icons/Copy.svg",
                                              onConfirm: () {
                                                Navigator.pop(context);
                                                locator<NavigationServices>()
                                                    .navigateTo(
                                                  eventEditPage,
                                                  arguments: {
                                                    'isEdit': true,
                                                    'eventID': id,
                                                    "isDuplicateEvent": true
                                                  },
                                                ).then((value) {
                                                  eventController
                                                      .getEventDetailsData(id);
                                                });
                                              },
                                              onCancel: () {
                                                Navigator.of(context).pop();
                                              },
                                            );
                                          },
                                        );
                                      // }

                                      case 1:
                                        if (kDebugMode) {
                                          log("Unpublish selected");
                                        }
                                        // if (businessController
                                        //         .userModel.value.kycDone ==
                                        //     1) {
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (BuildContext context) {
                                            return CustomDialog(
                                              onConfirmTxt: "Yes",
                                              onCancelText: "No",
                                              imageHeight: 48,
                                              title: 'Unpublish this Event?',
                                              content:
                                                  'This event will be no longer visible to the users.',
                                              image:
                                                  "assets/icons/EyeSlash.svg",
                                              onConfirm: () async {
                                                log("unpublish events");
                                                bool isSuccess =
                                                    await eventController
                                                        .editEventData(
                                                            publish: 0,
                                                            isApproved: 0,
                                                            status: "draft");
                                                if (isSuccess) {
                                                  Navigator.pop(Get.context!);
                                                  locator<NavigationServices>()
                                                      .goBack();
                                                } else {
                                                  CustomSnackBar.showError(
                                                      "Error",
                                                      'Failed to unpublish this event. Please try again.');
                                                }
                                              },
                                              onCancel: () {
                                                Navigator.of(context).pop();
                                              },
                                            );
                                          },
                                        );
                                        // }

                                        break;
                                      case 2:
                                        // if (businessController
                                        //         .userModel.value.kycDone ==
                                        //     1) {
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (BuildContext context) {
                                            return CustomDialog(
                                              onConfirmTxt: "Yes",
                                              onCancelText: "No",
                                              imageHeight: 48,
                                              title: 'Delete this Event?',
                                              content:
                                                  'Event will be deleted permanently for everyone.',
                                              image:
                                                  "assets/icons/Trash_red.svg",
                                              onConfirm: () async {
                                                Navigator.pop(context);
                                                eventController
                                                    .getEventDeleteData(id)
                                                    .then((value) {
                                                  Future.delayed(const Duration(
                                                      milliseconds: 300));
                                                  locator<NavigationServices>()
                                                      .goBack();
                                                  CustomSnackBar.showInfo(
                                                    'Success',
                                                    'Event is deleted permanently for everyone.',
                                                  );
                                                });
                                                log("event ID $id");
                                              },
                                              onCancel: () {
                                                Navigator.of(context).pop();
                                              },
                                            );
                                          },
                                        );
                                        // }

                                        break;
                                    }
                                  },
                                  itemBuilder: (BuildContext context) =>
                                      <PopupMenuEntry<int>>[
                                    PopupMenuItem<int>(
                                      value: 0,
                                      child: Row(
                                        children: [
                                          SvgPicture.asset(
                                              "assets/icons/Copy.svg"),
                                          const Gap(10),
                                          const Text('Duplicate'),
                                        ],
                                      ),
                                    ),
                                    if (eventController
                                            .eventDetailsModel.value.status ==
                                        'published')
                                      PopupMenuItem<int>(
                                        value: 1,
                                        child: Row(
                                          children: [
                                            SvgPicture.asset(
                                                "assets/icons/EyeSlash.svg"),
                                            const Gap(10),
                                            const Text('Unpublish'),
                                          ],
                                        ),
                                      ),
                                    PopupMenuItem<int>(
                                      value: 2,
                                      child: Row(
                                        children: [
                                          SvgPicture.asset(
                                              "assets/icons/Trash.svg"),
                                          const Gap(10),
                                          const Text('Delete'),
                                        ],
                                      ),
                                    ),
                                  ],
                                  position: PopupMenuPosition.under,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary
                              .withOpacity(.3), // Set the color of the divider
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 8.0, horizontal: 18),
                          child: Column(
                            children: [
                              if (eventController
                                      .eventDetailsModel.value.reason !=
                                  "")
                                Column(
                                  children: [
                                    Container(
                                      width: Get.width,
                                      padding: const EdgeInsets.all(16),
                                      decoration: const BoxDecoration(
                                        color: AppColors.lightPink,
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(12),
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              "Event submission has been rejected.",
                                              style: bodyTextBold),
                                          const Gap(5),
                                          Text(
                                              "Reason: ${eventController.eventDetailsModel.value.reason}",
                                              style: body2TextRegular),
                                          const Gap(10),
                                          Row(
                                            children: [
                                              const Icon(
                                                Icons.info_outline,
                                                color: AppColors.txtsecondary,
                                              ),
                                              const Gap(6),
                                              Expanded(
                                                child: Text(
                                                  "This banner will disappear after the event/ class is resubmitted for review.",
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  maxLines: 3,
                                                  style:
                                                      body3TextRegular.copyWith(
                                                          color: AppColors
                                                              .txtsecondary),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Gap(30),
                                  ],
                                ),
                              scrrenWidth <= 820
                                  ? _buildMobileView()
                                  : _buildDesktopView()
                            ],
                          ),
                        ),
                        eventType == "past"
                            ? const SizedBox()
                            : eventController.eventDetailsModel.value.publish ==
                                    1
                                ? const SizedBox()
                                : Column(
                                    children: [
                                      Divider(
                                        thickness: 1.0,
                                        color: AppColors.ktertiary.withOpacity(
                                            .3), // Set the color of the divider
                                      ),
                                      Row(
                                        children: [
                                          const Spacer(),
                                          PrimaryButton(
                                            textColor:
                                                // businessController
                                                //             .userModel
                                                //             .value
                                                //             .kycDone ==
                                                //         0
                                                //     ? AppColors.ktertiary
                                                //     :
                                                AppColors.kwhite,
                                            backgroundColor:
                                                // businessController
                                                //             .userModel
                                                //             .value
                                                //             .kycDone ==
                                                //         0
                                                //     ? AppColors.scaffoldColor
                                                //     :
                                                AppColors.kprimarycolor,
                                            text: "Submit for review",
                                            onTap: () {
                                              // if (businessController.userModel
                                              //         .value.kycDone ==
                                              //     1) {
                                              showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return ConfirmPopup(
                                                    dialogHeight: 220,
                                                    dialogWidth: 300,
                                                    title: 'Submit for review',
                                                    message:
                                                        'Please ensure the details you entered are\ncorrect. Once submitted, your event will be\nreviewed and published upon approval.',
                                                    onConfirm: () async {
                                                      log("Submit for review");
                                                      bool isSuccess =
                                                          await eventController
                                                              .editEventData(
                                                                  publish: 1,
                                                                  isApproved: 0,
                                                                  status:
                                                                      "in-review");
                                                      if (isSuccess) {
                                                        Navigator.pop(
                                                            Get.context!);
                                                        locator<NavigationServices>()
                                                            .goBack();
                                                      } else {
                                                        CustomSnackBar.showError(
                                                            "Error",
                                                            'Failed to submit for review. Please try again.');
                                                      }
                                                    },
                                                    icon: SvgPicture.asset(
                                                      'assets/icons/Checks.svg',
                                                      height: 50,
                                                      fit: BoxFit.fill,
                                                    ),
                                                    confirmText: 'Submit',
                                                    cancelText: 'Cancel',
                                                  );
                                                },
                                              );
                                              // }
                                            },
                                          ),
                                          const Gap(20),
                                        ],
                                      ),
                                      const Gap(10),
                                    ],
                                  ),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  _buildMobileView() {
    // double scrrenWidth = MediaQuery.of(context).size.width;
    return Obx(
      () => Column(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.0),
            child: CachedNetworkImage(
              imageUrl: eventController.eventDetailsModel.value.bannerUrl,
              height: Get.height * .25,
              width: Get.width,
              fit: BoxFit.cover,
              placeholder: (context, url) => Image.asset(
                  "assets/png/Banner_Placeholder.png",
                  height: 200,
                  width: Get.width,
                  fit: BoxFit.cover),
              errorWidget: (context, url, error) => Image.asset(
                  "assets/png/Banner_Placeholder.png",
                  height: 200,
                  width: Get.width,
                  fit: BoxFit.cover),
            ),
          ),
          const Gap(20),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              eventController.eventDetailsModel.value.title,
              style: title3TextBold,
            ),
          ),
          const Gap(15),
          Wrap(
            runSpacing: 5,
            spacing: 5,
            children: [
              eventController.eventDetailsModel.value.eventType == "online"
                  ? const EventRowChild(
                      iconPath: "assets/icons/VideoConference.svg",
                      textTitle: "Online")
                  : EventRowChild(
                      iconPath: "assets/svg/MapPin.svg",
                      textTitle:
                          eventController.eventDetailsModel.value.eventType),
              const Gap(10),
              EventRowChild(
                iconPath: "assets/svg/Baby.svg",
                textTitle:
                    "${eventController.eventDetailsModel.value.minAge}-${eventController.eventDetailsModel.value.maxAge.toString().replaceAll("19", "18+")} years",
              ),
              const Gap(10),
              EventRowChild(
                  iconPath: "assets/svg/ClockClockwise.svg",
                  textTitle: formatDuration2(
                      eventController.eventDetailsModel.value.duration)
                  // "${eventController.eventDetailsModel.value.duration} mins",
                  ),
            ],
          ),
          const Gap(15),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "About event",
                style: bodyTextBold,
              ),
              Text(
                eventController.eventDetailsModel.value.description,
                style: body2TextMedium,
              ),
              // const Gap(10),
              // Divider(
              //   thickness: 1.0,
              //   color: AppColors.ktertiary.withOpacity(.3),
              // ),
              // EventColumChild(
              //   subTitle: eventType.capitalizeFirst.toString(),
              //   title: "Event mode",
              // ),
              Divider(
                thickness: 1.0,
                color: AppColors.ktertiary
                    .withOpacity(.3), // Set the color of the divider
              ),
              EventColumChild(
                subTitle:
                    '${dateFormat(eventController.eventDetailsModel.value.startDate)} ${extractTime(eventController.eventDetailsModel.value.startDate)}',
                title: "Start Date",
              ),
              Divider(
                thickness: 1.0,
                color: AppColors.ktertiary
                    .withOpacity(.3), // Set the color of the divider
              ),
              EventColumChild(
                subTitle:
                    '${dateFormat(eventController.eventDetailsModel.value.endDate)} ${extractTime(eventController.eventDetailsModel.value.endDate)}',
                title: "End Date",
              ),
              Divider(
                thickness: 1.0,
                color: AppColors.ktertiary
                    .withOpacity(.3), // Set the color of the divider
              ),
              EventColumChild(
                subTitle: eventController.eventDetailsModel.value.price == 0
                    ? "Free"
                    : "₹${eventController.eventDetailsModel.value.price} ${eventController.eventDetailsModel.value.price == 1 ? '' : 'onwards'}",
                title: "Price",
              ),
              eventController.eventDetailsModel.value.ctaMobile == ""
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary.withOpacity(.3),
                        ),
                        EventColumChild(
                          subTitle: eventController
                              .eventDetailsModel.value.ctaUrl
                              .toString(),
                          onTap: () {
                            _launchURL(
                                eventController.eventDetailsModel.value.ctaUrl,
                                false);
                          },
                          title: "Booking Url",
                        ),
                      ],
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary
                              .withOpacity(.3), // Set the color of the divider
                        ),
                        InkWell(
                          hoverColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          overlayColor:
                              MaterialStateProperty.all(Colors.transparent),
                          onTap: () => _launchURL(
                              eventController.eventDetailsModel.value.ctaMobile,
                              true),
                          child: EventColumChild(
                            subTitle:
                                "+91-${eventController.eventDetailsModel.value.ctaMobile}",
                            title: "Contact",
                          ),
                        ),
                      ],
                    ),
              eventController.eventDetailsModel.value.eventType == "online"
                  ? const SizedBox.shrink()
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary
                              .withOpacity(.3), // Set the color of the divider
                        ),
                        EventColumChild(
                          subTitle:
                              eventController.eventDetailsModel.value.city,
                          title: "City",
                        ),
                        Divider(
                          thickness: 1.0,
                          color: AppColors.ktertiary
                              .withOpacity(.3), // Set the color of the divider
                        ),
                        EventColumChild(
                          subTitle: eventController
                              .eventDetailsModel.value.locationDetails.address
                              .replaceFirst(RegExp(r'^.*?,\s*'), ''),
                          title: "Adress",
                        )
                      ],
                    ),
            ],
          ),
        ],
      ),
    );
  }

  _buildDesktopView() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.0),
                  child:
                      // eventController.eventDetailsModel.value.bannerUrl.isEmpty
                      //     ? Image.asset("assets/png/Banner_Placeholder.png",
                      //         height: 450, width: 450, fit: BoxFit.cover)
                      //     : Image.network(
                      //         eventController.eventDetailsModel.value.bannerUrl,
                      //         height: 450,
                      //         width: Get.width,
                      //         fit: BoxFit.cover,
                      //       ),

                      CachedNetworkImage(
                    imageUrl: eventController.eventDetailsModel.value.bannerUrl,
                    height: 450,
                    width: Get.width,
                    fit: BoxFit.fill,
                    placeholder: (context, url) => Image.asset(
                        "assets/png/Banner_Placeholder.png",
                        height: 450,
                        width: 450,
                        fit: BoxFit.cover),
                    errorWidget: (context, url, error) => Image.asset(
                        "assets/png/Banner_Placeholder.png",
                        height: 450,
                        width: 450,
                        fit: BoxFit.cover),
                  ),
                ),
                const Gap(20),
                Text(
                  eventController.eventDetailsModel.value.title,
                  style: title3TextBold,
                ),
                const Gap(15),
                Wrap(
                  spacing: 10,
                  runSpacing: 8,
                  children: [
                    eventController.eventDetailsModel.value.eventType ==
                            "online"
                        ? const EventDetailsRowChild(
                            iconPath: "assets/icons/VideoConference.svg",
                            textTitle: "Online")
                        : EventDetailsRowChild(
                            iconPath: "assets/svg/MapPin.svg",
                            textTitle: eventController
                                .eventDetailsModel.value.eventType),
                    EventDetailsRowChild(
                      iconPath: "assets/svg/Baby.svg",
                      textTitle:
                          "${eventController.eventDetailsModel.value.minAge}-${eventController.eventDetailsModel.value.maxAge.toString().replaceAll("19", "18+")} years",
                    ),
                    EventDetailsRowChild(
                      iconPath: "assets/svg/ClockClockwise.svg",
                      textTitle: formatDuration2(
                          eventController.eventDetailsModel.value.duration),
                    ),
                  ],
                ),
                const Gap(15),
                ListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                  title: Text(
                    "About event",
                    style: title3TextSemiBold,
                  ),
                  subtitle: Text(
                    eventController.eventDetailsModel.value.description,
                    style:
                        bodyTextRegular.copyWith(color: AppColors.txtsecondary),
                  ),
                ),
              ],
            ),
          ),
          const Gap(20),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color: AppColors.ktertiary.withOpacity(.3), width: 1.0),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // EventColumChild(
                  //   subTitle: eventType.capitalizeFirst.toString(),
                  //   title: "Event mode",
                  // ),
                  // Divider(
                  //   thickness: 1.0,
                  //   color: AppColors.ktertiary
                  //       .withOpacity(.3), // Set the color of the divider
                  // ),
                  EventColumChild(
                    subTitle:
                        '${dateFormat(eventController.eventDetailsModel.value.startDate)} ${extractTime(eventController.eventDetailsModel.value.startDate)}',
                    title: "Start Date",
                  ),
                  Divider(
                    thickness: 1.0,
                    color: AppColors.ktertiary
                        .withOpacity(.3), // Set the color of the divider
                  ),
                  EventColumChild(
                    subTitle:
                        '${dateFormat(eventController.eventDetailsModel.value.endDate)} ${extractTime(eventController.eventDetailsModel.value.endDate)}',
                    title: "End Date",
                  ),
                  // Divider(
                  //   thickness: 1.0,
                  //   color: AppColors.ktertiary.withOpacity(
                  //       .3), // Set the color of the divider
                  // ),
                  // EventColumChild(
                  //   subTitle:
                  //       "${eventController.eventDetailsModel.value.duration} minuts",
                  //   title: "Timings",
                  // ),
                  Divider(
                    thickness: 1.0,
                    color: AppColors.ktertiary
                        .withOpacity(.3), // Set the color of the divider
                  ),
                  EventColumChild(
                    subTitle: eventController.eventDetailsModel.value.price == 0
                        ? "Free"
                        : "₹${eventController.eventDetailsModel.value.price} ${eventController.eventDetailsModel.value.price == 1 ? '' : 'onwards'}",
                    title: "Price",
                  ),

                  eventController.eventDetailsModel.value.ctaMobile.isEmpty
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Divider(
                              thickness: 1.0,
                              color: AppColors.ktertiary.withOpacity(
                                  .3), // Set the color of the divider
                            ),
                            EventColumChild(
                              subTitle: eventController
                                  .eventDetailsModel.value.ctaUrl
                                  .toString(),
                              onTap: () {
                                _launchURL(
                                    eventController
                                        .eventDetailsModel.value.ctaUrl,
                                    false);
                              },
                              title: "Booking Url",
                            ),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Divider(
                              thickness: 1.0,
                              color: AppColors.ktertiary.withOpacity(
                                  .3), // Set the color of the divider
                            ),
                            InkWell(
                              hoverColor: Colors.transparent,
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              overlayColor:
                                  MaterialStateProperty.all(Colors.transparent),
                              onTap: () => _launchURL(
                                  eventController
                                      .eventDetailsModel.value.ctaMobile,
                                  true),
                              child: EventColumChild(
                                subTitle:
                                    "+91-${eventController.eventDetailsModel.value.ctaMobile}",
                                title: "Contact",
                              ),
                            ),
                          ],
                        ),
                  eventController.eventDetailsModel.value.eventType == "online"
                      ? const SizedBox.shrink()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Divider(
                              thickness: 1.0,
                              color: AppColors.ktertiary.withOpacity(
                                  .3), // Set the color of the divider
                            ),
                            EventColumChild(
                              subTitle:
                                  eventController.eventDetailsModel.value.city,
                              title: "City",
                            ),
                            Divider(
                              thickness: 1.0,
                              color: AppColors.ktertiary.withOpacity(
                                  .3), // Set the color of the divider
                            ),
                            EventColumChild(
                              subTitle: eventController.eventDetailsModel.value
                                  .locationDetails.address
                                  .replaceFirst(RegExp(r'^.*?,\s*'), ''),
                              title: "Adress",
                            )
                          ],
                        ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Future<void> _launchURL(String urlString, bool isPhone) async {
    // Ensure URL has proper scheme for web URLs
    String processedUrl = urlString;
    if (!isPhone &&
        !urlString.startsWith('http://') &&
        !urlString.startsWith('https://')) {
      processedUrl = 'https://$urlString';
    }

    final Uri url = Uri.parse(processedUrl);
    final Uri launchPhone = Uri(
      scheme: 'tel',
      path: urlString,
    );
    final Uri targetUri = isPhone ? launchPhone : url;

    if (!await canLaunchUrl(targetUri)) {
      if (kIsWeb && !isPhone) {
        try {
          await launchUrl(
            targetUri,
            mode: LaunchMode.externalApplication,
            webOnlyWindowName: '_blank',
          );
          return;
        } catch (e) {
          throw Exception('Could not launch $targetUri: $e');
        }
      }
      throw Exception('Could not launch $targetUri');
    }

    // Use different launch modes based on platform
    if (kIsWeb) {
      // For web, force external browser
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
        webOnlyWindowName: '_blank',
      );
    } else {
      // For mobile platforms
      await launchUrl(
        targetUri,
        mode: LaunchMode.externalApplication,
      );
    }
  }
}
