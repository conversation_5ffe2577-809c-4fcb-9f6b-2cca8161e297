import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/screen_res.dart';
import 'package:parenthing_dashboard/view/root/mobile_view_page.dart';
import 'package:parenthing_dashboard/view/root/desktop_view_page.dart';

class LoadingAnimationPage extends StatefulWidget {
  const LoadingAnimationPage({super.key});

  @override
  State<LoadingAnimationPage> createState() => _LoadingAnimationPageState();
}

class _LoadingAnimationPageState extends State<LoadingAnimationPage> {
  final BusinessController businessVM = Get.find<BusinessController>();

  @override
  void initState() {
    super.initState();
    _loadDataAndNavigate();
  }

  _loadDataAndNavigate() async {
    await businessVM.businessProfileDetails();
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const ResponsiveWidget(
            largeScreen: RootDashboardPage(),
            mediumScreen: RootDashboardPage(),
            smallScreen: MobileViewPage(),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.kprimarycolor),
      ),
    );
  }
}
