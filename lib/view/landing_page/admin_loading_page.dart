import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_root/admin_desktop_page.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/screen_res.dart';
import 'package:parenthing_dashboard/view/root/mobile_view_page.dart';

class AdminLoadingAnimationPage extends StatefulWidget {
  const AdminLoadingAnimationPage({super.key});

  @override
  State<AdminLoadingAnimationPage> createState() =>
      _AdminLoadingAnimationPageState();
}

class _AdminLoadingAnimationPageState extends State<AdminLoadingAnimationPage> {
  final AdminUserController adminUserController =
      Get.find<AdminUserController>();
  final AdminBusinessController adminBusinessVM =
      Get.find<AdminBusinessController>();
  @override
  void initState() {
    super.initState();
    _loadDataAndNavigate();
  }

  _loadDataAndNavigate() async {
    await adminUserController.getAdminDetails();
    adminBusinessVM.getAdminPendingReview();

    await Future.delayed(const Duration(seconds: 3));

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const ResponsiveWidget(
            largeScreen: AdminDesktopViewPage(),
            mediumScreen: AdminDesktopViewPage(),
            smallScreen: MobileViewPage(),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.kprimarycolor),
      ),
    );
  }
}
