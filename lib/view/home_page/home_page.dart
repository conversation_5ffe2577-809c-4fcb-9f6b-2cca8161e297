import 'dart:developer';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/views/admin_home/widget/value_notifier.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/routing/routes.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/home_page_total_card.dart';
import 'package:parenthing_dashboard/view/home_page/widgets/kyc_popup.dart';
import 'package:url_launcher/url_launcher.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final EventController eventController = Get.find<EventController>();
  final BusinessController businessController = Get.find<BusinessController>();
  final ClassController classVM = Get.find<ClassController>();
  final UserController userController = Get.find<UserController>();
  late ThemeData themeData;

  @override
  void initState() {
    super.initState();
    _initializeAndCheckKYC().then((value) {
      userController.getHomePageImages();
      userController.getBusinessGraphData();
      // firebaseService.initializeFirebase();
      //  homePageController.getGraphData("Mumbai", "from_beginning");
      eventController.getAllEventList("published");
      classVM.getAllClassList("published");
    });
  }

  Future<void> _initializeAndCheckKYC() async {
    await businessController.businessProfileDetails();
    if (businessController.userModel.value.kycStatus == "" &&
        businessController.showKycBanner.value) {
      // bool hasShownPopup = _hasShownPopup();
      // if (!hasShownPopup) {
      //   _showCustomDialog(Get.context!);
      //   _setPopupShown();
      // }
    } else {
      log("KYC is done");
    }
  }

  bool _hasShownPopup() {
    return storage.read('has_shown_kyc_popup') ?? false;
  }

  void _setPopupShown() {
    storage.write('has_shown_kyc_popup', true);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    themeData = Theme.of(context);
  }

  void _showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CustomDialog(
          onConfirmTxt: "Proceed",
          title: 'Complete your eKYC',
          content:
              'eKYC is required to verify your business to create and publish classes and events on the Parenthing platform',
          image: "assets/icons/kyc_icon.svg",
          onConfirm: () {
            Navigator.of(context).pop();
            locator<NavigationServices>()
                .navigateTo(
              kycFormPage,
            )
                .then((value) {
              businessController.businessProfileDetails();
            });
          },
          onCancel: () {
            setState(() {
              Navigator.of(context).pop();
            });
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: OrientationBuilder(
        builder: (context, orientation) {
          return SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // _buildKycBanner(),
                  // Obx(
                  //   () =>
                  //       businessController.userModel.value.isprofileComplete ==
                  //               100
                  //           ? const SizedBox()
                  //           : const ProfileInComplete(),
                  // ),
                  const Gap(20),
                  orientation == Orientation.portrait
                      ? _buildPortraitLayout()
                      : _buildLandscapeLayout(),
                  const Gap(20),
                  Text("Tip's", style: title2TextBold),
                  const Gap(20),
                  _buildTipsSection(orientation),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Widget _buildKycBanner() {
  //   return Obx(
  //     () => businessController.showKycBanner.value
  //         ? Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               KycBannerWrapper(
  //                 businessController: businessController,
  //                 type: businessController.userModel.value.kycStatus,
  //                 onDismissTap: () {
  //                   businessController.dismissKycBanner();
  //                 },
  //               ),
  //               const Gap(20),
  //             ],
  //           )
  //         : const SizedBox.shrink(),
  //   );
  // }

  Widget _buildPortraitLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("Welcome to Parenthing", style: title2TextBold),
        const Gap(20),
        _buildStatsGrid(),
        const Gap(20),
        parentsInviteWidget(),
        const Gap(10),
        businessInviteWidget(),
      ],
    );
  }

  Widget _buildLandscapeLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Welcome to Parenthing", style: title2TextBold),
              const Gap(20),
              _buildStatsGrid(),
              const Gap(15),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: parentsInviteWidget(),
                    ),
                    const Gap(30),
                    Expanded(
                      child: businessInviteWidget(),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatsGrid() {
    double screenWidth = MediaQuery.of(context).size.width;
    return Obx(() => screenWidth <= 500
        ? _buildMobileStats()
        : Row(
            children: [
              Expanded(
                child: HomePageTotalContainer(
                  totalCount:
                      '${userController.homePageGraph.value.totalEvents}',
                  menuTitle: 'Total events',
                  onTap: () {
                    locator<NavigationServices>().navigateTo(eventRoute);
                    selectedBusinessPageNotifier.value = 'Events';
                  },
                ),
              ),
              Expanded(
                child: HomePageTotalContainer(
                  totalCount:
                      '${userController.homePageGraph.value.inreviewEvents}',
                  menuTitle: 'In-review events',
                  onTap: () {
                    locator<NavigationServices>().navigateTo(eventRoute);
                    selectedBusinessPageNotifier.value = 'Events';
                  },
                ),
              ),
              Expanded(
                child: HomePageTotalContainer(
                  totalCount:
                      '${userController.homePageGraph.value.totalClasses}',
                  menuTitle: 'Total classes',
                  onTap: () {
                    locator<NavigationServices>().navigateTo(classesRoute);
                    selectedBusinessPageNotifier.value = "Classes";
                  },
                ),
              ),
              Expanded(
                child: HomePageTotalContainer(
                  totalCount:
                      '${userController.homePageGraph.value.inreviewClasses}',
                  menuTitle: 'In-review classes',
                  onTap: () {
                    locator<NavigationServices>().navigateTo(classesRoute);
                    selectedBusinessPageNotifier.value = "Classes";
                  },
                ),
              ),
            ],
          ));
  }

  Widget _buildMobileStats() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      childAspectRatio: 1.6,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 10,
      mainAxisSpacing: 10,
      children: [
        HomePageTotalContainer(
          totalCount: '${userController.homePageGraph.value.totalEvents}',
          menuTitle: 'Total events',
          onTap: () {
            locator<NavigationServices>().navigateTo(eventRoute);
            selectedBusinessPageNotifier.value = 'Events';
          },
        ),
        HomePageTotalContainer(
          totalCount: '${userController.homePageGraph.value.inreviewEvents}',
          menuTitle: 'In-review events',
          onTap: () {
            locator<NavigationServices>().navigateTo(eventRoute);
            selectedBusinessPageNotifier.value = 'Events';
          },
        ),
        HomePageTotalContainer(
          totalCount: '${userController.homePageGraph.value.totalClasses}',
          menuTitle: 'Total classes',
          onTap: () {
            locator<NavigationServices>().navigateTo(classesRoute);
            selectedBusinessPageNotifier.value = "Classes";
          },
        ),
        HomePageTotalContainer(
          totalCount: '${userController.homePageGraph.value.inreviewClasses}',
          menuTitle: 'In-review classes',
          onTap: () {
            locator<NavigationServices>().navigateTo(classesRoute);
            selectedBusinessPageNotifier.value = "Classes";
          },
        ),
      ],
    );
  }

  Widget _buildTipsSection(Orientation orientation) {
    return Obx(() {
      if (userController.isImageLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }
      if (userController.homePageImageModel.isEmpty) {
        return const Text('No images available.');
      }
      return orientation == Orientation.portrait
          ? CarouselSlider(
              options: CarouselOptions(
                height: 450,
                aspectRatio: 16 / 9,
                viewportFraction: 0.8,
                initialPage: 0,
                enableInfiniteScroll: false,
                reverse: false,
                autoPlay: true,
                autoPlayInterval: const Duration(seconds: 3),
                autoPlayAnimationDuration: const Duration(milliseconds: 800),
                autoPlayCurve: Curves.fastOutSlowIn,
                enlargeCenterPage: true,
                scrollDirection: Axis.horizontal,
              ),
              items: userController.homePageImageModel.map((image) {
                return Builder(
                  builder: (BuildContext context) {
                    return Container(
                      width: MediaQuery.of(context).size.width,
                      margin: const EdgeInsets.symmetric(horizontal: 5.0),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CachedNetworkImage(
                          imageUrl: image.imagesUrl,
                          fit: BoxFit.contain,
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: userController.homePageImageModel.map((image) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: CachedNetworkImage(
                    imageUrl: image.imagesUrl,
                    width: Get.width * .25,
                    height: 550,
                    fit: BoxFit.cover,
                  ),
                );
              }).toList(),
            );
    });
  }

  Widget parentsInviteWidget() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(11),
        border: Border.all(color: AppColors.lgrey, width: 1.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SvgPicture.asset(
            'assets/svg/UserCirclePlus.svg',
            height: 65,
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Invite Parents',
                  style: body2TextBold,
                ),
                Text(
                  'Invite your friends and people in your circle to the Parenthing platform via WhatsApp or by copying and sharing the link.',
                  textAlign: TextAlign.start,
                  maxLines: 3,
                  style: body2TextRegular,
                ),
                const Gap(20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hi! You can now see my upcoming classes and events for your kids on Parenthing.\n\n\n Download the app here: https://play.google.com/store/apps/details?id=mobile.parenthing.app&hl=en";
                        FlutterClipboard.copy(shareContent)
                            .then((value) => Get.snackbar('', 'Copied'));
                      },
                      child: const FaIcon(
                        FontAwesomeIcons.copy,
                        size: 24.0,
                        color: AppColors.kprimarycolor,
                      ),
                    ),
                    const Gap(20),
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hi! You can now see my upcoming classes and events for your kids on Parenthing.\n\n\n Download the app here: https://play.google.com/store/apps/details?id=mobile.parenthing.app&hl=en";
                        _launchWhatsApp(shareContent);
                      },
                      child: const FaIcon(
                        FontAwesomeIcons.whatsapp,
                        size: 24.0,
                        color: AppColors.kprimarycolor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget businessInviteWidget() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(11),
        border: Border.all(color: AppColors.lgrey, width: 1.0),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/svg/Buildings_invite.svg',
          ),
          const Gap(10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Invite Business',
                  style: body2TextBold,
                ),
                Text(
                  'Invite businesses and event organisers in your network to the Parenthing platform via WhatsApp or by sharing the link.',
                  textAlign: TextAlign.start,
                  style: body2TextRegular,
                ),
                const Gap(20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hey! I'm using Parenthing to get more bookings for my class and events.\n\n\n I think it'd be great for you too:https://stag-deployment.dl8bfpe8mpm5z.amplifyapp.com/";
                        FlutterClipboard.copy(shareContent)
                            .then((value) => Get.snackbar('', 'Copid'));
                      },
                      child: const FaIcon(
                        FontAwesomeIcons.copy,
                        size: 24.0,
                        color: AppColors.kprimarycolor,
                      ),
                    ),
                    const Gap(20),
                    Bounceable(
                      onTap: () {
                        String shareContent =
                            "Hey! I'm using Parenthing to get more bookings for my class and events.\n\n\n I think it'd be great for you too:https://stag-deployment.dl8bfpe8mpm5z.amplifyapp.com/";
                        _launchWhatsApp(shareContent);
                      },
                      child: const FaIcon(
                        FontAwesomeIcons.whatsapp,
                        size: 24.0,
                        color: AppColors.kprimarycolor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Future<void> _launchWhatsApp(String message) async {
    //const phoneNumber = '+9185305 72636';
    final uri =
        Uri.parse('https://wa.me/?text=${Uri.encodeComponent(message)}');
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $uri';
      }
    } catch (e) {
      debugPrint('Error launching WhatsApp: $e');
    }
  }
}
