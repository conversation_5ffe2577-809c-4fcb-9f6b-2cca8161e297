import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'dart:math' as math;
import 'package:pie_chart/pie_chart.dart';

class EventPieChart extends StatefulWidget {
  const EventPieChart({super.key});

  @override
  State<EventPieChart> createState() => _EventPieChartState();
}

enum LegendShape { circle, rectangle }

class _EventPieChartState extends State<EventPieChart> {
  final UserController userController = Get.find<UserController>();

  @override
  void initState() {
    userController.getBusinessGraphData();
    Future.delayed(const Duration(milliseconds: 500), () {
      loadGraphData();
    });
    super.initState();
  }

  Map<String, double> dataMapEvents = {
    "Total": 0,
    "Approved": 0,
    "InReview": 0,
    "Drafts": 0,
  };

  Map<String, double> dataMapClass = {
    "Total": 0,
    "Approved": 0,
    "InReview": 0,
    "Drafts": 0,
  };

  final legendLabels = <String, String>{
    "Total": "Total",
    "Approved": "Approved",
    "InReview": "InReview",
    "Drafts": "Drafts",
  };

  final legendLabelsClass = <String, String>{
    "Total": "Total",
    "Approved": "Approved",
    "InReview": "InReview",
    "Drafts": "Drafts",
  };

  final colorList = <Color>[
    const Color(0xff5E57E1),
    const Color(0xff19D62C),
    const Color(0xffFFD941),
    const Color(0xff2E2B43),
  ];

  int key = 0;

  loadGraphData() {
    setState(() {
      dataMapEvents = <String, double>{
        "Total": userController.homePageGraph.value.totalEvents.toDouble(),
        "Approved":
            userController.homePageGraph.value.approvedEvents.toDouble(),
        "InReview":
            userController.homePageGraph.value.inreviewEvents.toDouble(),
        "Drafts": userController.homePageGraph.value.draftEvents.toDouble(),
      };

//class data
      dataMapClass = <String, double>{
        "Total": userController.homePageGraph.value.totalClasses.toDouble(),
        "Approved":
            userController.homePageGraph.value.approvedClasses.toDouble(),
        "InReview":
            userController.homePageGraph.value.inreviewClasses.toDouble(),
        "Drafts": userController.homePageGraph.value.draftClasses.toDouble(),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    final chart = PieChart(
      key: ValueKey(key),
      dataMap: dataMapEvents,
      animationDuration: const Duration(milliseconds: 800),
      chartLegendSpacing: 32.0,
      chartRadius: math.min(MediaQuery.of(context).size.width / 3.2, 300),
      colorList: colorList,
      initialAngleInDegree: 0,
      chartType: ChartType.disc,
      centerText: "",
      legendLabels: legendLabels,
      legendOptions: LegendOptions(
        showLegendsInRow: true,
        legendPosition: LegendPosition.bottom,
        legendTextStyle: bodyTextRegular,
      ),
      chartValuesOptions: const ChartValuesOptions(
        showChartValueBackground: false,
        showChartValues: true,
        showChartValuesInPercentage: false,
        showChartValuesOutside: true,
        decimalPlaces: 0,
      ),
      ringStrokeWidth: 32.0,
      emptyColor: Colors.grey,
      gradientList: null,
      emptyColorGradient: const [
        Color(0xff6c5ce7),
        Colors.blue,
      ],
      baseChartColor: Colors.transparent,
    );
//
    final chartClass = PieChart(
      key: ValueKey(key),
      dataMap: dataMapClass,
      animationDuration: const Duration(milliseconds: 800),
      chartLegendSpacing: 32.0,
      chartRadius: math.min(MediaQuery.of(context).size.width / 3.2, 300),
      colorList: colorList,
      initialAngleInDegree: 0,
      chartType: ChartType.disc,
      centerText: "",
      legendLabels: legendLabelsClass,
      legendOptions: LegendOptions(
          showLegendsInRow: true,
          legendPosition: LegendPosition.bottom,
          legendTextStyle: bodyTextRegular),
      chartValuesOptions: const ChartValuesOptions(
        showChartValueBackground: false,
        showChartValues: true,
        showChartValuesInPercentage: false,
        showChartValuesOutside: true,
        decimalPlaces: 0,
      ),
      ringStrokeWidth: 32.0,
      emptyColor: Colors.grey,
      gradientList: null,
      emptyColorGradient: const [
        Color(0xff6c5ce7),
        Colors.blue,
      ],
      baseChartColor: Colors.transparent,
    );
    return LayoutBuilder(
      builder: (_, constraints) {
        if (constraints.maxWidth >= 600) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Container(
                  padding: const EdgeInsets.all(14.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.backcolor),
                    borderRadius: BorderRadius.circular(11),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Events",
                        style: body2TextBold,
                      ),
                      chart,
                    ],
                  ),
                ),
              ),
              const Gap(20),
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Container(
                  padding: const EdgeInsets.all(14.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.backcolor),
                    borderRadius: BorderRadius.circular(11),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Classes",
                        style: body2TextBold,
                      ),
                      chartClass,
                    ],
                  ),
                ),
              ),
            ],
          );
        } else {
          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(
                    vertical: 32,
                  ),
                  child: chart,
                ),
                //settings,
              ],
            ),
          );
        }
      },
    );
  }
}
