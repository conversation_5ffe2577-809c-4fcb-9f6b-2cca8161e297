import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/secondary_button.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback onConfirm;
  final String onConfirmTxt;
  final String onCancelText;
  final VoidCallback onCancel;
  final String image;
  final double imageHeight;

 const CustomDialog({super.key, 
    required this.title,
    required this.content,
    required this.onConfirm,

    required this.onCancel, required this.image, required this.onConfirmTxt,  this.onCancelText = "I'll do it later",  this.imageHeight = 64.0,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.kwhite,
      surfaceTintColor: AppColors.kwhite,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          width: 280,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
              image,
                height: imageHeight,
              ),
              const Gap(10),
              Text(
                title,
                style: bodyTextBold,
              ),
        const    Gap(5),
              Text(
                content,
                textAlign: TextAlign.center,
                style: body2TextMedium,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 40,
                      child: SecondaryButton(text: onCancelText, onTap: onCancel,isImage: false,))
                    // CustomOutlinedButton(
                    //   text: "I'll do it later",
                    //   onPressed: onCancel,
                    //   borderColor: AppColors.ktertiary,
                    //   borderWidth: 2.0,
                    //   borderRadius: BorderRadius.circular(10.0),
                    //   textStyle: body2TextSemiBold.copyWith(
                    //       color: AppColors.kprimarycolor),
                    // ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                      child: SizedBox(
                      height: 40,
                        
                        child: PrimaryButton(text: onConfirmTxt, onTap: onConfirm)))
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
