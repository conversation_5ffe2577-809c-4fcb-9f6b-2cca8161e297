import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';

class HomePageTotalContainer extends StatelessWidget {
  final String totalCount;
  final String menuTitle;
  final void Function() onTap;

  const HomePageTotalContainer({
    super.key,
    required this.totalCount,
    required this.menuTitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
          height: 120,
          // width: Get.width,
          margin:  const EdgeInsets.all(6) ,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
            color: AppColors.kwhite,boxShadow: [
           BoxShadow(
                      color: AppColors.kgrey.withOpacity(0.4),
                      offset: const Offset(
                        1.0,
                        1.0,
                      ),
                      blurRadius: 5.0,
                      spreadRadius: 2.0,
                    ),
        ]),
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(child: Text(totalCount, style: heading2TextMedium,textAlign: TextAlign.center,)),
              const Gap(10),
              Flexible(child: Text(menuTitle, style: body2TextMedium,textAlign: TextAlign.center)),
            ],
          ),
        ),
      ),
    );
  }
}
