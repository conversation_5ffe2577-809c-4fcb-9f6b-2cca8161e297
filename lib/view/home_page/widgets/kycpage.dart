import 'dart:developer';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/controller/kyc_controller.dart';
import 'package:parenthing_dashboard/main.dart';
import 'dart:io';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/constants/gaps.dart';
import 'package:parenthing_dashboard/res/constants/style.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:parenthing_dashboard/routing/locator.dart';
import 'package:parenthing_dashboard/routing/navigation_services.dart';
import 'package:parenthing_dashboard/view/common_widgets/dropdown.dart';
import 'package:parenthing_dashboard/view/common_widgets/primary_button.dart';
import 'package:parenthing_dashboard/view/common_widgets/textformfield.dart';

class KycVerify extends StatefulWidget {
  const KycVerify({super.key});

  @override
  State<KycVerify> createState() => _KycVerifyState();
}

class _KycVerifyState extends State<KycVerify> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final EventController eventController = Get.find<EventController>();
  final KycController kycController = Get.find<KycController>();
  String selectedDocumentType = '';
  String selectedPersonalidType = '';
  BusinessController businessVM = BusinessController();
  List<String> items = [
    'Incorporation Certificate',
    'Partnership deed',
    'GST Certificate',
    "Personal id's",
  ];
  List<String> personalIdItems = [
    'Aadhaar Card',
    'Pan Card',
    'Passport',
    'Driving License',
    'Voter ID'
  ];
  String userID = "0";
  TextEditingController businessNameController = TextEditingController();
  TextEditingController registeredAddressController = TextEditingController();
  TextEditingController documentNumberController = TextEditingController();
  String? _imageUrl;

  @override
  void initState() {
    super.initState();
    fetchBusinessDetails();
    if (kDebugMode) {
      log("user id: $userID");
    }
  }

  void _resetUploadState() {
    setState(() {
      eventController.uploadedFileName.value = "";
      _imageUrl = null;
    });
  }

  Future<void> fetchBusinessDetails() async {
    await businessVM.businessProfileDetails();
    setState(() {
      businessNameController.text = businessVM.userModel.value.businessName;
      registeredAddressController.text =
          businessVM.userModel.value.officeAddress;
    });
  }

  Widget _getDocumentInputField() {
    switch (selectedDocumentType) {
      case 'Incorporation Certificate':
        return _buildBusinessIncorporationInput();
      case 'Partnership deed':
        return _buildPartnershipInput();
      case 'GST Certificate':
        return _buildGSTCertificateInput();
      case "Personal id's":
        return _buildPersonalInput();
      default:
        return Container();
    }
  }

  Widget _buildPersonalInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Personal ID Type*',
          style: rubikStyle.copyWith(
            fontSize: 15.0,
          ),
        ),
        kSmHeight,
        CustomDropdownFormField(
          items: personalIdItems
              .map((item) => DropdownMenuItem(
                    value: item,
                    child: Text(item),
                  ))
              .toList(),
          hintText: 'Select Personal ID Type',
          onChanged: (value) {
            setState(() {
              selectedPersonalidType = value!;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a Personal ID Type';
            }
            return null;
          },
        ),
        const Gap(20),
        _getPersonalIdInputField(),
      ],
    );
  }

  Widget _getPersonalIdInputField() {
    switch (selectedPersonalidType) {
      case 'Aadhaar Card':
        return CustomTextFormField(
          controller: documentNumberController,
          onChanged: (p0) {},
          maxLength: 12,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          hintText: 'Enter Aadhaar License number',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Aadhaar License number';
            }

            return null;
          },
        );
      case 'Pan Card':
        return CustomTextFormField(
          controller: documentNumberController,
          onChanged: (p0) {},
          maxLength: 10,
          hintText: 'Enter Pan Card number',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Pan Card number';
            }
            String pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]$';
            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid Pan Card number';
            }

            return null;
          },
        );
      case 'Driving License':
        return CustomTextFormField(
          controller: documentNumberController,
          onChanged: (p0) {},
          hintText: 'Enter Driving License number',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Driving License number';
            }
            String pattern = r'^[A-Z]{2}[0-9-]{13}$';
            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid Driving License number';
            }
            return null;
          },
        );
      case 'Passport':
        return CustomTextFormField(
          controller: documentNumberController,
          hintText: 'Enter Passport number',
          onChanged: (p0) {},
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter Passport number';
            }
            String pattern = r'^[A-Z][0-9]{7}$';
            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid Passport number';
            }
            return null;
          },
        );
      case 'Voter ID':
        return CustomTextFormField(
          controller: documentNumberController,
          hintText: 'Enter Voter ID number',
          onChanged: (p0) {},
          maxLength: 12,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) {
              String pattern = r'^[A-Z]{3}[0-9]{7}$';

              RegExp regExp = RegExp(pattern);
              if (!regExp.hasMatch(value!)) {
                return 'Please enter a valid Voter ID number';
              }
            }
            return null;
          },
        );
      default:
        return Container();
    }
  }

  Widget _buildBusinessIncorporationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Number',
          style: rubikStyle.copyWith(fontSize: 15.0),
        ),
        kSmHeight,
        CustomTextFormField(
          controller: documentNumberController,
          hintText: 'Type here',
          onChanged: (p0) {},
          maxLength: 21,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter CIN number';
            }
            String pattern =
                r'^[A-Z]{3}[0-9]{4}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$';

            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid CIN number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPartnershipInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Number',
          style: rubikStyle.copyWith(fontSize: 15.0),
        ),
        kSmHeight,
        CustomTextFormField(
          controller: documentNumberController,
          hintText: 'Type here',
          onChanged: (p0) {},
          maxLength: 7,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter LLPIN number';
            }
            String pattern = r'^[A-Z]{3}[0-9]{4}$';

            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid LLPIN number';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildGSTCertificateInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Number*',
          style: rubikStyle.copyWith(
            fontSize: 15.0,
          ),
        ),
        kSmHeight,
        CustomTextFormField(
          controller: documentNumberController,
          hintText: 'Type here',
          onChanged: (p0) {},
          maxLength: 15,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter GST Certificate number';
            }
            String pattern =
                r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$';

            RegExp regExp = RegExp(pattern);
            if (!regExp.hasMatch(value)) {
              return 'Please enter a valid GST Certificate number';
            }
            return null;
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Container(
            margin: EdgeInsets.only(
                right: screenWidth <= 820 ? 0 : Get.width * 0.3),
            width: Get.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: AppColors.kgrey,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                        onTap: () {
                          locator<NavigationServices>().goBack();
                        },
                        child: SvgPicture.asset(
                          'assets/icons/arrow-left.svg',
                        ),
                      ),
                      mdWidth,
                      Text("Complete your eKYC ", style: title3TextSemiBold),
                    ],
                  ),
                ),
                const Divider(
                  thickness: 1.0,
                  color: AppColors.kgrey,
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'Business Name*',
                          style: rubikStyle.copyWith(
                            fontSize: 15.0,
                            color: AppColors.kblack,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        kSmHeight,
                        CustomTextFormField(
                          controller: businessNameController,
                          onChanged: (p0) {},
                          hintText: "Enter here",
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return ('Please enter Business Name');
                            }
                            return null;
                          },
                        ),
                        kMinHeight,
                        Text(
                          'Registered Address*',
                          style: rubikStyle.copyWith(
                            fontSize: 15.0,
                            color: AppColors.kblack,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        kSmHeight,
                        CustomTextFormField(
                          controller: registeredAddressController,
                          hintText: 'Enter here',
                          onChanged: (val) {},
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return "Enter a registered address";
                            }
                            return null;
                          },
                        ),
                        kMinHeight,
                        Text(
                          'Document Type*',
                          style: rubikStyle.copyWith(
                            fontSize: 15.0,
                            color: AppColors.kblack,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        kSmHeight,
                        CustomDropdownFormField(
                          items: items
                              .map((item) => DropdownMenuItem(
                                    value: item,
                                    child: Text(item),
                                  ))
                              .toList(),
                          hintText: 'Select',
                          onChanged: (value) {
                            setState(() {
                              selectedDocumentType = value!;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a Document Type';
                            }
                            return null;
                          },
                        ),
                        kMinHeight,
                        _getDocumentInputField(),
                        const Gap(10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Upload Document*',
                              style: rubikStyle.copyWith(
                                fontSize: 15.0,
                                color: AppColors.kblack,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            kSmHeight,
                            DottedBorder(
                              color: AppColors.bordergrey,
                              strokeWidth: 1,
                              dashPattern: const [6, 3],
                              borderType: BorderType.RRect,
                              radius: const Radius.circular(4),
                              child: Obx(() {
                                return eventController
                                        .uploadedFileName.value.isEmpty
                                    ? InkWell(
hoverColor: Colors.transparent,
  splashColor: Colors.transparent,
  highlightColor: Colors.transparent,
  overlayColor: MaterialStateProperty.all(Colors.transparent),
                                        onTap: () async {
                                          log("Upload Image button pressed");
                                          FilePickerResult? result =
                                              await FilePicker.platform
                                                  .pickFiles(
                                            type: FileType.custom,
                                            allowedExtensions: [
                                              'jpg',
                                              'jpeg',
                                              'png',
                                              'pdf'
                                            ],
                                          );

                                          if (result != null) {
                                            PlatformFile pickedFile =
                                                result.files.first;
                                            Uint8List? fileBytes =
                                                result.files.first.bytes;
                                            log("File selected: ${pickedFile.name}");
                                            await _uploadFileHelper(
                                                pickedFile, null, fileBytes!);
                                          } else {
                                            log('No file selected.');
                                          }
                                        },
                                        child: Container(
                                          height: 48,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16),
                                          child: Row(
                                            children: [
                                              SvgPicture.asset(
                                                "assets/icons/upload_picture.svg",
                                                fit: BoxFit.contain,
                                              ),
                                              const SizedBox(width: 10),
                                              Text(
                                                "Upload document",
                                                style: bodyTextBold.copyWith(
                                                  color:
                                                      AppColors.kprimarycolor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Stack(
                                        children: [
                                          Container(
                                            height: 48,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16),
                                            child: Row(
                                              children: [
                                                const Icon(
                                                  Icons.insert_drive_file,
                                                  color:
                                                      AppColors.kprimarycolor,
                                                ),
                                                const SizedBox(width: 10),
                                                Expanded(
                                                  child: Text(
                                                    eventController
                                                        .uploadedFileName.value,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style:
                                                        bodyTextBold.copyWith(
                                                      color: AppColors
                                                          .kprimarycolor,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Positioned(
                                            top: 0,
                                            right: 0,
                                            child: IconButton(
                                              icon: const Icon(Icons.close),
                                              onPressed: () {
                                                eventController.uploadedFileName
                                                    .value = "";
                                              },
                                            ),
                                          ),
                                        ],
                                      );
                              }),
                            ),
                            const Gap(5),
                            Text(
                              'Accepted formats: PDF, JPEG, PNG - up to 2 MB',
                              style: rubikStyle.copyWith(
                                fontSize: 12.0,
                                fontWeight: FontWeight.w300,
                              ),
                            ),
                          ],
                        ),
                        const Gap(15),
                        const Divider(
                          thickness: 1.0,
                          color: AppColors.kgrey,
                        ),
                        const Gap(15),
                        Align(
                          alignment: Alignment.centerRight,
                          child: SizedBox(
                            width: screenWidth <= 820 ? screenWidth : 200,
                            child: PrimaryButton(
                              text: 'Submit for verification',
                              onTap: () async {
                                if (_formKey.currentState?.validate() ??
                                    false) {
                                  if (_imageUrl == null) {
                           CustomSnackBar.showError(
                                      "Error",
                                      "Please upload the document.",
                                    );
                                    return;
                                  }
                                  userID = storage.read("USER_ID") ?? "0";
                                  Map<String, dynamic> payload = {
                                    "business_id": int.parse(userID),
                                    "business_name":
                                        businessNameController.text,
                                    "business_address":
                                        registeredAddressController.text,
                                    "document_type": selectedDocumentType,
                                    "id_type": selectedPersonalidType,
                                    "document_number":
                                        documentNumberController.text,
                                    "document_url": _imageUrl ?? ""
                                  };
                                  bool isSuccess =
                                      await kycController.kycSubmitApi(payload);
                                  if (isSuccess) {
                           CustomSnackBar.showInfo(
                                      "Success",
                                      "KYC submission successful",
                                    );

                                    locator<NavigationServices>().goBack();
                                    _resetUploadState();
                                  } else {
                           CustomSnackBar.showError(
                                      "Failed",
                                      "KYC submission failed. Please try again.",
                                    );
                                  }
                                }
                              },
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _uploadFileHelper(
      PlatformFile pickedFile, File? file, Uint8List fileBytes) async {
    String fileExtension = pickedFile.extension?.toLowerCase() ?? '';
    List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];

    if (!allowedExtensions.contains(fileExtension)) {
                           CustomSnackBar.showError(
        "Error",
        "Please upload a valid image file (jpg, jpeg, png, pdf).",
      
      );
      return;
    }

    if (pickedFile.size > 2 * 1024 * 1024) {
                           CustomSnackBar.showError(
        "Error",
        "File size exceeds 2 MB limit.",
      );
      return;
    }

    String contentType =
        fileExtension == 'pdf' ? 'application/pdf' : 'image/$fileExtension';
    String filePath = file?.path ?? '';
    log("Starting _uploadFileHelper with fileName: ${pickedFile.name}, filePath: $filePath");
    bool value = await eventController.createFileNameEntry(
        pickedFile.name, contentType, filePath, fileBytes, "kyc");
    if (value) {
      String encodedFileName = Uri.encodeComponent(pickedFile.name);
      String newImageUrl =
          "https://profilemedia.s3.ap-south-1.amazonaws.com/$encodedFileName";
      setState(() {
        eventController.uploadedFileName.value = pickedFile.name;
        _imageUrl = newImageUrl;
        // _imageUrl = eventController.uploadedFileName.value;
      });
      if (kDebugMode) {
        log("_imageUrl set to: $newImageUrl");
      }
                           CustomSnackBar.showInfo(
     
        "Succes",
        "File uploaded successfully",
      
      );
    } else {
                           CustomSnackBar.showError(
     
        "Failed",
        "Unable to upload file..try after sometime.",
     
      );
    }
  }
}
