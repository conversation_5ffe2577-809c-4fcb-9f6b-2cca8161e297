import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:linkfy_text/linkfy_text.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:parenthing_dashboard/res/app_color.dart';
import 'package:parenthing_dashboard/res/custom_snackbar.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpPage extends StatefulWidget {
  const HelpPage({super.key});

  @override
  State<HelpPage> createState() => _HelpPageState();
}

class _HelpPageState extends State<HelpPage> {
  Map<int, bool> expandedStates = {};
  String appVersion = '';

  final List<FAQItem> faqItems = [
    // FAQItem(
    //   question: "How to get started with using a dashboard effectively?",
    //   answer:
    //       "To get started with using a dashboard effectively, first familiarize yourself with its layout and features. Identify the key metrics that are most relevant to your goals. Spend some time customizing the dashboard to display the information you need at a glance. Regularly review the data to track your progress and make informed decisions. Lastly, don't hesitate to explore tutorials or help resources to maximize your understanding and usage.",
    //   isExpanded: true,
    // ),
    // FAQItem(
    //   question:
    //       "What is the typical format of workshops, and how can they be tailored to fit my needs?",
    //   answer:
    //       "Workshops typically follow a structured format with learning objectives, interactive activities, and practical exercises. They can be tailored to your specific needs by customizing content, duration, and delivery methods.",
    // ),
    // FAQItem(
    //   question:
    //       "What measures are in place to keep my data safe on the dashboard?",
    //   answer:
    //       "We implement multiple security layers including encryption, secure authentication, regular security audits, and compliance with data protection regulations to ensure your data remains safe and secure.",
    // ),
    // FAQItem(
    //   question:
    //       "What should I do if I encounter technical problems while using the dashboard?",
    //   answer:
    //       "If you encounter technical problems, first try refreshing the page or clearing your browser cache. Check your internet connection and try accessing from a different browser. If issues persist, contact our technical support team for assistance.",
    // ),
    // FAQItem(
    //   question:
    //       "How can I earn rewards or recognition for engaging in workshops or activities?",
    //   answer:
    //       "You can earn rewards through active participation, completing assignments, providing feedback, and achieving learning milestones. Recognition may come in the form of certificates, badges, or special acknowledgments.",
    // ),
    // FAQItem(
    //   question:
    //       "What should I do if I forget my password or need to change my account details?",
    //   answer:
    //       "Use the 'Forgot Password' link on the login page to reset your password. For account details changes, log into your account and navigate to the settings or profile section to update your information.",
    // ),
    // FAQItem(
    //   question:
    //       "How will I be notified about upcoming workshops, updates, or events?",
    //   answer:
    //       "You'll receive notifications through email, in-app notifications, and SMS alerts (if enabled). You can customize your notification preferences in your account settings.",
    // ),
    // FAQItem(
    //   question:
    //       "Are there any resources or materials I can download for offline use?",
    //   answer:
    //       "Yes, we provide downloadable resources including guides, worksheets, video content, and reference materials that you can access offline for your convenience.",
    // ),
    FAQItem(
      question: "I run classes for kids, how can I get listed on Parenthing?",
      answer:
          "Create your account on https://www.parenthingapp.com/business . You can start listing your classes and events right away.",
    ),
    FAQItem(
      question:
          "I conduct events for kids, how can I get listed on Parenthing?",
      answer:
          "Create your account on https://www.parenthingapp.com/business . You can start listing your classes and events right away.",
    ),
    FAQItem(
      question: "How to create a new class?",
      answer:
          "Login to your business account. Click on Classes from the navigation on the left. Click on the Create class button on the top right of the My Classes page that opens up.",
    ),
    FAQItem(
      question: "How to create a new event?",
      answer:
          "Login to your business account. Click on Events from the navigation on the left. Click on the Create event button on the top right of the My Events page that opens up.",
    ),
    FAQItem(
      question: "Why was my class suspended?",
      answer:
          "At Parenthing we take the safety of our users (parents and their kids) very seriously. If multiple parents report your class for erroneous details (for instance, an incorrect phone number or incorrect services) or for some other reason (including inappropriate behaviour, scam or spam), your class will be automatically unpublished.",
    ),
    FAQItem(
      question: "How long do classes and events stay in review?",
      answer:
          "We attempt to review all new classes and events within a few business hours. However, if you do not receive feedback on your class or event within two business working days, please contact us on whatsapp.",
    ),
    FAQItem(
      question: "What happens if I delete an event or class?",
      answer:
          "If you delete an event or class, it will be removed from all listings on Parenthing and parents will no longer be able to find them on our apps. However, you are solely responsible for informing parents who have booked your class or event about any cancellations or closures.",
    ),
    FAQItem(
      question: "How can I take booking requests directly from Parenthing?",
      answer:
          "At the moment, the platform redirects booking requests to your whatsapp number or to any booking link you share. In the near future, we will allow parents to book tickets for your events via Parenthing directly.",
    ),
  ];

  final List<VideoItem> videoItems = [
    VideoItem(
      title: "How to get started with using a dashboard effectively?",
      thumbnail: "assets/images/video1.jpg",
      gradientColors: [Colors.orange.shade200, Colors.pink.shade200],
    ),
    VideoItem(
      title: "How to get started with using a dashboard effectively?",
      thumbnail: "assets/images/video2.jpg",
      gradientColors: [Colors.purple.shade200, Colors.blue.shade200],
    ),
    VideoItem(
      title: "How to get started with using a dashboard effectively?",
      thumbnail: "assets/images/video3.jpg",
      gradientColors: [Colors.grey.shade400, Colors.grey.shade600],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      appVersion = 'version ${packageInfo.version}+${packageInfo.buildNumber}';
    });
  }

  Future<void> _launchWhatsApp() async {
    const String phoneNumber = "8530572636";
    const String message = "Hi, I have a question";
    final Uri whatsappUri = Uri.parse(
        "https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}");

    if (await canLaunchUrl(whatsappUri)) {
      await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        CustomSnackBar.showError("Error", 'WhatsApp not installed');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: AppColors.kwhite,
      // appBar: AppBar(
      //   backgroundColor: Colors.white,
      //   elevation: 0,
      //   leading: IconButton(
      //     icon: const Icon(Icons.arrow_back, color: Colors.black),
      //     onPressed: () => Navigator.of(context).pop(),
      //   ),
      //   title: const Text(
      //     'FAQ',
      //     style: TextStyle(
      //       color: Colors.black,
      //       fontSize: 20,
      //       fontWeight: FontWeight.w600,
      //     ),
      //   ),
      //   actions: [
      // Container(
      //   margin: const EdgeInsets.only(right: 16),
      //   child: ElevatedButton.icon(
      //     onPressed: _launchWhatsApp,
      //     icon: const Icon(Icons.message, size: 16),
      //     label: const Text('Send a message'),
      //     style: ElevatedButton.styleFrom(
      //       backgroundColor: Colors.blue.shade600,
      //       foregroundColor: Colors.white,
      //       elevation: 0,
      //       padding:
      //           const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      //       shape: RoundedRectangleBorder(
      //         borderRadius: BorderRadius.circular(20),
      //       ),
      //     ),
      //   ),
      // ),
      //   ],
      // ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          width: screenWidth <= 820 ? Get.width : Get.width * .7,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "FAQ",
                    style: GoogleFonts.rubik(
                        fontWeight: FontWeight.w600,
                        color: AppColors.txtprimary,
                        fontSize: 24),
                  ),
                  Container(
                    height: 48,
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(
                          Radius.circular(12),
                        ),
                        border: Border.all(color: AppColors.bordergrey)),
                    child: ElevatedButton.icon(
                      onPressed: _launchWhatsApp,
                      icon: SvgPicture.asset("assets/icons/chat_icon.svg"),
                      label: Text(
                        'Send a Whatsapp Message',
                        style: GoogleFonts.rubik(
                            fontWeight: FontWeight.w700,
                            color: AppColors.kprimarycolor,
                            fontSize: 14),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.kwhite,
                        splashFactory: NoSplash.splashFactory,
                        shadowColor: Colors.transparent,
                        surfaceTintColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(25),
              Container(
                // margin: const EdgeInsets.all(16),
                // padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(
                    color: AppColors.lgrey,
                  ),
                ),
                child: Column(
                  children: faqItems.asMap().entries.map((entry) {
                    int index = entry.key;
                    FAQItem item = entry.value;
                    bool isExpanded = expandedStates[index] ?? false;
                    bool isLastItem = index == faqItems.length - 1;
                    return Container(
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: isLastItem
                                ? Colors.transparent
                                : Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Theme(
                        data: Theme.of(context).copyWith(
                          dividerColor: Colors.transparent,
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          expansionTileTheme: const ExpansionTileThemeData(
                            backgroundColor: Colors.transparent,
                            collapsedBackgroundColor: Colors.transparent,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: ExpansionTile(
                            tilePadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            childrenPadding: const EdgeInsets.only(
                                left: 16, right: 16, bottom: 16),
                            initiallyExpanded: isExpanded,
                            onExpansionChanged: (expanded) {
                              setState(() {
                                // Reset all to false
                                expandedStates = {};
                                // Expand only the currently tapped one
                                if (expanded) {
                                  expandedStates[index] = true;
                                }
                              });
                            },
                            title: Text(item.question,
                                style: GoogleFonts.rubik(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.txtprimary)),
                            trailing: Icon(
                              isExpanded
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              color: AppColors.secondary,
                            ),
                            children: [
                              Align(
                                alignment: Alignment.centerLeft,
                                child: LinkifyText(
                                  item.answer,
                                  textStyle: GoogleFonts.rubik(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.txtsecondary,
                                  ),
                                  linkStyle: const TextStyle(
                                    color: Colors.blue,
                                    // decoration: TextDecoration.underline,
                                  ),
                                  onTap: (link) async {
                                    final value = link.value ?? '';
                                    log(value);
                                    final uri = Uri.parse(
                                        value.startsWith('http')
                                            ? value
                                            : 'https://$value');
                                    if (await canLaunchUrl(uri)) {
                                      await launchUrl(uri,
                                          mode: LaunchMode.externalApplication);
                                    } else {
                                      debugPrint(
                                          'Could not launch ${uri.toString()}');
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),

              const SizedBox(height: 32),
              Center(
                child: Text(appVersion,
                    style: GoogleFonts.rubik(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.txtprimary)),
              )
              // // Self help videos section
              // const Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 16),
              //   child: Text(
              //     'Self help videos',
              //     style: TextStyle(
              //       fontSize: 24,
              //       fontWeight: FontWeight.w600,
              //       color: Colors.black,
              //     ),
              //   ),
              // ),

              // const SizedBox(height: 16),

              // SizedBox(
              //   height: 200,
              //   child: ListView.builder(
              //     scrollDirection: Axis.horizontal,
              //     padding: const EdgeInsets.symmetric(horizontal: 16),
              //     itemCount: videoItems.length,
              //     itemBuilder: (context, index) {
              //       VideoItem video = videoItems[index];
              //       return Container(
              //         width: 280,
              //         margin: const EdgeInsets.only(right: 16),
              //         child: Card(
              //           elevation: 4,
              //           shape: RoundedRectangleBorder(
              //             borderRadius: BorderRadius.circular(12),
              //           ),
              //           child: Column(
              //             crossAxisAlignment: CrossAxisAlignment.start,
              //             children: [
              //               // Video thumbnail
              //               Expanded(
              //                 flex: 3,
              //                 child: Container(
              //                   width: double.infinity,
              //                   decoration: BoxDecoration(
              //                     borderRadius: const BorderRadius.only(
              //                       topLeft: Radius.circular(12),
              //                       topRight: Radius.circular(12),
              //                     ),
              //                     gradient: LinearGradient(
              //                       colors: video.gradientColors,
              //                       begin: Alignment.topLeft,
              //                       end: Alignment.bottomRight,
              //                     ),
              //                   ),
              //                   child: Center(
              //                     child: Container(
              //                       width: 50,
              //                       height: 50,
              //                       decoration: BoxDecoration(
              //                         color: Colors.red,
              //                         borderRadius: BorderRadius.circular(8),
              //                       ),
              //                       child: const Icon(
              //                         Icons.play_arrow,
              //                         color: Colors.white,
              //                         size: 30,
              //                       ),
              //                     ),
              //                   ),
              //                 ),
              //               ),

              //               // Video title
              //               Expanded(
              //                 flex: 2,
              //                 child: Padding(
              //                   padding: const EdgeInsets.all(12),
              //                   child: Text(
              //                     video.title,
              //                     style: const TextStyle(
              //                       fontSize: 14,
              //                       fontWeight: FontWeight.w500,
              //                       color: Colors.black87,
              //                     ),
              //                     maxLines: 3,
              //                     overflow: TextOverflow.ellipsis,
              //                   ),
              //                 ),
              //               ),
              //             ],
              //           ),
              //         ),
              //       );
              //     },
              //   ),
              // ),

              // const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;
  final bool isExpanded;

  FAQItem({
    required this.question,
    required this.answer,
    this.isExpanded = false,
  });
}

class VideoItem {
  final String title;
  final String thumbnail;
  final List<Color> gradientColors;

  VideoItem({
    required this.title,
    required this.thumbnail,
    required this.gradientColors,
  });
}
