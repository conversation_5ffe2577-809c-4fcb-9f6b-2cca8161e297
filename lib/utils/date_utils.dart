import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

String getTodayDate() {
  String todayDate = "";
  var now = DateTime.now();
  var formatter = DateFormat('dd MMM yyyy');
  String formattedDate = formatter.format(now);
  todayDate = "Today, $formattedDate";
  return todayDate;
}

void logDateFormat(String timeStamp) {
  String finalTimeStamp = "${timeStamp}z";
  final DateTime docDateTime = DateTime.parse(finalTimeStamp);
  var formatter = DateFormat('dd MMM hh:mm a');
  final String formatted = formatter.format(docDateTime);
  if (kDebugMode) {
    print(formatted);
  }
}

String getOnlyDate(String timeStamp) {
  String finalTimeStamp = "${timeStamp}z";
  final DateTime docDateTime = DateTime.parse(finalTimeStamp);
  var formatter = DateFormat('dd MMM hh:mm a');
  final String formatted = formatter.format(docDateTime);
  return formatted;
}

String getDate(String dateString) {
  DateTime dateTime = DateTime.parse(dateString);
  DateFormat dateFormat = DateFormat("d MMMM yyyy");
  String formattedDate = dateFormat.format(dateTime);

  return formattedDate;
}

String getDateWithDayMonthName(String timeStamp) {
  final DateTime docDateTime = DateTime.parse(timeStamp);
  var formatter = DateFormat('dd MMM yyyy, hh:mm a');
  final String formatted = formatter.format(docDateTime);
  return formatted;
}

String dateWithMonthName(String timeStamp) {
  final DateTime docDateTime = DateTime.parse(timeStamp);
  final DateFormat formatter = DateFormat('dd-MMM-yyyy hh:mm a');
  final String formatted = formatter.format(docDateTime);
  return formatted;
}

String dateFormat(String timeStamp) {
  final DateTime docDateTime = DateTime.parse(timeStamp);
  final DateFormat formatter = DateFormat('dd MMM yyyy');
  final String formatted = formatter.format(docDateTime);
  return formatted;
}

// return 06:00PM
String convertTime(String timeData) {
  String staffTime = '';
  String timeStamp24HR = "2020-07-20T$timeData";
  staffTime = DateFormat.jm().format(DateTime.parse(timeStamp24HR));
  return staffTime;
}

String extractTime(String timestamp) {
  DateTime dateTime = DateTime.parse(timestamp);
  String formattedTime =
      DateFormat.jm().format(dateTime); // 'jm' formats as 'h:mm a'
  return formattedTime;
}

// return Feb 02,2023
String monthWithDay(String dateData) {
  String staffAttendanceDate = '';
  String timeStamp24HR = "20${dateData}T16:55:00";
  staffAttendanceDate =
      DateFormat.yMMMd().format(DateTime.parse(timeStamp24HR));
  return staffAttendanceDate;
}

String formatDuration(String durationString) {
  List<String> parts = durationString.split(':');
  int hours = int.parse(parts[0]);
  int minutes = int.parse(parts[1]);
  int seconds = int.parse(parts[2]);

  Duration duration =
      Duration(hours: hours, minutes: minutes, seconds: seconds);
  int totalMinutes = duration.inMinutes;
  int formattedHours = totalMinutes ~/ 60;
  int formattedMinutes = totalMinutes % 60;
  int formattedSeconds = duration.inSeconds % 60;

  return '${formattedHours}h, ${formattedMinutes}m, ${formattedSeconds}s';
}

String convertTimeFormat(String timeString) {
  if (timeString == "") {
    timeString = "00:00:00";
  }
  DateFormat format = DateFormat('H:mm:ss');
  DateTime dateTime = format.parse(timeString);
  DateFormat amPmFormat = DateFormat('h:mm a');
  String formattedTime = amPmFormat.format(dateTime);
  return formattedTime;
}

String formatMilliseconds(int milliseconds) {
  Duration duration = Duration(milliseconds: milliseconds);
  String twoDigits(int n) => n.toString().padLeft(2, '0');
  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  return "${duration.inHours}:$twoDigitMinutes:$twoDigitSeconds";
}

String formatDate({
  DateTime? date,
  required String format,
}) {
  DateTime now = date ?? DateTime.now();
  return DateFormat(format).format(now);
}

String formatDateWith5h30m({
  DateTime? date,
  required String format,
}) {
  DateTime now = date == null
      ? DateTime.now()
      : date.add(const Duration(hours: 5, minutes: 30));
  return DateFormat(format).format(now);
}

getTime(String timestampString) {
  DateTime timestamp = DateTime.parse(timestampString);
  String timePart = DateFormat.Hm().format(timestamp);
  return timePart;
}

String formatTime(String dateTimeString) {
  DateTime dateTime = DateTime.parse(dateTimeString);
  String time = DateFormat('h:mm a').format(dateTime);
  return time;
}

DateTime? tryParseDateTime(String? dateString) {
  if (dateString == null || dateString.isEmpty) return null;
  try {
    return DateTime.parse(dateString);
  } catch (e) {
    if (kDebugMode) {
      print('Error parsing date: $dateString');
    }
    return null;
  }
}

String formatDateTime(DateTime dateTime) {
  return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
}

DateTime getValidDateTime(String? dateString,
    {required DateTime defaultValue}) {
  return tryParseDateTime(dateString) ?? defaultValue;
}

String formatDuration2(int minutes) {
  final days = minutes ~/ 1440;
  final hours = (minutes % 1440) ~/ 60;
  final remainingMinutes = minutes % 60;

  final List<String> parts = [];

  if (days > 0) {
    parts.add('$days day${days != 1 ? 's' : ''}');
  }
  if (hours > 0) {
    parts.add('$hours hour${hours != 1 ? 's' : ''}');
  }
  if (remainingMinutes > 0) {
    parts.add('$remainingMinutes minute${remainingMinutes != 1 ? 's' : ''}');
  }

  return parts.join(', ');
}

String formatDateTimeStamp(String dateTimeStr) {
  // ignore: unnecessary_null_comparison
  if (dateTimeStr == null || dateTimeStr.isEmpty) {
    return '-';
  }
  try {
    final DateTime dateTime = DateTime.parse(dateTimeStr);
    return DateFormat('dd-MMM-yyyy hh:mma').format(dateTime);
  } catch (e) {
    log('Error parsing date: $e');
    return 'Invalid Date';
  }
}

String formatDateForDisplay(String dateString) {
  try {
    DateTime dateTime = DateTime.parse(dateString);
    DateTime now = DateTime.now();

    Duration difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    }
  } catch (e) {
    return dateString;
  }
}

double calculateDropdownHeight(int notificationCount) {
  if (notificationCount == 0) {
    return 200;
  }
  double headerHeight = 60;
  double itemHeight = 80;
  double calculatedHeight = headerHeight + (notificationCount * itemHeight);
  return calculatedHeight.clamp(200, 400);
}
