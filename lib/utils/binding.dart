import 'package:get/get.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_business_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_home_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_kyc_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_parents_controller.dart';
import 'package:parenthing_dashboard/admin_dashboard/controller/admin_user_controller.dart';
import 'package:parenthing_dashboard/controller/business_profile_controller.dart';
import 'package:parenthing_dashboard/controller/classes_controller.dart';
import 'package:parenthing_dashboard/controller/event_controller.dart';
import 'package:parenthing_dashboard/controller/kyc_controller.dart';
import 'package:parenthing_dashboard/controller/user_controller.dart';
import 'package:parenthing_dashboard/network/api_helper.dart';

class GlobalBindings extends Bindings {
  @override
  void dependencies() {
    Get.put(ApiController(), permanent: true);
    Get.put(BusinessController(), permanent: true);
    Get.put(UserController(), permanent: true);
    Get.put(AdminBusinessController(), permanent: true);
    Get.put(AdminUserController(), permanent: true);
    Get.put(HomePageController(), permanent: true);
    Get.put(EventController(), permanent: true);
    Get.put(ClassController(), permanent: true);
    Get.put(AdminParentsModelController(), permanent: true);
    Get.put(AdminKycController(), permanent: true);
    Get.put(KycController(), permanent: true);
  }
}
