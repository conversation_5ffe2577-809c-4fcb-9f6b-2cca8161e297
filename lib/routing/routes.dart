const String welcomeRoute = '/welcome_page';
const String loadingAnimationRoute = '/loading_animation_page';
const String loginRoute = '/login_page';
const String notificationsRoute = '/notifications';
const String homeRoute = '/home';
const String profileRoute = '/setting_page';
const String helpRoute = '/help_page';
const String rootRoute = '/root_page';
const String kycFormPage = "/kyc_form_page";
const String businessEditPage = '/businessEdit_page';
const String eventRoute = '/event_page';
const String eventDetailsRoute = '/event_details_page';
const String classesRoute = '/classes_page';
const String createclassRoute = '/create_class_page';
const String editclassRoute = '/edit_class_page';
const String classDetailsRoute = '/classes_details_page';
const String createclassPageRoute =
    '/create_classes_page'; //! need to remove after edit and create class in one page
const String createEventPage = '/eventCreate_page';
const String eventEditPage = '/eventEdit_page';
const String addAddressPage = '/addAddress_page';
const String businessNotificationsPage = '/business_page';
const String businessRegister = '/business_register';



//! admin page routes
const String adminHomeRoute = '/home_page';
const String adminBusinessRoute = '/business_page';
const String adminBusinessDetailsRoute = '/business_details_page';
const String adminRequestRoute = '/request_page';
const String adminProfileRoute = '/profile_page';
const String adminParentsRoute = '/parents_page';
const String adminUsersRoute = '/users_page';
const String adminAnalyticsRoute = '/analytics_page';
const String adminCreateEventDetailsPage = '/admin_create_event_detail_page';
const String adminEventDetailsPage = '/admin_event_detail_page';
const String adminClassDetailsPage = '/admin_class_detail_page';
const String adminParentsDetails = '/parents_details_page';
const String adminReportEventDetails = '/admin_report_event_details';
const String adminReportClassDetailsPage = '/admin_report_class_detail_page';
const String businessOnBoardForm = '/business_onboard_form';
