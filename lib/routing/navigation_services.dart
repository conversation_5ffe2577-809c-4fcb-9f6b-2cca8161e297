import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parenthing_dashboard/routing/routes.dart';

class NavigationServices {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  Future<dynamic> navigateTo(String routeName, {dynamic arguments}) async {
    try {
      if (routeName == editclassRoute) {
        // Pass arguments only when editing
        return await navigatorKey.currentState!.pushNamed(
          routeName,
          arguments: arguments,
        );
      } else {
        final result = await navigatorKey.currentState!.pushNamed(
          routeName,
          arguments: arguments,
        );
        return result;
      }
    } catch (error) {
      if (kDebugMode) {
        print('Navigation error: $error');
      }

      return null;
    }
  }

  void goBack() {
    return navigatorKey.currentState!.pop();
  }

  void goBackWithResult(dynamic result) {
    return navigatorKey.currentState!.pop(result);
  }

  void navigateToReplacement(Widget page) {
    Navigator.pushReplacement(
      Get.context!,
      MaterialPageRoute(builder: (context) => page),
    );
  }
}
