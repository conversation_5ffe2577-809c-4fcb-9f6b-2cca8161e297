# Nginx configuration for Flutter web with path-based routing
# Place this configuration in your server block

server {
    listen 80;
    server_name your-domain.com;
    
    # Root directory where your Flutter web build files are located
    root /path/to/your/flutter/web/build;
    index index.html;

    # Handle all routes by serving index.html
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Optional: Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Optional: Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
